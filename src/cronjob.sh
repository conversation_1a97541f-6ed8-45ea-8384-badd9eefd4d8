#!/bin/bash

#Python's PATH must be similar /home/<USER>/aoc-aldgram-online-certification-be/src/env/bin/python
#OR /home/<USER>/aoc-aldgram-online-certification-be/env/bin/python

#Manage.py's PATH must be similar /home/<USER>/aoc-aldgram-online-certification-be/src/manage.py
PATH_ENV="$(pwd)/env/bin/activate"
echo $PATH_ENV
CWD_PYTHON_1="cd $(pwd) && source env/bin/activate && python manage.py remind_change_password"
CWD_PYTHON_2="cd $(pwd) && source env/bin/activate && python manage.py remove_notification"
CWD_PYTHON_3="cd $(pwd) && source env/bin/activate && python manage.py  get_refresh_token_kanna_default"
CWD_PYTHON_4="cd $(pwd) && source env/bin/activate && python manage.py sync_users_kanna"
CWD_PYTHON_5="cd $(pwd) && source env/bin/activate && python manage.py upload_logs_to_s3"
CWD_SYNC_LOG="$(pwd)/logs/sync_user_kanna.log"

CWD_LOG="$(pwd)/logs/cronjob.log"

TIME_AUTO_GET_NEW_REFRESH_TOKEN=""
source .env
#run day 1 of month
if [ -z "$TIME_AUTO_GET_NEW_REFRESH_TOKEN" ];
then
    echo "TIME_AUTO_GET_NEW_REFRESH_TOKEN is not set, set default value"
    TIME_AUTO_GET_NEW_REFRESH_TOKEN="0 0 1 * *"
fi

#run every days 9 am

TIME_RUN_CRONJOB_SYNC_USER_KANNA=""
source .env
if [ -z "$TIME_RUN_CRONJOB_SYNC_USER_KANNA" ];
then
    echo "TIME_RUN_CRONJOB_SYNC_USER_KANNA is not set, set default value"
    TIME_RUN_CRONJOB_SYNC_USER_KANNA="0 4 * * *"
fi

TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3=""
source .env
if [ -z "$TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3" ];
then
    echo "TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3 is not set, set default value"
    TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3="0 0 * * *"
fi

if [[ -f "$PATH_ENV" ]]
then
    cronjob_1="0 9 * * * $CWD_PYTHON_1 >> $CWD_LOG"
    cronjob_2="0 * * * * $CWD_PYTHON_2 >> $CWD_LOG"
    cronjob_3="$TIME_AUTO_GET_NEW_REFRESH_TOKEN $CWD_PYTHON_3 >> $CWD_LOG"
    cronjob_4="$TIME_RUN_CRONJOB_SYNC_USER_KANNA $CWD_PYTHON_4 >> $CWD_SYNC_LOG"
    cronjob_5="$TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3 $CWD_PYTHON_5 >> $CWD_LOG"
    ( crontab -l | grep -v -F "$CWD_PYTHON_1" ; echo "$cronjob_1") | crontab -
    ( crontab -l | grep -v -F "$CWD_PYTHON_2" ; echo "$cronjob_2") | crontab -
    ( crontab -l | grep -v -F "$CWD_PYTHON_3" ; echo "$cronjob_3") | crontab -
    ( crontab -l | grep -v -F "$CWD_PYTHON_4" ; echo "$cronjob_4") | crontab -
    ( crontab -l | grep -v -F "$CWD_PYTHON_5" ; echo "$cronjob_5") | crontab -
else
    echo "Create Cronjob failed, please check your python's path again!"
fi
