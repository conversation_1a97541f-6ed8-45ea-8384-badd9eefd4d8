from rest_framework import viewsets

from utilities.permissions.custom_permission import HasKannaPermission

from .models import JapaneseCalendar
from .serializers import JapaneseCalendarSerializer


class JapanCalendarViewSet(viewsets.ModelViewSet):
    serializer_class = JapaneseCalendarSerializer
    http_method_names = ["get"]
    queryset = JapaneseCalendar.objects.all()
    permission_classes = [HasKannaPermission]
