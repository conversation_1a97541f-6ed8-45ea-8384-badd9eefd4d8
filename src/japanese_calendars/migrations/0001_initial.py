# Generated by Django 4.1 on 2022-09-30 06:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="JapaneseCalendar",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=25)),
                ("date_from", models.DateField(blank=True, null=True)),
                ("date_to", models.DateField(blank=True, null=True)),
            ],
            options={
                "db_table": "japanese_calendar",
                "ordering": ["id"],
            },
        ),
    ]
