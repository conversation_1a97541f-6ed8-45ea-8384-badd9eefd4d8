# Generated by Django 4.1 on 2022-09-20 09:11

from django.db import migrations
from django.core.management import call_command


def unload_fixture(apps, schema_editor):
    "Brutally deleting all entries for this model..."

    MyModel = apps.get_model("japanese_calendars", "JapaneseCalendar")
    MyModel.objects.all().delete()


def load_fixture(apps, schema_editor):
    call_command("loaddata", "fixtures/01-master/04-japanese-calendar.json")


class Migration(migrations.Migration):
    dependencies = [
        ("japanese_calendars", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(load_fixture, reverse_code=unload_fixture),
    ]
