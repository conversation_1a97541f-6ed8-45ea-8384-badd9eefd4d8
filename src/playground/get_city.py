import requests
import json
import re

def get_city_from_zipcode(zipcode):
    """
    Get city information from a Japanese zipcode
    
    Args:
        zipcode (str): Japanese postal code (7 digits)
        
    Returns:
        dict: Dictionary containing prefecture, city, and town information
    """
    # Ensure zipcode is properly formatted (remove hyphens if present)
    zipcode = zipcode.replace("-", "")
    
    # Get data from yubinbango API
    url = f"https://yubinbango.github.io/yubinbango-data/data/{zipcode[:3]}.js"
    response = requests.get(url)
    
    if response.status_code != 200:
        return {"error": f"Failed to retrieve data for zipcode: {zipcode}"}
    
    # Parse JSON-like content (the API returns JavaScript, not pure JSON)
    data = response.text
    print(response.text)
    match = re.search(r'\$yubin\((\{.*\})\);?', data, re.DOTALL)
    
    if not match:
        return {"error": f"Could not parse response for zipcode: {zipcode}"}
    
    zipcode_map = json.loads(match.group(1))
    address_data = zipcode_map.get(zipcode)
    
    if not address_data:
        return {"error": f"No data found for zipcode: {zipcode}"}
    
    # The address data is an array with prefecture, city, town information
    # Format: [prefecture_id, prefecture_name, city_name, town_name]
    return {
        "prefecture_id": address_data[0],
        "prefecture": address_data[1],
        "city": address_data[2],
    }

# Example usage
zipcode = "1420061"
city_info = get_city_from_zipcode(zipcode)
print(city_info)

# # You can also use it with a hyphenated zipcode
# zipcode_with_hyphen = "142-0061"
# city_info = get_city_from_zipcode(zipcode_with_hyphen)
# print(city_info)
