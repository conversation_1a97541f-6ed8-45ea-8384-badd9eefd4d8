from rest_framework import viewsets

from django.db.models import Q

from kanna_api.models.role import Role

from .serializers import RoleSerializer


class RoleViewSet(viewsets.ModelViewSet):
    serializer_class = RoleSerializer
    http_method_names = ["get", "post", "put", "delete"]

    def get_queryset(self):
        queryset = Role.objects.all()
        name = self.request.query_params.get("name", None)
        if name is not None:
            queryset = queryset.filter(Q(name__icontains=name))
        return queryset
