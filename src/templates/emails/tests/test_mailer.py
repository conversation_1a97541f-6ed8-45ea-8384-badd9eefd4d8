from django.test import TestCase
from django.core import mail
from unittest.mock import patch
from templates.emails.mailer import (
    send_mail,
    X_SES_CONFIGURATION_SET,
    X_SES_MESSAGE_TAGS,
)


class TestMailer(TestCase):
    def setUp(self):
        self.subject = "Test Subject"
        self.template = "emails/templates/test_template.html"
        self.emails = ["<EMAIL>"]
        self.merge_data = {"key": "value"}

    @patch("templates.emails.mailer.render_to_string")
    def test_send_mail_with_headers(self, mock_render):
        mock_render.return_value = "<html>Test</html>"
        send_mail(self.subject, self.template, self.emails, self.merge_data)
        self.assertEqual(len(mail.outbox), 1)
        sent_email = mail.outbox[0]
        self.assertEqual(sent_email.subject, self.subject)
        self.assertEqual(sent_email.to, self.emails)
        self.assertEqual(sent_email.alternatives[0][0], "<html>Test</html>")
        self.assertEqual(sent_email.alternatives[0][1], "text/html")
        self.assertEqual(
            sent_email.extra_headers["X-SES-CONFIGURATION-SET"], X_SES_CONFIGURATION_SET
        )
        self.assertEqual(
            sent_email.extra_headers["X-SES-MESSAGE-TAGS"], X_SES_MESSAGE_TAGS
        )

    @patch("templates.emails.mailer.render_to_string")
    def test_send_mail_with_multiple_recipients(self, mock_render):
        mock_render.return_value = "<html>Test</html>"
        multiple_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]
        send_mail(self.subject, self.template, multiple_emails, self.merge_data)
        self.assertEqual(len(mail.outbox), 1)
        sent_email = mail.outbox[0]
        self.assertEqual(set(sent_email.to), set(multiple_emails))
        self.assertEqual(
            sent_email.extra_headers["X-SES-CONFIGURATION-SET"], X_SES_CONFIGURATION_SET
        )
        self.assertEqual(
            sent_email.extra_headers["X-SES-MESSAGE-TAGS"], X_SES_MESSAGE_TAGS
        )
