from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string

from api import constants
from api.constants import RequestType, ApproveFlowAction
from authentication.models import User
from companies.models import Company
from kanna_api.models import UserKanna
from utilities import email_helper

X_SES_CONFIGURATION_SET = "ses-email-event-config"
X_SES_MESSAGE_TAGS = f"from-service=agc-{settings.APP_ENV.lower()}"


def send_opt_email(
    username,
    email,
):
    register_link = ""
    email_body = (
        "Hi " + username + " Use the link below to verify your email \n" + register_link
    )
    data = {"email_body": email_body, "to_email": email, "email_subject": "OTP email"}

    email_helper.send(data)
    return True


def send_mail(subject, template, emails, merge_data={}):
    html_body = render_to_string(template, merge_data)
    email_batches = _create_email_batches(emails)
    for batch in email_batches:
        msg = EmailMultiAlternatives(
            subject=subject, from_email=settings.ADMIN_EMAIL, to=batch, body="",
            headers={
                "X-SES-CONFIGURATION-SET": X_SES_CONFIGURATION_SET,
                "X-SES-MESSAGE-TAGS": X_SES_MESSAGE_TAGS
            }
        )
        msg.attach_alternative(html_body, "text/html")
        msg.send()


def send_mail_with_cc(subject, template, emails, emails_cc, merge_data={}):
    html_body = render_to_string(template, merge_data)

    msg = EmailMultiAlternatives(
        subject=subject,
        from_email=settings.ADMIN_EMAIL,
        to=emails,
        body="",
        cc=emails_cc,
    )
    msg.attach_alternative(html_body, "text/html")
    msg.send()


def send_approve_email(user, company, password):
    merge_data = {
        "fullname": user.get_full_name(),
        "company_name": company.name,
        "password": password,
        "email": user.email,
        "link": settings.WEBSITE_URL + "login",
    }
    subject = "本会員登録通知"
    send_mail(subject, "emails/templates/approved_email.html", [user.email], merge_data)
    return True


def send_remind_change_password_email(user):
    subject = "パスワードの再設定が必要"
    merge_data = {
        "link": settings.WEBSITE_URL + "login",
        "full_name": user.get_full_name(),
        "email": user.email,
    }
    send_mail(
        subject,
        "emails/templates/remind_owner_change_password.html",
        [user.email],
        merge_data,
    )
    return True


def send_register_owner_by_shop_email(user, company, password):
    subject = "本会員登録通知"

    merge_data = {
        "fullname": user.get_full_name(),
        "company_name": company.name,
        "password": password,
        "email": user.email,
        "link": settings.WEBSITE_URL + "login",
    }
    send_mail(
        subject,
        "emails/templates/register_owner_by_shop_email.html",
        [user.email],
        merge_data,
    )
    return True


def send_password_reset_email(user, current_site, relative_link, redirect_url):
    subject = "[AOC] パスワードのリセット"
    absurl = "http://" + current_site + relative_link
    link = absurl + "?redirect_url=" + redirect_url
    merge_data = {
        "absurl": absurl,
        "redirect_url": redirect_url,
        "link": link,
        "fullname": user.get_full_name(),
    }
    send_mail(
        subject,
        "emails/templates/password_reset_confirm.html",
        [user.email],
        merge_data,
    )
    return True


def send_verify_code_email(user : User, code: str):
    subject = "認証コードをご確認ください"

    merge_data = {
        "code": code,
        "fullname": user.get_full_name(),
    }
    send_mail(
        subject, "emails/templates/email_verify_code.html", [user.email], merge_data
    )
    return True


def send_request_create_owner_email(request_user):
    subject = "アカウント申請が完了しました。"
    merge_data = {
        "full_name": request_user.get_full_name(),
    }
    send_mail(
        subject,
        "emails/templates/request_create_owner_email.html",
        [request_user.email],
        merge_data,
    )
    return True


def send_reject_request_owner_email(request_user):
    subject = "Your request account is reject"
    merge_data = {
        "full_name": request_user.get_full_name(),
    }
    send_mail(
        subject,
        "emails/templates/reject_request_owner_email.html",
        [request_user.email],
        merge_data,
    )
    return True


# SEND mail approve flow status for sds.
def request_approved_approve_flow_sds(
        company_name: str,
        user_dealer: list,
        user_request: UserKanna,
        comment: str,
        sds_type: str,
        cms_title: str
) -> bool:
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_request.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.REQUEST.value
    )
    merge_data = {
        "name": company_name,
        "name_request": user_request.name,
        "document": "診断書",
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
        "comment": comment,
        "cms_title": cms_title,
    }
    send_mail(
        subject,
        "emails/templates/request_approve_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


def send_approved_approve_flow_sds(
        company_to: Company,
        user_approved: UserKanna,
        user_to: list,
        comment: str,
        sds_type: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_approved.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.APPROVE.value
    )
    merge_data = {
        "name": company_to.name,
        "name_approved": user_approved.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/approved_flow_email.html",
        user_to,
        merge_data,
    )
    return True


def send_returned_approve_flow_sds_for_shop(
        user: UserKanna,
        user_return: UserKanna,
        comment: str,
        sds_type: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_return.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.RETURN.value
    )
    merge_data = {
        "name": user.name,
        "name_return": user_return.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/return_flow_email.html",
        [user.email],
        merge_data,
    )
    return True


def send_returned_approve_flow_sds_for_dealer(
        company_dealer: Company,
        user_return: UserKanna,
        user_dealer: list,
        comment: str,
        sds_type: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_return.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.RETURN.value
    )
    merge_data = {
        "name": company_dealer.name,
        "name_return": user_return.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/return_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


def send_rejected_approve_flow_sds_for_shop(
        user: UserKanna,
        user_reject: UserKanna,
        comment: str,
        cms_title: str,
        sds_type: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_reject.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.REJECT.value
    )
    merge_data = {
        "name": user.name,
        "name_reject": user_reject.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/reject_flow_email.html",
        [user.email],
        merge_data,
    )
    return True


def send_rejected_approve_flow_sds_for_dealer(
        company_dealer: Company,
        user_reject: UserKanna,
        user_dealer: list,
        comment: str,
        sds_type: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_reject.name,
        request_type=RequestType[sds_type].value,
        action=ApproveFlowAction.REJECT.value
    )
    merge_data = {
        "name": company_dealer.name,
        "name_reject": user_reject.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_DIAGNOSTIC_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/reject_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


# SEND mail approve flow status for ms.
def send_approved_approve_flow_ms(
        company_to: Company,
        user_approved: UserKanna,
        user_to: list,
        comment: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_approved.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.APPROVE.value
    )
    merge_data = {
        "name": company_to.name,
        "name_approved": user_approved.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/approved_flow_email.html",
        user_to,
        merge_data,
    )
    return True


def send_returned_approve_flow_ms_for_shop(
        user: UserKanna,
        user_return: UserKanna,
        comment: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_return.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.RETURN.value
    )
    merge_data = {
        "name": user.name,
        "name_return": user_return.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/return_flow_email.html",
        [user.email],
        merge_data,
    )
    return True


def send_returned_approve_flow_ms_for_dealer(
        company_dealer: Company,
        user_return: UserKanna,
        user_dealer: list,
        comment: str,
        cms_title: str
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_return.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.RETURN.value
    )
    merge_data = {
        "name": company_dealer.name,
        "name_return": user_return.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/return_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


def send_rejected_approve_flow_ms_for_shop(
        user: UserKanna,
        user_reject: UserKanna,
        comment: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_reject.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.REJECT.value
    )
    merge_data = {
        "name": user.name,
        "name_reject": user_reject.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/reject_flow_email.html",
        [user.email],
        merge_data,
    )
    return True


def send_rejected_approve_flow_ms_for_dealer(
        company_dealer: Company,
        user_reject: UserKanna,
        user_dealer: list,
        comment: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_reject.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.REJECT.value
    )
    merge_data = {
        "name": company_dealer.name,
        "name_reject": user_reject.name,
        "comment": comment,
        "cms_title": cms_title,
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
    }
    send_mail(
        subject,
        "emails/templates/reject_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


def request_approved_approve_flow_ms(
        company_name: str,
        user_dealer: list,
        user_request: UserKanna,
        comment: str,
        cms_title: str,
):
    subject = _build_handle_approve_flow_mail_subject(
        request_user_name=user_request.name,
        request_type=RequestType.MANAGEMENT_SHEET.value,
        action=ApproveFlowAction.REQUEST.value
    )
    merge_data = {
        "name": company_name,
        "name_request": user_request.name,
        "document": "管理シート",
        "link": constants.FE_MANAGEMENT_SHEET_LIST_URL,
        "comment": comment,
        "cms_title": cms_title
    }
    send_mail(
        subject,
        "emails/templates/request_approve_flow_email.html",
        user_dealer,
        merge_data,
    )
    return True


def send_mail_release_warranty(owner, name_release):
    subject = "承認依頼通知"
    merge_data = {
        "owner": owner.first_name + owner.last_name,
        "name_release": name_release,
        "link": settings.WEBSITE_URL,
    }
    send_mail(
        subject,
        "emails/templates/release_warranty.html",
        [owner.email],
        merge_data,
    )
    return True


def _build_handle_approve_flow_mail_subject(
        request_user_name: str,
        request_type: str,
        action: str
) -> str:
    return f"【{action}通知】{request_type}{action} {request_user_name}"


def _create_email_batches(emails: list) -> list:
    batch_size = 50

    email_batches = []
    curr_batch = []
    for email in emails:
        curr_batch.append(email)
        if len(curr_batch) == batch_size:
            email_batches.append(curr_batch)
            curr_batch = []

    if curr_batch:
        email_batches.append(curr_batch)

    return email_batches
