import json
from ast import List

from notifications.models import Notification
from rest_framework import serializers

from django.db.models import Q

from api import constants
from approve_flow.models import OwnerElectronicMark
from authentication.models import User
from cms.services import get_cms_kanna
from companies.models import Company
from companies.serializers import CompanySerializer
from kanna_api.models import UserKanna
from role_management.serializers import RoleSerializer
from utilities.exceptions.api_exception import UserConstuctionError
from warranty.models import Warranty

from .models import ActDealer, RequestOwner, UserConstruction


class UserCommonSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
        )


class UserLiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
        )


class UserSerializer(serializers.ModelSerializer):
    company = serializers.SerializerMethodField(read_only=True)
    full_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "company_id",
            "company",
            "status",
            "notes",
            "postcode",
            "address",
            "address_detail",
            "full_name",
            "prefecture",
            "phone",
            "created_at",
        )

    def get_company(self, obj):
        company = Company.objects.filter(pk=obj.company_id).first()
        if company:
            return CompanySerializer(company).data
        return None

    def get_full_name(self, obj):
        return obj.get_full_name()


class UserKannaSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    construction_name = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    company = CompanySerializer(read_only=True)
    company_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = UserKanna
        fields = (
            "id",
            "name",
            "email",
            "phone",
            "role",
            "role_id",
            "company_id",
            "company",
            "construction_name",
            "building_name",
            "tel",
            "address",
            "is_active",
            "pic_position",
            "level",
            "last_login_at",
        )
        ref_name = "User Kanna"

    def get_phone(self, obj: UserKanna):
        phone = obj.tel or (obj.user.phone if obj.user else None)
        if phone:
            return phone
        return ""

    def get_construction_name(self, obj):
        # get name from contruction model
        return ""

    def get_building_name(self, obj):
        # get name from building model
        return ""


class UserKannaCommonSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserKanna
        fields = (
            "id",
            "name",
            "email",
            "tel",
            "address",
        )
        ref_name = "User Kanna"


class UserKannaUpdateSerializer(serializers.ModelSerializer):
    company = CompanySerializer(read_only=True)
    company_id = serializers.IntegerField(required=False, allow_null=True)
    email = serializers.CharField(required=False)
    name = serializers.CharField(required=False)
    phone = serializers.CharField(required=False)
    shop_ids = serializers.CharField(required=False, write_only=True, allow_null=True)

    class Meta:
        model = UserKanna
        fields = (
            "id",
            "name",
            "email",
            "phone",
            "company",
            "company_id",
            "pic_position",
            "level",
            "shop_ids",
            "is_active",
            "last_login_at",
        )

    def update(self, instance, validated_data):
        company = Company.objects.get(id=validated_data["company_id"])
        if company.type == "SHOP":
            instance.role_id = constants.KANNA_USER_ROLE["MAKEUP_SHOP"]
        if company.type == "DEALER":
            instance.role_id = constants.KANNA_USER_ROLE["DEALER"]
        if company.type == "ACT":
            instance.role_id = constants.KANNA_USER_ROLE["ACT"]

        if "phone" in validated_data and validated_data["phone"] and instance.user:
            phone = validated_data.pop("phone")
            user = instance.user

            user.phone = phone

            validated_data["tel"] = phone

            user.save()

        # When admin update user kanna infor then active this user
        instance.is_active = True
        return super().update(instance, validated_data)


class ActDealerSerializer(serializers.ModelSerializer):
    dealer = UserKannaSerializer(read_only=True)

    class Meta:
        model = ActDealer
        fields = ["id", "dealer_id", "dealer", "act_id"]
        ref_name = "User Kanna ACT Dealer"


class UserUpdateSerializer(UserSerializer):
    company_id = serializers.IntegerField(required=False, allow_null=True)
    email = serializers.EmailField()
    construction_uuids = serializers.CharField(
        required=False, write_only=True, allow_blank=True
    )

    class Meta:
        model = User
        fields = UserSerializer.Meta.fields + ("construction_uuids",)
        extra_kwargs = {}

    def update(self, instance, validated_data):
        construction_uuids = None
        arr_user_construction: List[UserConstruction] = []
        if "construction_uuids" in validated_data:
            construction_uuids = validated_data.pop("construction_uuids")

        if len(construction_uuids) == 0:
            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=instance.pk
            )
            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()
        else:
            construction_uuids = construction_uuids.split(",")
            for construction_uuid in construction_uuids:

                construction_uuid_used = UserConstruction.objects.filter(
                    ~Q(owner_id=instance.pk), construction_uuid=construction_uuid
                )

                if not construction_uuid_used:
                    cms = get_cms_kanna(
                        construction_uuid, self.context["request"].user.kanna_user
                    )
                    cms_title = cms.get("title")
                    item = UserConstruction(
                        **{
                            "owner_id": instance.pk,
                            "construction_uuid": construction_uuid,
                            "construction_name": cms_title,
                        }
                    )
                    arr_user_construction.append(item)
                else:
                    raise UserConstuctionError(
                        {
                            "construction_uuid": construction_uuid,
                            "detail": "construction was used",
                        },
                    )

            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=instance.pk
            )

            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()

            UserConstruction.objects.bulk_create(arr_user_construction)

        for key, value in validated_data.items():
            setattr(instance, key, value)
        instance.save()
        return instance


class RequestOwnerSerializer(serializers.ModelSerializer):
    company = serializers.SerializerMethodField(read_only=True)
    full_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = RequestOwner
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "full_name",
            "company_id",
            "company",
            "status",
            "postcode",
            "prefecture",
            "address",
            "address_detail",
            "notes",
            "phone",
            "created",
        ]

    def get_company(self, obj):
        company = Company.objects.filter(pk=obj.company_id).first()
        if company:
            return CompanySerializer(company).data
        return None

    def get_full_name(self, obj):
        return obj.get_full_name()


class UserConstructionSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserConstruction
        fields = ["id", "construction_uuid", "status"]

    def get_status(self, obj):
        status_wrranty = (
            Warranty.objects.values_list("status", flat=True)
            .filter(management_sheet__cms_uuid=obj.construction_uuid)
            .first()
        )
        return status_wrranty


class RequestOwnerUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestOwner
        fields = [
            "email",
            "first_name",
            "last_name",
            "company_id",
            "status",
            "address",
            "address_detail",
            "notes",
            "phone",
            "postcode",
            "prefecture",
        ]


class UserKannaInfomationSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    company = CompanySerializer(read_only=True)
    company_id = serializers.IntegerField(required=False, allow_null=True)
    name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserKanna
        fields = (
            "id",
            "name",
            "email",
            "role",
            "company_id",
            "company",
        )
        ref_name = "User Kanna"

    def get_name(self, obj):
        if obj.user:
            return obj.user.get_full_name()
        return obj.name


class UserKannaLiteSerializer(serializers.ModelSerializer):
    kanna_user = UserKannaInfomationSerializer(read_only=True)

    class Meta:
        model = User
        fields = ("kanna_user",)


class GenericNotificationRelatedField(serializers.RelatedField):
    def to_representation(self, value):
        try:
            target_type = value.__class__.__name__
        except Exception as e:
            print(e)
            target_type = ""
        data = {
            "id": value.pk,
            "document": target_type,
            "type": value.type,
        }
        return data


class NotificationSerializer(serializers.ModelSerializer):
    # actor = UserCommonSerializer(read_only=True)
    # action_object = GenericNotificationRelatedField(read_only=True)
    verb = serializers.CharField(read_only=True)
    target = GenericNotificationRelatedField(read_only=True)
    object_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Notification
        fields = ("id", "verb", "target", "unread", "object_data", "timestamp")

    def get_object_data(self, obj):
        try:
            noti_data = json.loads(obj.description.replace("'", '"'))
            noti_data["construction_name"] = ""
            noti_data["construction_no"] = ""
            if (
                "construction_uuid" in noti_data
                and obj.recipient.kanna_user is not None
            ):
                cms = get_cms_kanna(
                    noti_data["construction_uuid"], obj.recipient.kanna_user
                )
                noti_data["construction_name"] = cms["title"]
                if "construction_no" in cms:
                    noti_data["construction_no"] = cms["construction_no"]
            return noti_data
        except Exception as e:
            print(e)
        return ""


class OwnerElectronicMarkSerializers(serializers.ModelSerializer):
    class Meta:
        model = OwnerElectronicMark
        fields = ["image_base64"]
