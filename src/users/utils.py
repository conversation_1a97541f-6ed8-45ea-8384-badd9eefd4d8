from django.contrib.auth import authenticate
from django.urls import reverse

from authentication.models import Area, Prefectures, Shop, User


def setup_tests(client):
    try:
        email = "<EMAIL>"
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        area = Area.objects.create(name="Area")
        prefectures = Prefectures.objects.create(name="Prefectures", area_id=area.pk)
        shop = Shop.objects.create(name="Shop", prefectures_id=prefectures.id)
        user = User.objects.create_user(email, "testpass", shop.pk, True, True)
    user.username = "ngocnm"
    user.save()
    client.user = authenticate(username=user.email, password="testpass")
    # Set token to client to call API request
    if hasattr(client, "credentials"):
        login_with_token(client, {"email": email, "password": "testpass"})

    return user


def login_with_token(client, data):
    resp = client.post(
        reverse("login", kwargs={"version": "v1"}),
        {"email": data["email"], "password": data["password"]},
    )
    token = resp.data["auth_token"]
    client.credentials(HTTP_AUTHORIZATION="Token " + token)

    return token
