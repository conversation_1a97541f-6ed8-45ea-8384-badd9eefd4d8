from django.core.management.base import BaseCommand
from notifications.models import Notification
from datetime import datetime, timedelta
from django.conf import settings

class Command(BaseCommand):
    def handle(self, *args, **options):
        delta = datetime.now() - timedelta(hours=settings.TIME_TO_REMOVE_NOTIFICATION)
        notifications = Notification.objects.filter(timestamp__lt=delta).delete()
