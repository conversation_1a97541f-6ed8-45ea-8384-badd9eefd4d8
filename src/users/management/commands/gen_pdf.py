from io import BytesIO

import pdfkit
import PyPDF2

from django.conf import settings
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string


class Command(BaseCommand):
    def handle(self, *args, **options):
        watermark_file = settings.STATIC_ROOT + "pdf/watermark.pdf"
        template_path = "documents/demo.html"

        html = render_to_string(template_path, {})

        pdf = pdfkit.from_string(
            html,
            False,
            options={
                "page-size": "A4",
                "dpi": 400,
                "encoding": "utf-8",
                "enable-local-file-access": True,
            },
        )
        pdf = BytesIO(pdf)
        reader = PyPDF2.PdfReader(watermark_file)
        watermark_page = reader.pages[0]
        writer = PyPDF2.PdfWriter()
        reader = PyPDF2.PdfReader(pdf)
        page_indices = list(range(0, len(reader.pages)))
        for index in page_indices:
            content_page = reader.pages[index]
            mediabox = content_page.mediabox
            content_page.merge_page(watermark_page)
            content_page.mediabox = mediabox
            writer.add_page(content_page)

        pdf_add_watermark = BytesIO()
        writer.write(pdf_add_watermark)
        pdf = pdf_add_watermark.getvalue()
        print(pdf)
        print("Done")
