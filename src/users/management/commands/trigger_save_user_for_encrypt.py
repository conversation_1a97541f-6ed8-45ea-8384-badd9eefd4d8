from mirage.crypto import Crypto

from django.core.management.base import BaseCommand

from authentication.models import User
from construction_methods.models import ConstructionMethod
from kanna_api.models import UserKanna


class Command(BaseCommand):
    def handle(self, *args, **options):
        users = User.objects.all()
        for user in users:
            c = Crypto()
            email = c.encrypt(user.email)
            user.email = email
            user.save()

        user_kannas = UserKanna.objects.all()
        for user_kanna in user_kannas:
            email = c.encrypt(user_kanna.email)
            level = c.encrypt(user_kanna.level)
            user_kanna.email = email
            user_kanna.level = level
            # user = User.objects.get(email=email)
            # user_kanna.user = user
            user_kanna.save()

        construction_methods = ConstructionMethod.objects.all()
        for construction_method in construction_methods:
            warranty_year_type = c.encrypt(construction_method.warranty_year_type)
            category = c.encrypt(construction_method.category)
            complete = c.encrypt(construction_method.complete)
            construction_method.warranty_year_type = warranty_year_type
            construction_method.category = category
            construction_method.complete = complete
            construction_method.save()

        print("Done")
