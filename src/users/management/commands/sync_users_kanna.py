import logging
import os
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from api import constants
from authentication.models import User
from companies.models import Company, CompanyType
from kanna_api.models import UserKanna
from kanna_api.models.role import Role
from kanna_api.user import get_members

logger_sync_users_kanna = logging.getLogger("sync_user_kanna")


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        time_now = datetime.now()
        logger_sync_users_kanna.info("Checked Day: " + str(time_now))

        try:
            kanna_users = UserKanna.objects.get(
                Q(user__email=settings.USER_KANNA_EMAIL_TO_CREATE_CMS)
            )
        except Exception as e:
            logger_sync_users_kanna.error(f"User kanna not found: {e}")
            return

        response_data = get_members(kanna_users)
        response_data = sorted(
            response_data, key=lambda k: k["createdAt"], reverse=False
        )
        number_user_created = 0
        number_user_updated = 0
        number_user_synced = 0
        number_user_failed = 0
        created_by = kanna_users.user_uuid
        for user in response_data:
            is_created = False
            role_kanna_object = None
            logger_sync_users_kanna.info("User: " + str(user["email"]))
            try:
                with transaction.atomic():
                    user_kanna = UserKanna.objects.filter(
                        user_uuid=user["uuid"]
                    ).first()
                    if user_kanna:
                        user_online_certificate = user_kanna.user
                        if (
                            user_online_certificate_exist := User.objects.filter(
                                email=user["email"]
                            )
                            .exclude(id=user_kanna.user.id)
                            .all()
                        ):
                            user_online_certificate_exist.delete()

                        user_online_certificate.email = user["email"]
                        user_online_certificate.username = user["email"].replace(
                            "@", ""
                        )
                        user_online_certificate.first_name = user["firstName"]
                        user_online_certificate.last_name = user["lastName"]
                        user_online_certificate.save()
                        if (
                            user_kanna.name != user["fullName"]
                            or user_kanna.email != user["email"]
                        ):
                            logger_sync_users_kanna.info("User updated")
                            number_user_updated += 1
                        user_kanna.email = user["email"]
                        user_kanna.name = user["fullName"]
                        user_kanna.save()
                    else:
                        user_online_certificate, created = User.objects.get_or_create(
                            email=user["email"],
                            defaults={
                                "username": user["email"].replace("@", ""),
                                "first_name": user["firstName"],
                                "last_name": user["lastName"],
                            },
                        )
                        user_kanna = UserKanna.objects.create(
                            user_uuid=user["uuid"],
                            created_by=created_by,
                            email=user["email"],
                            name=user["fullName"],
                            user=user_online_certificate,
                        )
                        is_created = True
                        number_user_created += 1
                        logger_sync_users_kanna.info("User created")

                    if user["company"]["uuid"]:
                        role_admin = Role.objects.get(
                            id=constants.KANNA_USER_ROLE["ADMIN_SYSTEM"]
                        )
                        if (
                            user["company"]["uuid"]
                            == os.environ.get("INTERNAL_COMPANY_UUID", "")
                            and user_kanna.role != role_admin
                        ):
                            role_act = constants.KANNA_USER_ROLE["ACT"]
                            act_company = Company.objects.filter(
                                type=CompanyType.ACT
                            ).first()
                            user_kanna.company = act_company
                            role_kanna_object = Role.objects.get(id=role_act)
                            if not is_created:
                                user_kanna.role = role_kanna_object

                        user_kanna.save()
                    number_user_synced = number_user_created + number_user_updated
            except Exception as e:
                number_user_failed += 1
                logger_sync_users_kanna.error(f"Error: {e} - {user_kanna}")

        result = {
            "number_user_created": number_user_created,
            "number_user_updated": number_user_updated,
            "number_user_synced": number_user_synced,
            "number_user_failed": number_user_failed,
        }

        logger_sync_users_kanna.info(result)

        logger_sync_users_kanna.info("End sync user kanna")
