import random

from rest_framework.test import APITestCase

from django.urls import reverse

from authentication.factories import UserFactory
from companies.factories import CompanyFactory
from prefectures.factories import PrefecturesFactory
from role_management.factories import RoleFactory

from .factories import UserKannaFactory
from .models import ActDealer, UserConstruction, UserKanna


class UserKannaTests(APITestCase):
    def test_get_user_kanna_list(self):
        test_email = "<EMAIL>"
        email = str(random.randint(0, 100000)) + test_email
        UserKannaFactory(email=email)
        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 1)

        email = str(random.randint(0, 100000)) + test_email
        UserKannaFactory(email=email)

        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url + "?is_active=false")
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 0)

        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url + "?is_active=True")
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 4)

        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url + "?is_active=true")
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 4)

        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url + "?level=LEVEL_2")
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 0)

        url = reverse("kanna_users", kwargs={"version": "v1"})
        response = self.client.get(url + "?level=LEVEL_1")
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 4)
        self.assertEqual(data["results"][0]["construction_name"], "")
        self.assertEqual(data["results"][0]["building_name"], "")
        self.assertEqual(data["results"][0]["tel"], "")
        self.assertEqual(data["results"][0]["address"], "")

    def test_update_user_kanna(self):
        # test role and re-password are required fields
        user_kanna = UserKannaFactory()
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id)

        data = {
            "company_id": company.id,  # company
            "level": "LEVEL_1",
            "pic_position": "Saler",  # position name
            "pic_role": "SHOP",  # role
            "is_active": True,
        }
        url = reverse(
            "users:kanna-detail", kwargs={"version": "v1", "pk": user_kanna.pk}
        )
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["level"], "LEVEL_1")
        self.assertEqual(response.data["pic_position"], "Saler")
        self.assertEqual(response.data["pic_role"], "SHOP")
        self.assertEqual(response.data["company_id"], company.pk)
        self.assertEqual(response.data["company"]["name"], company.name)
        self.assertEqual(response.data["is_active"], True)

        data = {
            "email": "<EMAIL>",
            "name": "Test Name",
            "company_id": company.id,  # company
            "level": "LEVEL_1",
            "pic_position": "Saler",  # position name
            "pic_role": "SHOP",  # role
        }
        url = reverse(
            "users:kanna-detail", kwargs={"version": "v1", "pk": user_kanna.pk}
        )
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["level"], "LEVEL_1")
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["email"], data["email"])

    def test_add_dealer_for_act(self):
        # user is not ATC account
        act = UserKannaFactory()
        data = {"dealer_ids": "1,2,3"}
        url = reverse("users:kanna-dealers", kwargs={"version": "v1", "pk": act.pk})
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 400)

        # user is ATC accounts
        user_kanna2 = UserKannaFactory(user_uuid="95d17e0f-869b-4fdb-8cf6-3f7517da1bf5")
        data = {"dealer_ids": user_kanna2.pk}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        act_dealer = ActDealer.objects.filter(
            act_id=act.pk, dealer_id=user_kanna2.pk
        ).first()
        self.assertEqual(act_dealer.act_id, act.id)
        self.assertEqual(act_dealer.dealer_id, user_kanna2.id)

        user_kanna3 = UserKannaFactory(user_uuid="95d17e0f-869b-4fdb-8cf6-3f7517da1bf1")
        data = {
            "dealer_ids": f"{user_kanna2.pk},{user_kanna3.pk}",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        act_dealer = ActDealer.objects.filter(
            act_id=act.pk, dealer_id=user_kanna3.pk
        ).first()
        self.assertEqual(act_dealer.act_id, act.id)
        self.assertEqual(act_dealer.dealer_id, user_kanna3.id)

    def test_get_dealers_for_act(self):
        act = UserKannaFactory()
        user_kanna2 = UserKannaFactory(user_uuid="95d17e0f-869b-4fdb-8cf6-3f7517da1bf5")
        url = reverse("users:kanna-dealers", kwargs={"version": "v1", "pk": act.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        data = {"dealer_ids": user_kanna2.pk}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["dealer_id"], user_kanna2.pk)

    def test_set_roles_for_user(self):
        user = UserKannaFactory()
        role = RoleFactory(name="Shop")
        data = {"user_ids": user.pk}
        url = reverse("users:kanna-roles", kwargs={"version": "v1"})
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 401)

        data = {"user_ids": user.pk, "role_id": role.pk}
        url = reverse("users:kanna-roles", kwargs={"version": "v1"})
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)

        user = UserKanna.objects.get(pk=user.pk, is_active=True)
        self.assertEqual(user.role_id, role.pk)

        user2 = UserKannaFactory()
        user3 = UserKannaFactory()
        role = RoleFactory(name="Dealer")

        data = {"user_ids": f"{user2.pk},{user3.pk}", "role_id": role.pk}
        url = reverse("users:kanna-roles", kwargs={"version": "v1"})
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        user = UserKanna.objects.get(pk=user2.pk, is_active=True)
        self.assertEqual(user.role_id, role.pk)
        user = UserKanna.objects.get(pk=user3.pk, is_active=True)
        self.assertEqual(user.role_id, role.pk)


class UserTests(APITestCase):
    def test_company_update_user(self):
        # test role and re-password are required fields
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id)
        user = UserFactory(company_id=company.pk)

        data = {
            "construction_uuids": "1",
        }
        url = reverse("users:users-detail", kwargs={"version": "v1", "pk": user.pk})
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)
        user_cms_count = UserConstruction.objects.filter(
            construction_id=1, owner_id=user.pk
        ).count()
        self.assertNotEqual(response.data["email"], "email")
        self.assertEqual(user_cms_count, 1)

        data = {
            "construction_uuids": "1,2",
        }
        url = reverse("users:users-detail", kwargs={"version": "v1", "pk": user.pk})
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)
        user_cms_count = UserConstruction.objects.filter(
            construction_id=1, owner_id=user.pk
        ).count()
        self.assertEqual(user_cms_count, 1)
        user_cms_count = UserConstruction.objects.filter(owner_id=user.pk).count()
        self.assertEqual(user_cms_count, 2)

    def get_construction_list_of_owner(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id)
        user = UserFactory(company_id=company.pk)
        url = reverse(
            "users:users-constructions", kwargs={"version": "v1", "pk": user.pk}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 0)

        data = {
            "construction_uuids": "95d17e0f-869b-4fdb-8cf6-3f7517da1bf9, 95d17e0f-869b-4fdb-8cf6-3f7517da1bf8",
        }
        url = reverse("users:users-detail", kwargs={"version": "v1", "pk": user.pk})
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)
        user_cms_count = UserConstruction.objects.filter(owner_id=user.pk).count()
        self.assertEqual(user_cms_count, 2)

        url = reverse(
            "users:users-constructions", kwargs={"version": "v1", "pk": user.pk}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 2)


class PostCodeAPITest(APITestCase):
    def test_get_address_by_postcode(self):
        url = reverse("post_code", kwargs={"version": "v1"})
        url = url + "?code=abc"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["message"], "'No such postal code: abc'")

        url = reverse("post_code", kwargs={"version": "v1"})
        url = url + "?code=105-0011"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertNotEqual(response.data["prefecture"], "")
        self.assertEqual(response.data["city"], "港区")
        self.assertEqual(response.data["neighborhood"], "芝公園")
