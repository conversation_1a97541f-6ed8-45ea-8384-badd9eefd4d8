from rest_framework.routers import SimpleRouter

from django.urls import include
from django.urls import re_path as url

from users.views import (
    NotificationViewSet,
    RequestOwnerViewSet,
    UserKannaViewSet,
    UserViewSet,
)

router = SimpleRouter()

router.register(r"", UserViewSet, "users")
router.register(r"kanna", UserKannaViewSet, "kanna")
router.register(r"request_owner", RequestOwnerViewSet, "request_owners")
router.register(
    r"(?P<member_pk>\d+)/notifications", NotificationViewSet, "notifications"
)
app_name = "users"
urlpatterns = [url(r"^", include(router.urls))]
