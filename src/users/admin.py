from notifications.admin import NotificationAdmin
from notifications.models import Notification

from django.contrib import admin

from kanna_api.models.user_kanna import UserKanna
from users.models import ActDealer, RequestOwner, UserConstruction


@admin.register(UserKanna)
class UserKannaAdmin(admin.ModelAdmin):
    list_display = ["pk", "name", "user_uuid", "level", "company", "is_active", "email"]
    search_fields = ["name", "email", "user_uuid"]
    autocomplete_fields = ["company"]


@admin.register(ActDealer)
class ActDealerAdmin(admin.ModelAdmin):
    list_display = ["pk", "dealer", "act"]
    search_fields = ["dealer__email"]


@admin.register(RequestOwner)
class RequestOwnerAdmin(admin.ModelAdmin):
    list_display = ["pk", "email", "company_id", "status"]
    search_fields = ["email"]


@admin.register(UserConstruction)
class UserConstructionAdmin(admin.ModelAdmin):
    list_display = ["pk", "owner", "construction_uuid"]
    search_fields = ["owner__email"]


admin.site.unregister(Notification)


@admin.register(Notification)
class AccountNotificationAdmin(NotificationAdmin):
    list_display = ["pk", "recipient", "actor", "verb", "unread", "timestamp"]

    search_fields = ("recipient__email",)
