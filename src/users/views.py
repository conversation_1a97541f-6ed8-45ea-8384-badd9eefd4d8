import codecs
import csv
import logging
import os
import re
from time import strftime

from rest_framework import mixins, permissions, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, ValidationError, status
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import AccessToken

from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse
from django.utils import timezone

from api import constants
from api.constants import (
    COLUMN_CSV_USER_KANNA,
    MAX_LENGTH_NAME,
    MAX_LENGTH_PHONE,
    MAX_LENGTH_POSITION,
)
from authentication.models import User
from companies.constants import SHOP_TYPE_CHOICES
from companies.models import Company, CompanyType
from kanna_api.models import UserKanna
from kanna_api.models.role import Role
from kanna_api.user import get_members
from users.models import ActDealer, RequestOwner, UserConstruction
from users.serializers import (
    ActDealerSerializer,
    NotificationSerializer,
    RequestOwnerSerializer,
    RequestOwnerUpdateSerializer,
    UserConstructionSerializer,
    UserKannaSerializer,
    User<PERSON><PERSON>UpdateSerializer,
    UserSerializer,
    UserUpdateSerializer,
)
from utilities.order_filtering.ordering import Ordering
from utilities.seal.seal import Seal
from warranty.models import Warranty
from warranty.serializers import WarrantyOwnerSerializer

logger = logging.getLogger(__name__)


class UserViewSet(viewsets.ModelViewSet):
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = User.objects.all()
        email = self.request.query_params.get("email", None)
        if email is not None:
            queryset = queryset.filter(Q(email__icontains=email))
        return queryset

    def get_serializer_class(self):
        if self.action == "update":
            return UserUpdateSerializer
        return UserSerializer

    @action(
        detail=True,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="constructions",
    )
    def constructions(self, request, **kwargs):
        user = User.objects.filter(pk=kwargs["pk"], is_active=True).first()
        if user is None:
            raise ValidationError("Invalid user.")
        if request.method == "GET":
            constructions = UserConstruction.objects.filter(owner_id=user.pk)
            page = self.paginate_queryset(constructions)
            if page is not None:
                serialize = UserConstructionSerializer(
                    page, many=True, context={"request": request}
                )
                return self.get_paginated_response(serialize.data)
            return Response(
                UserConstructionSerializer(
                    constructions, many=True, context={"request": request}
                ).data,
                200,
            )

        return Response(status=200)

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="warranties",
    )
    def warranties(self, request, **kwargs):
        user = request.user
        if request.method == "GET":
            building_name = self.request.query_params.get("building_name", None)
            if building_name is not None:
                warranties = Warranty.objects.filter(
                    Q(owner_id=user.pk),
                    Q(management_sheet__building_name__icontains=building_name),
                )
            else:
                warranties = Warranty.objects.filter(owner_id=user.pk)

            params = self.request.query_params.get("sort_by", None)
            if params:
                sort_by_list = {
                    "construction_no",
                    "warranty_year_type",
                    "status",
                    "created_at",
                    "cms_title",
                    "building_name",
                    "start_warranty_date",
                    "end_warranty_date",
                }

                list_combine = []
                params_direction = self.request.query_params.get(
                    "order_by_direction", ""
                )

                serialize = WarrantyOwnerSerializer(
                    warranties, many=True, context={"request": request}
                )

                result_order = Ordering.order_filtering(
                    serialize.data,
                    params,
                    params_direction,
                    sort_by_list,
                    list_combine,
                    False,
                )

                page = self.paginate_queryset(result_order)

                if page is not None:
                    return self.get_paginated_response(page)
                else:
                    return Response(
                        result_order,
                        200,
                    )

            else:

                page = self.paginate_queryset(warranties)
                if page is not None:
                    serialize = WarrantyOwnerSerializer(
                        page, many=True, context={"request": request}
                    )
                    return self.get_paginated_response(serialize.data)

                else:
                    return Response(
                        WarrantyOwnerSerializer(
                            warranties, many=True, context={"request": request}
                        ).data,
                        200,
                    )

        return Response(status=200)

    # draw signed.
    @action(
        detail=False,
        methods=["post"],
        url_path="draw-signed",  # noqa
    )
    def draw_signed(self, request, *args, **kwargs):

        access_token_obj = AccessToken(
            request.META.get("HTTP_AUTHORIZATION", "").replace("Bearer ", "")
        )

        user_online_certificate = User.objects.filter(
            id=access_token_obj["user_id"]
        ).first()

        seal = Seal()
        pngImageB64String = "data:image/png;base64,"
        user_signed = user_online_certificate.last_name

        if len(user_signed) > 6:
            user_signed = user_signed[0:6]

        image = seal.draw_seal(user_signed)
        image_base64 = pngImageB64String + image.decode("utf-8")

        return Response(
            data={
                "message": "Draw electronic mark success",
                "electronic_mark_base64": image_base64,
            },
            status=200,
        )

    # update last check notification
    @action(
        detail=False,
        methods=["post"],
        url_path="checked-notification",  # noqa
    )
    def checked_notification(self, request, *args, **kwargs):
        access_token_obj = AccessToken(
            request.META.get("HTTP_AUTHORIZATION", "").replace("Bearer ", "")
        )

        user_online_certificate = User.objects.filter(
            id=access_token_obj["user_id"]
        ).first()
        user_online_certificate.last_check_notification_at = timezone.now()
        user_online_certificate.save()

        return Response(status=200)


class UserKannaViewSet(viewsets.ModelViewSet):
    serializer_class = UserKannaSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = UserKanna.objects.all()
        email = self.request.query_params.get("email", None)
        if email is not None:
            queryset = queryset.filter(Q(email__icontains=email))
        level = self.request.query_params.get("level", None)
        if level is not None:
            queryset = queryset.filter(Q(level=level))
        company_type = self.request.query_params.get("type", None)
        if company_type is not None:
            queryset = queryset.filter(Q(company__type=company_type.upper()))
        is_active = self.request.query_params.get("is_active", None)
        if is_active is not None:
            queryset = queryset.filter(Q(is_active=is_active.capitalize()))
        keyword = self.request.query_params.get("keyword", None)
        if keyword is not None:
            user_ids = []
            # normalize keyword to lower case and remove space
            keyword_reverse = " ".join(reversed(keyword.lower().split())).replace(
                " ", ""
            )
            keyword = keyword.lower().replace(" ", "")
            for key in queryset:
                if key.name:
                    # normalize name to lower case and remove space
                    name_key = key.name.lower().replace(" ", "")
                    name_key_reverse = " ".join(
                        reversed(key.name.lower().split())
                    ).replace(" ", "")
                else:
                    name_key = ""
                    name_key_reverse = ""
                if (
                    keyword in name_key
                    or keyword in name_key_reverse
                    or keyword_reverse in name_key
                    or keyword_reverse in name_key_reverse
                ):
                    user_ids.append(key.id)
            queryset = queryset.filter(Q(id__in=user_ids))
        return queryset

    def get_serializer_class(self):
        if self.action == "update":
            return UserKannaUpdateSerializer
        return UserKannaSerializer

    @action(
        detail=True,
        methods=["post", "get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="dealers",
    )
    def dealers(self, request, **kwargs):
        act_id = kwargs["pk"]
        # check if pk is off act
        act = UserKanna.objects.filter(pk=act_id).first()
        if act is None:
            raise ValidationError("Invalid ATC account")

        # Check if method POST then add dealer for ACT
        if request.method == "POST":
            dealer_ids = request.data.get("dealer_ids", None)
            if dealer_ids is not None:
                dealer_ids = dealer_ids.split(",")
                dealers = UserKanna.objects.filter(
                    pk__in=dealer_ids,
                )
                for dealer in dealers:
                    __, __ = ActDealer.objects.get_or_create(act=act, dealer=dealer)

        # Check if method GET then return list dealers for ACT
        if request.method == "GET":
            act_dealers = ActDealer.objects.filter(act=act)
            search = request.query_params.get("search", None)
            if search is not None:
                act_dealers = act_dealers.filter(dealer__name__icontains=search)
            page = self.paginate_queryset(act_dealers)
            if page is not None:
                serialize = ActDealerSerializer(
                    page, many=True, context={"request": request}
                )
                return self.get_paginated_response(serialize.data)
            return Response(
                ActDealerSerializer(
                    act_dealers, many=True, context={"request": request}
                ).data,
                200,
            )

        return Response(status=200)

    @action(
        detail=False,
        methods=["post", "get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="roles",
    )
    def roles(self, request, **kwargs):
        if request.method == "POST":
            user_ids = request.data.get("user_ids", None)
            if user_ids is None:
                raise ValidationError("Please select user")
            role_id = request.data.get("role_id", None)
            if role_id is None:
                raise ValidationError("Please select role")
            role = Role.objects.filter(pk=role_id).first()
            if role is None:
                raise NotFound("Role not found.")

            user_ids = user_ids.split(",")
            UserKanna.objects.filter(
                pk__in=user_ids,
                is_active=True,
            ).update(role_id=role.pk)
        return Response(status=200)

    # sync user Kanna
    @action(
        detail=False,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="sync-users-kanna",
    )
    def sync_users_kanna(self, request, **kwargs):
        kanna_users = UserKanna.objects.get(email=request.user.email)
        response_data = get_members(kanna_users)
        response_data = sorted(
            response_data, key=lambda k: k["createdAt"], reverse=False
        )
        number_user_created = 0
        number_user_updated = 0
        number_user_synced = 0
        number_user_failed = 0
        created_by = request.user.kanna_user.user_uuid
        for user in response_data:
            is_created = False
            role_kanna_object = None
            try:
                with transaction.atomic():
                    user_kanna = UserKanna.objects.filter(
                        user_uuid=user["uuid"]
                    ).first()
                    if user_kanna:
                        user_online_certificate = user_kanna.user
                        if (
                            user_online_certificate_exist := User.objects.filter(
                                email=user["email"]
                            )
                            .exclude(id=user_kanna.user.id)
                            .all()
                        ):
                            user_online_certificate_exist.delete()

                        user_online_certificate.email = user["email"]
                        user_online_certificate.username = user["email"].replace(
                            "@", ""
                        )
                        user_online_certificate.first_name = user["firstName"]
                        user_online_certificate.last_name = user["lastName"]
                        user_online_certificate.save()
                        if (
                            user_kanna.name != user["fullName"]
                            or user_kanna.email != user["email"]
                        ):
                            number_user_updated += 1
                        user_kanna.email = user["email"]
                        user_kanna.name = user["fullName"]
                        user_kanna.save()
                    else:
                        user_online_certificate, created = User.objects.get_or_create(
                            email=user["email"],
                            defaults={
                                "username": user["email"].replace("@", ""),
                                "first_name": user["firstName"],
                                "last_name": user["lastName"],
                            },
                        )
                        user_kanna = UserKanna.objects.create(
                            user_uuid=user["uuid"],
                            created_by=created_by,
                            email=user["email"],
                            name=user["fullName"],
                            user=user_online_certificate,
                        )
                        is_created = True
                        number_user_created += 1

                    if user["company"]["uuid"]:
                        role_admin = Role.objects.get(
                            id=constants.KANNA_USER_ROLE["ADMIN_SYSTEM"]
                        )
                        if (
                            user["company"]["uuid"]
                            == os.environ.get("INTERNAL_COMPANY_UUID", "")
                            and user_kanna.role != role_admin
                        ):
                            role_act = constants.KANNA_USER_ROLE["ACT"]
                            act_company = Company.objects.filter(
                                type=CompanyType.ACT
                            ).first()
                            user_kanna.company = act_company
                            role_kanna_object = Role.objects.get(id=role_act)
                            if not is_created:
                                user_kanna.role = role_kanna_object

                        user_kanna.save()
                    number_user_synced = number_user_created + number_user_updated
            except Exception as e:
                logger.error(f"error: {e} - {user} - {user_kanna}")
                number_user_failed += 1
                continue
        return Response(
            {
                "number_user_created": number_user_created,
                "number_user_updated": number_user_updated,
                "number_user_synced": number_user_synced,
                "number_user_failed": number_user_failed,
            },
            status=200,
        )

    # export user kanna
    @action(
        detail=False,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="export-users-kanna",
    )
    def export_users_kanna(self, request, **kwargs):
        user_kanna = self.get_queryset()
        headers = ["担当者 ID", "担当者", "グループ承認", "会社種別", "ステータス", "会社名", "会社住所", "メールアドレス"]
        file_name = f"Kanna_Users_{strftime('%Y-%m-%d-%H-%M')}"
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="{file_name}.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)

        level_choice = {
            "LEVEL_1": "最１承認",
            "LEVEL_2": "最２承認",
            "LEVEL_3": "最３承認",
        }

        shop_type_choice = SHOP_TYPE_CHOICES
        user_kanna = user_kanna.order_by("id")
        for user in user_kanna:
            tmp_data = []
            tmp_data.append(user.id)
            tmp_data.append(user.name)

            if user.level is not None:
                tmp_data.append(level_choice.get(user.level))
            else:
                tmp_data.append("")

            if user.company is not None:
                tmp_data.append(shop_type_choice.get(user.company.type))
            else:
                tmp_data.append("")

            if user.is_active:
                tmp_data.append("アクティブ")
            else:
                tmp_data.append("インアクティブ")

            if user.company is not None:
                tmp_data.append(user.company.name)
                tmp_data.append(user.company.address)
            else:
                tmp_data.append("")
                tmp_data.append("")

            tmp_data.append(user.email)
            writer.writerow(tmp_data)

        return response

    # import user kanna for AGC.
    @action(
        detail=False,
        methods=["post"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="import-users-kanna",
    )
    def import_users_kanna(self, request, **kwargs):
        """Upload data from CSV, with validation."""
        count_row_import = 0
        count_row_success = 0
        count_row_error = 0
        row_current = 0
        status_import = 0
        errors_list = []
        level = None
        file = request.FILES.get("csv_file")
        if not file:
            return Response(
                {
                    "message": "csv_file is None",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        reader = csv.DictReader(codecs.iterdecode(file, "utf-8"), delimiter=",")
        num_cols = len(reader.fieldnames)
        data = list(reader)

        def validate_csv_file(file):
            # Format must be .csv
            file_ext = os.path.splitext(file.name)[1]
            if file_ext.lower() == ".csv" and num_cols == COLUMN_CSV_USER_KANNA:
                # Valid
                return True
            # Invalid
            return False

        # Validate format csv file (structure, format)
        csv_data = validate_csv_file(file)

        if csv_data is False:
            return Response(
                {
                    "message": "csv_file is invalid",
                },
                status=400,
            )

        regex_email = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"

        def isValid(email):
            if re.fullmatch(regex_email, email):
                return True

            else:
                return False

        for row in data:
            count_row_import = count_row_import + 1
            row_current = row_current + 1
            try:
                # Validate Level:
                if list(row.values())[5] == "1":
                    level = "LEVEL_1"
                elif list(row.values())[5] == "2":
                    level = "LEVEL_2"
                elif list(row.values())[5] == "3":
                    level = "LEVEL_3"
                else:
                    errors_list.append(
                        {
                            "row": row_current,
                            "detail": "グループ承認は１〜３まで１つ値を入力してください。（１：第１承認、２：第２承認、３：最終承認）",
                        }
                    )
                    count_row_error = count_row_error + 1
                    continue

                # Validate Email:
                if list(row.values())[1] is None or list(row.values())[1] == "":
                    errors_list.append(
                        {"row": row_current, "detail": "このメールアドレスはNullになっています。"}
                    )
                    count_row_error = count_row_error + 1
                    continue

                else:
                    if isValid(list(row.values())[1]) is False:
                        errors_list.append(
                            {"row": row_current, "detail": "このメールアドレスは形式が正しくありません。"}
                        )
                        count_row_error = count_row_error + 1
                        continue

                    old_user = UserKanna.objects.filter(email=(list(row.values())[1]))
                    if old_user:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "このメールアドレスはAGCシステムに既に存在しています。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate Name:
                if list(row.values())[2] is None or list(row.values())[2] == "":
                    errors_list.append(
                        {"row": row_current, "detail": "名前ところがNullになっているレコードがあります。"}
                    )
                    count_row_error = count_row_error + 1
                    continue
                else:
                    if len(list(row.values())[2]) > MAX_LENGTH_NAME:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "名前の桁数は255より超えてはいけません。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate pic_position:
                if list(row.values())[6] is not None:

                    if len(list(row.values())[2]) > MAX_LENGTH_POSITION:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "役職の桁数は255より超えてはいけません。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate phone number:
                if list(row.values())[7] is not None and len(list(row.values())[7]) > 0:
                    # strip first and last space
                    list(row.values())[7] = list(row.values())[7].strip()
                    if len(list(row.values())[7]) > MAX_LENGTH_PHONE:
                        errors_list.append(
                            {"row": row_current, "detail": "電話番号の桁数は15より超えてはいけません。"}
                        )
                        count_row_error = count_row_error + 1
                        continue

                    # To match a string consisting of 10 to 15 digits, spaces, hyphens, or plus signs,
                    if not re.match(r"^[0-9-+ ]{10,15}$", list(row.values())[7]):
                        errors_list.append(
                            {"row": row_current, "detail": "電話番号は合計10〜15文字で入力してください。"}
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate company:
                if not list(row.values())[3]:

                    errors_list.append(
                        {
                            "row": row_current,
                            "detail": "会社ID(必須)ところがNullになっているレコードがあります。",
                        }
                    )
                    count_row_error = count_row_error + 1
                    continue
                else:
                    if not list(row.values())[3].isnumeric():
                        errors_list.append(
                            {"row": row_current, "detail": "会社ID(必須)は数字で入力してください。"}
                        )
                        count_row_error = count_row_error + 1
                        continue

                company = Company.objects.get(pk=list(row.values())[3])
                role_id = 0
                if company is not None:
                    if company.type == "SHOP":
                        role_id = constants.KANNA_USER_ROLE["MAKEUP_SHOP"]
                    if company.type == "DEALER":
                        role_id = constants.KANNA_USER_ROLE["DEALER"]
                    if company.type == "ACT":
                        role_id = constants.KANNA_USER_ROLE["ACT"]

                user_kanna = UserKanna.objects.create(
                    email=list(row.values())[1].strip(),
                    name=list(row.values())[2].strip(),
                    company_id=list(row.values())[3],
                    level=level,
                    pic_position=list(row.values())[6].strip(),
                    tel=list(row.values())[7].strip(),
                    role_id=role_id,
                    is_active=True,
                )

                user_kanna.save()
                count_row_success = count_row_success + 1

            except Company.DoesNotExist:
                errors_list.append({"row": row_current, "detail": "この会社は存在していません。"})
                count_row_error = count_row_error + 1
                continue

        if count_row_success == 0:
            status_import = status.HTTP_400_BAD_REQUEST
        else:
            status_import = status.HTTP_201_CREATED
        return Response(
            {
                "total_row:": count_row_import,
                "success:": count_row_success,
                "error:": count_row_error,
                "errors_list": errors_list,
            },
            status=status_import,
        )


class RequestOwnerViewSet(viewsets.ModelViewSet):
    serializer_class = RequestOwnerSerializer
    http_method_names = ["get", "put"]

    def get_serializer_class(self):
        if self.action == "update":
            return RequestOwnerUpdateSerializer
        return RequestOwnerSerializer

    queryset = RequestOwner.objects.all()


class NotificationViewSet(
    mixins.ListModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = self.request.user.notifications.all().prefetch_related(
            "actor", "action_object"
        )
        search = self.request.query_params.get("search", None)
        if search is not None:
            queryset = queryset.filter(Q(description__icontains=search))
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serialize = self.get_serializer(
                page, many=True, context={"request": request}
            )
            return self.get_paginated_response(serialize.data)
        return Response(
            NotificationSerializer(
                queryset,
                many=True,
                context={"request": request},
            ).data,
            200,
        )

    @action(detail=True, methods=["get"], url_name="read")
    def read(self, request, *args, **kwargs):
        instace = self.get_object()
        instace.unread = False
        instace.save()
        return Response(status.HTTP_200_OK)

    @action(detail=False, methods=["post"], url_name="mark-all-as-read")
    def mark_all_as_read(self, request, **kwargs):
        self.request.user.notifications.unread().update(unread=False)
        return Response(status=204)

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="unread",
    )
    def unread(self, request, **kwargs):
        notifications = self.request.user.notifications.unread()
        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data, status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="total_unread",
    )
    def total_unread(self, request, **kwargs):
        unread_notification_count = request.user.notifications.unread().count()
        return Response({"total_unread": unread_notification_count}, status.HTTP_200_OK)
