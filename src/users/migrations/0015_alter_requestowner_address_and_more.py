# Generated by Django 4.1.2 on 2022-12-13 04:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0014_alter_requestowner_email"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="requestowner",
            name="address",
            field=models.Char<PERSON>ield(blank=True, default="", max_length=225, null=True),
        ),
        migrations.AlterField(
            model_name="requestowner",
            name="address_detail",
            field=models.CharField(blank=True, default="", max_length=225, null=True),
        ),
        migrations.AlterField(
            model_name="requestowner",
            name="postcode",
            field=models.CharField(blank=True, default="", max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="requestowner",
            name="prefecture",
            field=models.CharField(blank=True, default="", max_length=225, null=True),
        ),
    ]
