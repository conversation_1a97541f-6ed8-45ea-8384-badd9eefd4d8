# Generated by Django 4.0.6 on 2022-08-24 09:47

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('kanna_api', '0002_userkanna_company_name_userkanna_company_uuid_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserKannaProfile',
            fields=[
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('business_type', models.CharField(choices=[('SHOP', 'Shop'), ('DEALER', 'Dealer'), ('ACT', 'ACT')], max_length=10)),
                ('business_id', models.Char<PERSON>ield(blank=True, max_length=75)),
                ('level', models.CharField(choices=[('LEVEL_1', 'Level 1'), ('LEVEL_2', 'Level 2'), ('LEVEL_3', 'Level 3')], max_length=10)),
                ('position', models.CharField(blank=True, max_length=75)),
                ('address', models.CharField(blank=True, max_length=255)),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_kanna_profile', to='kanna_api.userkanna')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ActDealer',
            fields=[
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('act', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='act', to='kanna_api.userkanna')),
                ('dealer', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='dealer', to='kanna_api.userkanna')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
    ]
