# Generated by Django 4.0.6 on 2022-09-13 04:31

from django.db import migrations, models
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_userconstruction'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestOwner',
            fields=[
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('email', models.CharField(max_length=125)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=125)),
                ('last_name', models.CharField(blank=True, max_length=125)),
                ('phone', models.CharField(blank=True, max_length=125)),
                ('address', models.Char<PERSON><PERSON>(blank=True, max_length=225)),
                ('company_id', models.IntegerField()),
                ('status', models.CharField(blank=True, choices=[('WAITING_FOR_APPROVE', 'Waiting For Approve'), ('REJECT', 'Reject'), ('APPROVED', 'Approved')], default='WAITING_FOR_APPROVE', max_length=125)),
                ('notes', models.TextField(blank=True, default='', null=True)),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
    ]
