from django_extensions.db.models import TimeStampedModel

from django.conf import settings
from django.db import models

from authentication.models import User
from companies.models import Company
from kanna_api.models import UserKanna
from utilities.helpers.notifications import save_notifications


class ActDealer(TimeStampedModel):
    id = models.AutoField(primary_key=True)
    dealer = models.ForeignKey(
        UserKanna, to_field="id", related_name="dealer", on_delete=models.DO_NOTHING
    )
    act = models.ForeignKey(
        UserKanna, to_field="id", related_name="act", on_delete=models.DO_NOTHING
    )


class UserConstruction(TimeStampedModel):
    id = models.AutoField(primary_key=True)
    owner = models.ForeignKey(
        User, to_field="id", related_name="owner", on_delete=models.DO_NOTHING
    )
    construction_uuid = models.CharField(max_length=255, blank=True, null=True)
    construction_name = models.CharField(max_length=255, blank=True, null=True)
    comment = models.CharField(blank=True, null=True, max_length=255)


class RequestOwnerStatus(models.TextChoices):
    WAITING_FOR_APPROVE = "WAITING_FOR_APPROVE"
    REJECT = "REJECT"
    APPROVED = "APPROVED"


class RequestOwner(TimeStampedModel):
    id = models.AutoField(primary_key=True)
    email = models.CharField(max_length=125, blank=True)
    first_name = models.CharField(max_length=125, blank=True)
    last_name = models.CharField(max_length=125, blank=True)
    phone = models.CharField(max_length=125, blank=True)
    address = models.CharField(max_length=225, null=True, blank=True, default="")
    prefecture = models.CharField(max_length=225, null=True, blank=True, default="")
    postcode = models.CharField(max_length=50, null=True, blank=True, default="")
    address_detail = models.CharField(max_length=225, null=True, blank=True, default="")
    company_id = models.IntegerField()
    prefectures_id = models.IntegerField(blank=True, null=True)
    status = models.CharField(
        max_length=125,
        choices=RequestOwnerStatus.choices,
        default=RequestOwnerStatus.WAITING_FOR_APPROVE,
        blank=True,
    )
    notes = models.TextField(blank=True, null=True, default="")
    is_self_register = models.BooleanField(default=False)

    class Meta:
        ordering = ["-id"]

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def send_notifications(self, verb):
        action_object = self
        number_of_owner_self_register_waiting = RequestOwner.objects.filter(
            status=RequestOwnerStatus.WAITING_FOR_APPROVE,
            is_self_register=True,
        ).count()
        number_of_owner_waiting = RequestOwner.objects.filter(
            status=RequestOwnerStatus.WAITING_FOR_APPROVE,
        ).count()
        data = {
            "id": self.company_id,
            "from": "OWNER",
            "from_level": "",
            "action": verb,
            "document_type": "USER",
            "construction_no": "",
            "construction_name": "",
            "building_name": "",
            "request_account_number": action_object.id,
            "approved_date": "",
            "number_of_owner_waiting": number_of_owner_waiting,
            "number_of_owner_self_register_waiting": number_of_owner_self_register_waiting,
        }
        try:
            recipients = User.objects.filter(
                kanna_user__company_id=self.company_id
            ).all()
            company = Company.objects.filter(id=self.company_id).first()
            if len(recipients) > 0:
                save_notifications(
                    recipients=recipients,
                    sender=recipients[0],
                    verb=verb,
                    action_object=action_object,
                    target=company,
                    data=data,
                )
        except Exception as e:
            if settings.DEBUG:
                raise Exception(str(e))
            pass

    def get_full_name(self):
        """
        Returns the first_name plus the last_name, with a space in between.
        """
        try:
            full_name = f"{self.last_name}{self.first_name}"
        except AttributeError:
            full_name = self.email
        return full_name.strip()
