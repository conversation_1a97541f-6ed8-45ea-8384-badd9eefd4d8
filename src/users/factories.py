import factory
from factory.faker import Faker

from kanna_api.models import UserKanna
from users.models import RequestOwner


class UserKannaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = UserKanna

    email = factory.Sequence(lambda n: "{0}<EMAIL>".format(n))
    name = factory.Sequence(lambda n: "Test{0} ".format(n))
    user_uuid = Faker("uuid4")
    ka_access_token = "Token[XXXXXXXXXXXXXX]"
    ka_refresh_token = "RefreshToken[XXXXXXXXXXXXXX]"
    name = "User Kanna"
    is_active = True
    role_id = None


class RequestOwnerFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RequestOwner

    email = factory.Sequence(lambda n: "{0}<EMAIL>".format(n))
    phone = "112233"
