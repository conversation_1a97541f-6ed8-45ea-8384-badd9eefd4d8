import datetime
import uuid

from rest_framework.test import APITestCase

from django.urls import reverse

from companies.factories import CompanyFactory
from management_sheets.models import (
    ConstructionMethodUnitType,
    ManagementSheetType,
)
from materials.factories import MaterialFactory


class MSAPITests(APITestCase):
    def test_create_ms(self):
        company = CompanyFactory()
        material = MaterialFactory()
        url = reverse(
            "management_sheets:management_sheets-list", kwargs={"version": "v1"}
        )
        response = self.client.post(url)
        data = response.json()
        self.assertEqual(response.status_code, 400)

        construction_uuid = str(uuid.uuid4())
        actual_construction_data = []
        actual_construction_data_1 = {
            "construction_time": datetime.date.today(),
            "material_id": material.pk,
            "number_of_crates": "1",
            "number_of_crates_unit": ConstructionMethodUnitType.CAN_SET,
        }
        actual_construction_data.append(actual_construction_data_1)
        data = {
            "construction_uuid": construction_uuid,
            "workplace_address": "100 namek",
            "type": ManagementSheetType.NEW_CONSTRUCTION,
            "company_id": company.pk,
            "actual_construction_methods": actual_construction_data,
            "actual_construction": {
                "paint_parts_name": "paint part name",
                "paint_area": "paint area",
                "notes": "notes",
                "is_approved_to_release": True,
            },
        }
        response = self.client.post(url, data, format="json")
        data = response.json()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(data["construction_uuid"], construction_uuid)

        url = reverse(
            "management_sheets:management_sheets-detail",
            kwargs={"version": "v1", "pk": data["id"]},
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            data["actual_construction"][0]["paint_parts_name"], "paint part name"
        )
        self.assertEqual(data["actual_construction"][0]["is_approved_to_release"], True)
