from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models
from django.utils import timezone

from api.constants import NOTIFICATION_DOCUMENT_TYPES
from approve_flow.states import SubmitFromShopMS
from approve_flow_settings.models import ApproveFlowSetting
from kanna_api.models.user_kanna import UserKanna
from materials.models import Materials
from utilities.helpers.notifications import save_notifications
from utilities.model.custom_model import CustomModel
from .choices import ManagementSheetStatus, ManagementSheetType


class ManagementSheet(CustomModel):
    id = models.BigAutoField(primary_key=True)
    cms_uuid = models.UUIDField(null=True)
    construction_zipcode = models.CharField(max_length=16, blank=True, null=True)
    workplace_address = models.CharField(max_length=255, blank=True, null=True)
    # for Warranty
    contractor_company = models.CharField(max_length=225, blank=True, null=True)
    city = models.CharField(max_length=225, blank=True, null=True)
    construction_no = models.Char<PERSON>ield(max_length=225, blank=True, null=True)
    full_address = models.CharField(max_length=225, blank=True, null=True)
    prefecture = models.CharField(max_length=225, blank=True, null=True)
    building_name = models.CharField(max_length=255, blank=True, null=True)
    client_name = models.CharField(max_length=255, blank=True, null=True)
    tel = models.CharField(max_length=30, null=True, blank=True)
    pic = models.ForeignKey(
        UserKanna,
        related_name="pic",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    type = ArrayField(
        models.CharField(
            max_length=25,
            choices=ManagementSheetType.choices,
            default=ManagementSheetType.NEW_CONSTRUCTION,
            blank=True,
            null=True,
        )
    )
    status = models.CharField(
        max_length=25,
        choices=ManagementSheetStatus.choices,
        default=ManagementSheetStatus.TEMP,
        blank=True,
    )
    # Approve flow setting
    approve_flow_setting = models.ForeignKey(
        ApproveFlowSetting, on_delete=models.DO_NOTHING, null=True, blank=True
    )
    is_active = models.BooleanField(default=True)
    approve_shop_comment = models.TextField(blank=True, null=True)
    expected_approve_date = models.DateField(null=True)
    approved_at = models.DateField(blank=True, null=True)
    warranty_issued_at = models.DateField(blank=True, null=True)
    warranty_registration_no = models.CharField(max_length=225, blank=True, null=True)
    number_of_guide_to_using = models.CharField(max_length=225, blank=True, null=True)
    completed_date = models.DateField(blank=True, null=True)
    expect_approve_completed_date = models.DateField(blank=True, null=True)  # 完了日
    is_agree_info_above = models.BooleanField(default=False)  # 上記工事内容を確認しました。
    is_agree_to_send_survey = models.BooleanField(default=True)  # ご不要
    building_owner_name = models.CharField(max_length=30, null=True, blank=True)  # 御中
    start_warranty_date = models.DateField(blank=True, null=True)
    ten_years_end_warranty_date = models.DateField(blank=True, null=True)
    fifteen_years_end_warranty_date = models.DateField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    pdf_url = models.URLField(blank=True, null=True)

    class Meta:
        db_table = "management_sheet"
        ordering = ["-id"]

    def __init__(self, *args, **kwargs) -> None:
        self.state = kwargs.get("state", SubmitFromShopMS)
        super().__init__(*args, **kwargs)

    def __str__(self):
        return f"MS no. {self.pk}"

    def save(self, *args, **kwargs):
        """
        Override create method to set default is_agree_to_send_survey to False
        This ensures all new ManagementSheet instances are created with survey opt-out by default
        """
        if not self.pk:
            self.is_agree_to_send_survey = False
        super().save(*args, **kwargs)

    def handle_approve(self):
        self.state.handle_approve()

    def total_diagnostics(self):
        # TODO: use relation
        from diagnostics.models import Diagnostic

        return Diagnostic.objects.filter(management_sheet=self).count()

    def handle_submit(self):
        self.state.handle_submit()

    def handle_reject(self):
        self.state.handle_reject()

    def handle_return(self):
        self.state.handle_return()

    def send_notifications(
        self, verb, sender, recipients, from_type="", from_level="LEVEL_1"
    ):
        if recipients is None:
            return True
        action_object = self
        expected_approve_date = ""
        if self.expected_approve_date is not None:
            try:
                expected_approve_date = self.expected_approve_date.strftime("%Y-%m-%d")
            except Exception as e:
                print(e)
                expected_approve_date = self.expected_approve_date
        data = {
            "id": self.id,
            "from": from_type,
            "from_level": from_level,
            "action": verb,
            "document_type": NOTIFICATION_DOCUMENT_TYPES["MS"],
            "construction_uuid": str(self.cms_uuid),
            "building_name": self.building_name if self.building_name else "",
            "request_account_number": "",
            "approved_date": expected_approve_date,
        }
        save_notifications(
            recipients=recipients,
            sender=sender,
            verb=verb,
            action_object=action_object,
            data=data,
        )


class ActualConstructionMethodType(models.TextChoices):
    UNDER_COAT_1 = "UNDER_COAT_1"
    UNDER_COAT_2 = "UNDER_COAT_2"
    MEDIUM_COATING = "MEDIUM_COATING"
    TOP_COAT_1 = "TOP_COAT_1"
    TOP_COAT_2 = "TOP_COAT_2"


class ConstructionMethodUnitType(models.TextChoices):
    CAN_SET = "CAN_SET"
    KG_M2 = "KG_M2"


class ActualConstruction(models.Model):
    id = models.BigAutoField(primary_key=True)
    paint_parts_name = models.CharField(max_length=255, null=True, blank=True)
    paint_area = models.DecimalField(max_digits=20, decimal_places=1, blank=True, null=True)
    notes = models.CharField(max_length=255, null=True, blank=True)
    basic_material = models.CharField(max_length=255, null=True, blank=True)
    is_approved_to_release_warranty = models.BooleanField(default=False)
    hexcolor = models.CharField(max_length=255, null=True, blank=True)
    hexcolor_2 = models.CharField(max_length=255, null=True, blank=True)
    is_change_15_years_to_10_years = models.BooleanField(default=False)
    management_sheet = models.ForeignKey(
        ManagementSheet,
        related_name="actual_constructions",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    diagnostic_backup_id = models.CharField(max_length=10, null=True, blank=True)
    diagnostic_construction_method = models.ForeignKey(
        "diagnostics.DiagnosticConstructionMethod",
        related_name="actual_constructions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    # warranty
    warranties = models.ManyToManyField(
        "warranty.Warranty",
        related_name="actual_constructions",
        through="warranty.WarrantyConstructionMethod",
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class ActualConstructionMethod(models.Model):
    id = models.BigAutoField(primary_key=True)
    construction_time = models.DateField(blank=True, null=True)
    material = models.ForeignKey(
        Materials,
        to_field="id",
        related_name="actual_construction_method",
        on_delete=models.DO_NOTHING,
    )
    number_of_crates = models.FloatField(blank=True, null=True)
    amount_of_use = models.FloatField(blank=True, null=True)
    type = models.CharField(
        max_length=255,
        choices=ActualConstructionMethodType.choices,
        default=ActualConstructionMethodType.UNDER_COAT_1,
    )
    actual_construction = models.ForeignKey(
        ActualConstruction,
        related_name="actual_construction_method",
        on_delete=models.CASCADE,
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
