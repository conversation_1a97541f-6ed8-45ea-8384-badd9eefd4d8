from datetime import datetime
from logging import getLogger

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import AccessToken

from django.db import transaction
from django.db.models import Q

from api.constants import KANNA_USER_ROLE
from approve_flow.models import (
    ApproveFlowStep,
    OwnerElectronicMark,
    UserElectronicMark,
    UserRoleType,
)
from approve_flow.states import (
    ApproveFromACTMS,
    ApproveFromAdminMS,
    ApproveFromDealerMS,
    RejectFromACTMS,
    RejectFromAdminMS,
    RejectFromDealerMS,
    ReturnFromACTMS,
    ReturnFromAdminMS,
    ReturnfromDealerMS,
    SubmitFromShopMS,
)
from authentication.models import User
from cms.services import get_cms_kanna, update_user_assign_cms
from cms_document.models import DocumentDownloadInformation
from companies.models import CompanyType
from dashboard.utils import get_management_sheets_by_user
from kanna_api.models.user_kanna import UserKanna
from users.serializers import OwnerElectronicMarkSerializers
from utilities.order_filtering.ordering import Ordering
from utilities.permissions.custom_permission import HasKannaPermission
from utilities.seal.seal import Seal
from warranty.models import Warranty

from .choices import ManagementSheetStatus
from .models import ManagementSheet
from .serializers import (
    GetManagementSheetDetailSerialzer,
    ManagementSheetApproveFLowSerializer,
    ManagementSheetApproveHistorySerializer,
    ManagementSheetCommonInfoApproveHistorySerializer,
    ManagementSheetSerializer,
    ManagementSheetTemporaryUpdateSerializer,
    UpdateManagementSheetSerialzer,
    get_cms_data_for_management_sheets,
)


logger = getLogger(__name__)


class ManagementSheetViewSet(viewsets.ModelViewSet):
    serializer_class = ManagementSheetSerializer
    permission_classes = [HasKannaPermission]
    http_method_names = ["get", "post", "put", "delete"]

    def get_serializer_class(self):
        if self.action in ["list", "create"]:
            return ManagementSheetSerializer
        if self.action in ["retrieve"]:
            return GetManagementSheetDetailSerialzer
        if self.action in ["update"]:
            return UpdateManagementSheetSerialzer
        if self.action == "handle_approve_flow":
            return ManagementSheetApproveFLowSerializer
        if self.action == "get_approve_history":
            return ManagementSheetApproveHistorySerializer
        if self.action == "signed_owner":
            return OwnerElectronicMarkSerializers
        if self.action == "save_temp":
            return ManagementSheetTemporaryUpdateSerializer
        return ManagementSheetSerializer

    def get_queryset(self):
        user = self.request.user
        kanna_user = user.kanna_user
        if kanna_user:
            update_user_assign_cms(kanna_user)
        list_management_ids = get_management_sheets_by_user(kanna_user)
        queryset = ManagementSheet.objects.filter(id__in=list_management_ids).all()
        ms_type = self.request.query_params.get("type", None)
        if ms_type is not None:
            ms_type = ms_type.split(",")
            queryset = queryset.filter(Q(type__contains=ms_type))
        # search by shop id
        shop_id = self.request.query_params.get("shop_id", None)
        if shop_id is not None:
            queryset = queryset.filter(
                Q(diagnostic_management_sheet__company_id=shop_id)
            )

        construction_uuid = self.request.query_params.get("construction_uuid", None)
        if construction_uuid is not None:
            queryset = queryset.filter(Q(cms_uuid=construction_uuid))

        construction_no = self.request.query_params.get("construction_no", None)
        if construction_no is not None:
            queryset = queryset.filter(Q(construction_no__icontains=construction_no))
        construction_method_id = self.request.query_params.get(
            "construction_method_id", None
        )
        if construction_method_id is not None:
            queryset = queryset.filter(
                Q(
                    diagnostic_management_sheet__diagnostic_construction_methods__construction_method_id=construction_method_id
                )
            )
        pic_name = self.request.query_params.get("applicant_name", None)
        if pic_name is not None:
            users = User.objects.all()
            user_ids = []
            pic_name = pic_name.lower()
            for user in users:
                full_name = user.get_full_name()
                full_name_reverse = f"{user.first_name} {user.last_name}"
                first_name = user.first_name if user.first_name else ""
                last_name = user.last_name if user.last_name else ""
                if (
                    pic_name in first_name.lower()
                    or pic_name in last_name.lower()
                    or pic_name in full_name.lower()
                    or pic_name in full_name_reverse.lower()
                    or pic_name.replace(" ", "") in full_name.lower().replace(" ", "")
                    or pic_name.replace(" ", "")
                    in full_name_reverse.lower().replace(" ", "")
                ):
                    user_ids.append(user.id)
            queryset = queryset.filter(
                Q(diagnostic_management_sheet__person__in=user_ids)
            )
        warranty_issued_at = self.request.query_params.get("warranty_issued_at", None)
        if warranty_issued_at is not None:
            warranty_issued_at = queryset.filter(
                Q(warranty_issued_at=warranty_issued_at)
            )
        approved_at = self.request.query_params.get("approved_at", None)
        if approved_at is not None:
            warranty_exist = Warranty.objects.filter(
                Q(management_sheet__in=queryset),
                Q(created_at__date=approved_at),
            ).values_list("management_sheet__id", flat=True)
            queryset = queryset.filter(Q(id__in=warranty_exist))
        status = self.request.query_params.get("status", None)
        if status is not None:
            queryset = queryset.filter(Q(status=status))
        title = self.request.query_params.get("title", None)
        if title is not None:
            cms_uuids = queryset.values_list("cms_uuid", flat=True).distinct()
            logger.info(f"Get all cms data for {title}")
            self.request.cms_data_map = get_cms_data_for_management_sheets(
                management_sheets=queryset, kanna_user=kanna_user
            )
            id_cms = []
            for cms_uuid in cms_uuids:
                cms = self.request.cms_data_map.get(cms_uuid)
                if not cms:
                    continue
                if cms and title.lower() in cms["title"].lower():
                    id_cms.append(cms_uuid)
            queryset = queryset.filter(Q(cms_uuid__in=id_cms))
        return queryset.distinct()

    def retrieve(self, request, *args, **kwargs):
        instace = self.get_object()
        serializer = self.get_serializer(instace, context={"user": request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        params = self.request.query_params.get("sort_by", None)
        params_direction = self.request.query_params.get("order_by_direction", "")
        sort_by_list = {
            "construction_no",
            "cms_title",
            "status",
            "prefecture",
            "applicant_name",
            "tel",
            "warranty_issue_date",
        }

        if params in sort_by_list:

            if params not in ["cms_title", "applicant_name", "warranty_issue_date"]:

                if params_direction == "ASC":
                    params = params
                else:
                    params = "-" + params

                queryset = queryset.order_by(params)

                page = self.paginate_queryset(queryset)
                cms_data_map = get_cms_data_for_management_sheets(
                    management_sheets=page, kanna_user=request.user.kanna_user
                )
                serialize = self.get_serializer(
                    page,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )
                return self.get_paginated_response(serialize.data)

            else:
                list_combine = []
                cms_data_map = getattr(self.request, "cms_data_map", None)
                if cms_data_map is None:
                    logger.info("Get all cms data")
                    cms_data_map = get_cms_data_for_management_sheets(
                        management_sheets=queryset, kanna_user=request.user.kanna_user
                    )

                serialize = self.get_serializer(
                    queryset,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )

                result_order = Ordering.order_filtering(
                    serialize.data,
                    params,
                    params_direction,
                    sort_by_list,
                    list_combine,
                    False,
                )
                page = self.paginate_queryset(result_order)
                return self.get_paginated_response(page)
        else:
            page = self.paginate_queryset(queryset)
            cms_data_map = get_cms_data_for_management_sheets(
                management_sheets=page, kanna_user=request.user.kanna_user
            )
            serialize = self.get_serializer(
                page,
                many=True,
                context={"request": request, "cms_data_map": cms_data_map},
            )
            return self.get_paginated_response(serialize.data)

    # assign signed.
    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/signed-document",  # noqa
    )
    def signed(self, request, *args, **kwargs):

        instace = self.get_object()

        access_token_obj = AccessToken(
            request.META.get("HTTP_AUTHORIZATION", "").replace("Bearer ", "")
        )
        user_online_certificate = User.objects.filter(
            id=access_token_obj["user_id"]
        ).first()

        user_kanna = UserKanna.objects.filter(
            email=user_online_certificate.email
        ).first()

        seal = Seal()
        pngImageB64String = "data:image/png;base64,"

        user_signed = user_online_certificate.last_name

        if len(user_signed) > 6:
            user_signed = user_signed[0:6]

        image = seal.draw_seal(user_signed)
        image_base64 = pngImageB64String + image.decode("utf-8")
        # insert signed for ms
        user_role_type = ''
        if user_kanna.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
            user_role_type = UserRoleType.ADMIN
        elif user_kanna.role_id == KANNA_USER_ROLE["ACT"]:
            user_role_type = UserRoleType.ACT
        elif user_kanna.role_id == KANNA_USER_ROLE["DEALER"]:
            user_role_type = UserRoleType.DEALER
        elif user_kanna.role_id == KANNA_USER_ROLE["MAKEUP_SHOP"]:
            user_role_type = UserRoleType.SHOP

        UserElectronicMark.objects.get_or_create(
            user=request.user,
            management_sheet=instace,
            image_base64=image_base64,
            user_role_type=user_role_type,
        )

        return Response(
            data={
                "message": "Create electronic mark success",
            },
            status=200,
        )

    # assign signed.
    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/signed-document-owner",  # noqa
    )
    def signed_owner(self, request, *args, **kwargs):

        instace = self.get_object()
        data = request.data
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)

        instace.expect_approve_completed_date = datetime.now()
        instace.save()

        owner_electric_mark = OwnerElectronicMark.objects.filter(
            management_sheet=instace
        )

        if owner_electric_mark:
            owner_electric_mark.update(image_base64=data["image_base64"])
        else:
            OwnerElectronicMark.objects.create(
                management_sheet=instace, image_base64=data["image_base64"]
            )
        return Response(
            data={
                "message": "Create electronic mark success",
            },
            status=200,
        )

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/pdf-url",  # noqa
    )
    def get_pdf_url(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            document_info_instance = DocumentDownloadInformation.objects.get(
                management_sheet=instance
            )
            urls = document_info_instance.get_url_by_admin_account(
                request.user, document_type="management-sheet"
            )
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )
        except DocumentDownloadInformation.DoesNotExist:
            urls = {
                "url": "",
                "download_url": "",
            }
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/approve-history",  # noqa
    )
    def get_approve_history(self, request, *args, **kwargs):
        instance = self.get_object()
        steps = ApproveFlowStep.objects.filter(management_sheet=instance)
        steps_serializer = self.get_serializer(steps, many=True)
        common_serializer = ManagementSheetCommonInfoApproveHistorySerializer(
            instance, context={"request": request}
        )
        return Response(
            data={
                "data": {
                    "common": common_serializer.data,
                    "history": steps_serializer.data,
                }
            },
            status=status.HTTP_200_OK,
        )

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/(?P<action>\w+)",  # noqa
    )
    @transaction.atomic()
    def handle_approve_flow(self, request, action, *args, **kwargs):
        if action not in ["approve", "reject", "return"]:
            return Response({"success": False}, status=400)

        # Validate request
        data = request.data
        management_sheet = self.get_object()
        serializer = self.get_serializer(
            data=data, context={"management_sheet": management_sheet, "action": action}
        )
        serializer.is_valid(raise_exception=True)

        # Handle the logic of updating a management sheet status after it has been updated by someone else.
        latest_updated_at_str = data.get("latest_updated_at")
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != management_sheet.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Get data
        user = request.user
        kanna_user = user.kanna_user
        company = kanna_user.company
        comment = data["comment"]
        if hasattr(management_sheet, "management_sheet_approve_flow_setting_instance"):
            approve_flow_setting_instance = (
                management_sheet.management_sheet_approve_flow_setting_instance
            )
        else:
            return Response(
                {"detail": "Something went wrong in approve flow"}, status=400
            )
        # If management sheet has stt is APPROVED, RETURN or REJECT
        # return 400 error
        if management_sheet.status in [
            ManagementSheetStatus.REJECT,
            ManagementSheetStatus.APPROVED,
        ]:
            return Response(
                # Message mean: This management sheet already approved, returned or rejected
                {
                    "detail": [
                        "この管理シートは既に承認されました。",
                        "この管理シートは既に返却されました。",
                    ]
                },
                status=400,
            )

        # Handle action approve
        if action == "approve":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                state = ApproveFromAdminMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.SHOP:
                state = SubmitFromShopMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.DEALER:
                state = ApproveFromDealerMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.ACT:
                state = ApproveFromACTMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logger.info(f"Approved {state} from user {kanna_user} in company {company}")

            management_sheet.state = state
            management_sheet.handle_approve()
            # @TODO: Check if last_level of ACT is approve, return data of warranty
            return Response({"success": True}, status=200)

        # Handle action reject
        if action == "reject":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                state = RejectFromAdminMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.DEALER:
                state = RejectFromDealerMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.ACT:
                state = RejectFromACTMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            management_sheet.state = state
            management_sheet.handle_reject()
            return Response({"success": True}, status=200)

        # Handle action return
        if action == "return":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                state = ReturnFromAdminMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.DEALER:
                state = ReturnfromDealerMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.ACT:
                state = ReturnFromACTMS(
                    kanna_user=kanna_user,
                    management_sheet=management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            management_sheet.state = state
            management_sheet.handle_return()
            return Response({"success": True}, status=200)

    def update(self, request, *args, **kwargs):
        data = request.data

        # Handle the logic of updating a record after it has been deleted by someone else.
        management_sheet = ManagementSheet._base_manager.filter(id=kwargs["pk"]).first()
        if management_sheet and management_sheet.is_deleted:
            return Response(
                data={"detail": "すでにデータは削除されています", "is_deleted": management_sheet.is_deleted},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Handle the logic of updating a record after it has been updated by someone else.
        latest_updated_at_str = data.get('latest_updated_at')
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != management_sheet.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        instance = self.get_object()
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(instance, serializer.validated_data)
        return Response(response_data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/save-temp",
    )
    def save_temp(self, request, *args, **kwargs):
        data = request.data
        # Handle the logic of updating a record after it has been deleted by someone else or not exits.
        management_sheet = ManagementSheet._base_manager.filter(id=kwargs["pk"]).first()
        if management_sheet:
            if management_sheet.is_deleted:
                return Response(
                    data={"detail": "すでにデータは削除されています", "is_deleted": management_sheet.is_deleted},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                data={"detail": "データは存在していません。"},
                status=status.HTTP_404_NOT_FOUND,
            )
        # Handle the logic of updating a record after it has been updated by someone else.
        latest_updated_at_str = data.get('latest_updated_at')
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != management_sheet.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(management_sheet, data)
        return Response(response_data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/save-temp",
    )
    def save_temp(self, request, *args, **kwargs):
        data = request.data
        # Handle the logic of updating a record after it has been deleted by someone else or not exits.
        management_sheet = ManagementSheet._base_manager.filter(id=kwargs["pk"]).first()
        if management_sheet:
            if management_sheet.is_deleted:
                return Response(
                    data={"detail": "すでにデータは削除されています", "is_deleted": management_sheet.is_deleted},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                data={"detail": "データは存在していません。"},
                status=status.HTTP_404_NOT_FOUND,
            )
        # Handle the logic of updating a record after it has been updated by someone else.
        latest_updated_at_str = data.get('latest_updated_at')
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != management_sheet.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(management_sheet, data)
        return Response(response_data, status=status.HTTP_200_OK)
