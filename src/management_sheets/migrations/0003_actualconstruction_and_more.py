# Generated by Django 4.0.6 on 2022-09-21 13:09

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('construction_methods', '0008_alter_constructionmethodstep_material'),
        ('materials', '0003_alter_materials_options'),
        ('companies', '0004_alter_company_options'),
        ('management_sheets', '0002_alter_managementsheet_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActualConstruction',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('paint_parts_name', models.CharField(max_length=255)),
                ('paint_area', models.CharField(max_length=255)),
                ('notes', models.CharField(max_length=255)),
                ('is_approved_to_release', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='managementsheet',
            name='construction_method',
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='ms_company', to='companies.company'),
        ),
        migrations.AlterField(
            model_name='managementsheet',
            name='construction_uuid',
            field=models.UUIDField(blank=True),
        ),
        migrations.CreateModel(
            name='ActualConstructionMethod',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('construction_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('number_of_crates', models.IntegerField(default=0)),
                ('number_of_crates_unit', models.CharField(choices=[('CAN_SET', 'Can Set'), ('KG_M2', 'Kg M2')], default='CAN_SET', max_length=16)),
                ('amount_of_use', models.IntegerField(default=0)),
                ('type', models.CharField(choices=[('UNDER_COAT', 'Under Coat'), ('MEDIUM_COATING', 'Medium Coating'), ('TOP_COAT_1', 'Top Coat 1'), ('TOP_COAT_2', 'Top Coat 2')], default='UNDER_COAT', max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('actual_construction', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='ms_actual_construction', to='management_sheets.actualconstruction')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='construction_method_material', to='materials.materials')),
                ('refer_construction_method', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='construction_method', to='construction_methods.constructionmethod')),
            ],
        ),
        migrations.AddField(
            model_name='actualconstruction',
            name='management_sheet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='management_sheet', to='management_sheets.managementsheet'),
        ),
    ]
