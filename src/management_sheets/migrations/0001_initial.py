# Generated by Django 4.0.6 on 2022-09-15 08:38

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('kanna_api', '0006_remove_userkanna_company_name_and_more'),
        ('construction_methods', '0002_alter_constructionmethodstep_material'),
    ]

    operations = [
        migrations.CreateModel(
            name='ManagementSheet',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('construction_uuid', models.UUIDField(blank=True, unique=True)),
                ('workplace_address', models.CharField(max_length=255)),
                ('main_contractor', models.CharField(max_length=255)),
                ('type', models.CharField(blank=True, choices=[('NEW_CONSTRUCTION', 'New Construction'), ('RENOVATION', 'Renovation')], default='NEW_CONSTRUCTION', max_length=25)),
                ('status', models.CharField(blank=True, choices=[('WAITING_FOR_APPROVE', 'Waiting For Approve'), ('APPROVED', 'Approved'), ('CANCELLED', 'Cancelled'), ('REJECTED', 'Rejected'), ('RESEND', 'Resend')], default='WAITING_FOR_APPROVE', max_length=25)),
                ('is_active', models.BooleanField(default=True)),
                ('appoved_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('warranty_issued_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('construction_method', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='construction_method', to='construction_methods.constructionmethod')),
                ('pic', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='pic', to='kanna_api.userkanna')),
            ],
            options={
                'db_table': 'management_sheet',
            },
        ),
    ]
