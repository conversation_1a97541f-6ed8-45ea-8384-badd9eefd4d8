# Generated by Django 4.1.3 on 2025-02-10 08:24

import django.db.models.deletion
from django.db import migrations, models


def create_diagnostic_construction_method(apps, schema_editor):
    # Get model
    ActualConstruction = apps.get_model('management_sheets', 'ActualConstruction')
    DiagnosticConstructionMethod = apps.get_model('diagnostics', 'DiagnosticConstructionMethod')
    Diagnostic = apps.get_model('diagnostics', 'Diagnostic')

    # Update model actual construction
    actual_construction_update = []
    actual_constructions = ActualConstruction.objects.all()

    for actual_construction in actual_constructions:
        if not actual_construction.diagnostic_id:
            continue

        if not Diagnostic.objects.filter(id=actual_construction.diagnostic_id).exists():
            continue

        diagnostic_cm = DiagnosticConstructionMethod.objects.filter(
            diagnostic=actual_construction.diagnostic).first()

        actual_construction.diagnostic_construction_method = diagnostic_cm
        actual_construction.diagnostic_backup_id = actual_construction.diagnostic_id  # Backup diagnostic id
        actual_construction_update.append(actual_construction)

    if actual_construction_update:
        ActualConstruction.objects.bulk_update(
            actual_construction_update,
            fields=['diagnostic_construction_method', 'diagnostic_backup_id']
        )


class Migration(migrations.Migration):
    dependencies = [
        ('diagnostics', '0028_rename_building_painted_area_diagnostic_building_painted_area_backup_and_more'),
        ('management_sheets', '0041_alter_actualconstruction_paint_area'),
    ]

    operations = [
        migrations.AddField(
            model_name='actualconstruction',
            name='diagnostic_backup_id',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='actualconstruction',
            name='diagnostic_construction_method',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="actual_constructions",
                to="diagnostics.diagnosticconstructionmethod",
            ),
        ),
        migrations.RunPython(create_diagnostic_construction_method),
        migrations.AddField(
            model_name='actualconstruction',
            name='hexcolor_2',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.RemoveField(
            model_name='actualconstruction',
            name='diagnostic',
        ),
        migrations.AlterField(
            model_name='actualconstruction',
            name='paint_area',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=20, null=True),
        ),
        migrations.AlterField(
            model_name="actualconstructionmethod",
            name="actual_construction",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="actual_construction_method",
                to="management_sheets.actualconstruction",
            ),
        ),
    ]
