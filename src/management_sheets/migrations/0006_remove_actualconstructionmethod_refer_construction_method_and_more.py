# Generated by Django 4.0.6 on 2022-10-05 14:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('approve_flow_settings', '0002_alter_approveflowsetting_dealer'),
        ('construction_methods', '0013_rename_dilution_max_constructionmethodstep_paint_distance_date_and_more'),
        ('management_sheets', '0005_remove_managementsheet_company'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='actualconstructionmethod',
            name='refer_construction_method',
        ),
        migrations.AddField(
            model_name='actualconstruction',
            name='refer_construction_method',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='refer_construction_method', to='construction_methods.constructionmethod'),
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='approve_flow_setting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='approve_flow_settings.approveflowsetting'),
        ),
        migrations.AlterField(
            model_name='actualconstructionmethod',
            name='type',
            field=models.CharField(choices=[('UNDER_COAT_1', 'Under Coat 1'), ('UNDER_COAT_2', 'Under Coat 2'), ('MEDIUM_COATING', 'Medium Coating'), ('TOP_COAT_1', 'Top Coat 1'), ('TOP_COAT_2', 'Top Coat 2')], default='UNDER_COAT_1', max_length=255),
        ),
    ]
