# Generated by Django 4.0.6 on 2022-10-21 06:43

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('management_sheets', '0018_rename_is_approved_to_release_actualconstruction_is_approved_to_release_warranty_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='managementsheet',
            name='type',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, choices=[('NEW_CONSTRUCTION', 'New Construction'), ('RENOVATION', 'Renovation')], default='NEW_CONSTRUCTION', max_length=25, null=True), size=None),
        ),
    ]
