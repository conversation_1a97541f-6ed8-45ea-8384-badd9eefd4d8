# Generated by Django 4.0.6 on 2022-10-06 04:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('diagnostics', '0011_remove_diagnostic_expected_comment'),
        ('management_sheets', '0006_remove_actualconstructionmethod_refer_construction_method_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='actualconstruction',
            name='diagnostic',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='actual_construction_diagnostic', to='diagnostics.diagnostic'),
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='deleted_at',
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
    ]
