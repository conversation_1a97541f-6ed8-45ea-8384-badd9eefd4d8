# Generated by Django 4.0.6 on 2022-10-03 14:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('management_sheets', '0003_actualconstruction_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='managementsheet',
            old_name='appoved_at',
            new_name='approved_at',
        ),
        migrations.RemoveField(
            model_name='managementsheet',
            name='construction_uuid',
        ),
        migrations.RemoveField(
            model_name='managementsheet',
            name='main_contractor',
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='cms_uuid',
            field=models.UUIDField(null=True),
        ),
        migrations.AlterField(
            model_name='managementsheet',
            name='status',
            field=models.CharField(blank=True, choices=[('TEMP', 'Temporary'), ('WAITING_APPROVAL', 'Waiting for approval'), ('APPROVED', 'Approval'), ('REJECT', 'Reject'), ('RETURN', 'Return')], default='TEMP', max_length=25),
        ),
    ]
