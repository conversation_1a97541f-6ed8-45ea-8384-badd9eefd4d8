# Generated by Django 4.0.6 on 2022-10-25 04:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('management_sheets', '0022_actualconstruction_warranties'),
    ]

    operations = [
        migrations.AddField(
            model_name='managementsheet',
            name='address',
            field=models.Char<PERSON><PERSON>(default='', max_length=225),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='building_name',
            field=models.Char<PERSON>ield(default='', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='client_name',
            field=models.Char<PERSON>ield(default='', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='full_address',
            field=models.Char<PERSON>ield(default='', max_length=225),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='prefecture',
            field=models.Char<PERSON><PERSON>(default='', max_length=225),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='tel',
            field=models.<PERSON>r<PERSON>ield(blank=True, max_length=30, null=True),
        ),
    ]
