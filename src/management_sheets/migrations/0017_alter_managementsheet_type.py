# Generated by Django 4.0.6 on 2022-10-20 06:32

import django.contrib.postgres.fields
from django.db import migrations, models


def update_type_is_null(apps, schema_editor):
    managementsheet_model = apps.get_model("management_sheets", "ManagementSheet")
    managementsheet_model.objects.all().update(type="{}")


class Migration(migrations.Migration):

    dependencies = [
        ('management_sheets', '0016_managementsheet_completed_date_and_more'),
    ]
    operations = [
        migrations.RunPython(update_type_is_null),

        migrations.AlterField(
            model_name='managementsheet',
            name='type',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, choices=[('NEW_CONSTRUCTION', 'New Construction'), ('RENOVATION', 'Renovation')], default='NEW_CONSTRUCTION', max_length=25), size=None),
        ),
    ]
