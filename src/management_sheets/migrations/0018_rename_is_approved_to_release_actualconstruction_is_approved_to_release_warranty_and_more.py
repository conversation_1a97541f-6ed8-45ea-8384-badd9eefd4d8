# Generated by Django 4.0.6 on 2022-10-21 03:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('management_sheets', '0017_alter_managementsheet_type'),
    ]

    operations = [
        migrations.RenameField(
            model_name='actualconstruction',
            old_name='is_approved_to_release',
            new_name='is_approved_to_release_warranty',
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='fifteen_years_end_warranty_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='start_warranty_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='managementsheet',
            name='ten_years_end_warranty_date',
            field=models.DateField(blank=True, null=True),
        ),
    ]
