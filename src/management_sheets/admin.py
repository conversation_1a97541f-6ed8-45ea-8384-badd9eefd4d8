from django.contrib import admin

from .models import (
    ActualConstruction,
    ActualConstructionMethod,
    ManagementSheet,
)


@admin.register(ManagementSheet)
class ManagementSheetAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "cms_uuid",
        "status",
        "pic",
        "type",
        "workplace_address",
        "approve_flow_setting",
    ]
    search_fields = ["id"]


@admin.register(ActualConstructionMethod)
class ActualConstructionMethodAdmin(admin.ModelAdmin):
    list_display = ["id", "actual_construction", "type"]
    search_fields = ["id", "actual_construction__pk"]


@admin.register(ActualConstruction)
class ActualConstructionAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "management_sheet",
    ]
    search_fields = ["id", "management_sheet__pk"]
