import logging
from typing import Any, Dict, List

from rest_framework import serializers

from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db.models import Q

from api.constants import KANNA_USER_ROLE, KannaUserRoleEnum
from approve_flow.models import (
    ApproveFlowSettingInstance,
    ApproveFlowStep,
    ApproveFlowStepStatusChoices,
    OwnerElectronicMark,
    UserElectronicMark,
    UserRoleType,
)
from approve_flow.services import get_min_max_level_in_company
from approve_flow_settings.models import ApproveFlowSetting
from approve_flow_settings.serializers import ApproveFlowSettingSerializer
from authentication.models import User
from cms.services import fetch_cms_data_map, get_cms_kanna
from companies.models import CompanyType
from companies.serializers import CompanySerializer
from construction_methods.serializers import (
    ListConstructionMethodSerializer,
    MaterialDetailSerializer,
)
from diagnostics.choices import DiagnosticStatus
from diagnostics.models import Diagnostic, DiagnosticConstructionMethod
from diagnostics.serializers import (
    GetCommentAndLevelSerializer,
    ListDiagnosticSerializer,
)
from materials.models import Materials
from users.models import UserConstruction
from users.serializers import (
    UserCommonSerializer,
    UserKannaLiteSerializer,
    UserKannaSerializer,
    UserLiteSerializer,
)
from warranty.models import Warranty

from .choices import ManagementSheetStatus
from .models import (
    ActualConstruction,
    ActualConstructionMethod,
    ActualConstructionMethodType,
    ManagementSheet,
    ManagementSheetType,
)

logger = logging.getLogger(__name__)


def get_cms_data_for_management_sheets(
    management_sheets: List[ManagementSheet], kanna_user
) -> Dict[str, Any]:
    """
    Fetch CMS data for multiple management sheets in parallel.

    Args:
        management_sheets: List of management sheet objects
        kanna_user: Kanna user for API authentication

    Returns:
        Dict mapping cms_uuid to cms_data, empty dict for failed requests
    """
    # Extract unique cms_uuids
    cms_uuids = list(
        set([management_sheet.cms_uuid for management_sheet in management_sheets])
    )
    return fetch_cms_data_map(cms_uuids, kanna_user)


class ActualConstructionMethodSerialize(serializers.ModelSerializer):
    construction_time = serializers.CharField(required=True)
    material = MaterialDetailSerializer(read_only=True)
    material_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = ActualConstructionMethod
        fields = [
            "id",
            "construction_time",
            "material_id",
            "type",
            "material",
            "number_of_crates",
            "amount_of_use",
        ]

        depth = 1


class MsActualConstructionMethodUpdateSerialize(serializers.Serializer):
    type = serializers.ChoiceField(
        choices=ActualConstructionMethodType.choices, required=True
    )
    construction_time = serializers.DateField(required=False, allow_null=True)
    material_id = serializers.IntegerField(required=True)
    number_of_crates = serializers.FloatField(required=False, allow_null=True)
    amount_of_use = serializers.FloatField(required=False, allow_null=True)

    def validate(self, attrs):
        try:
            Materials.objects.get(id=attrs["material_id"])
            return super().validate(attrs)
        except Materials.DoesNotExist:
            raise serializers.ValidationError({"related_fields": "Material not found"})


class MsActualConstructionUpdateSerialize(serializers.Serializer):
    paint_parts_name = serializers.CharField(required=False, allow_blank=True)
    basic_material = serializers.CharField(required=False, allow_blank=True)
    hexcolor = serializers.CharField(required=True, allow_blank=True, allow_null=True)
    hexcolor_2 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    paint_area = serializers.CharField(required=False, allow_blank=True)
    diagnostic_construction_method_id = serializers.CharField(required=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    is_approved_to_release_warranty = serializers.BooleanField(required=False)
    actual_construction_methods = serializers.ListField(
        child=MsActualConstructionMethodUpdateSerialize()
    )
    is_change_15_years_to_10_years = serializers.BooleanField(required=False)

    def validate_paint_area(self, value):
        if not value:
            return None
        try:
            value = float(value)
            if value <= 0:
                raise serializers.ValidationError(
                    "Please enter a number greater than 0 for the paint area."
                )
            return round(value, 1)
        except ValueError:
            raise serializers.ValidationError("Please enter a number")

    def validate(self, attrs):
        try:
            DiagnosticConstructionMethod.objects.get(id=attrs["diagnostic_construction_method_id"])
            return super().validate(attrs)
        except ObjectDoesNotExist:
            raise serializers.ValidationError(
                {"related_fields": "Diagnostic Construction Method not found"}
            )


class ActualConstructionSerialize(serializers.ModelSerializer):
    paint_parts_name = serializers.CharField(required=False)
    hexcolor = serializers.CharField(required=False)
    hexcolor_2 = serializers.CharField(required=False)
    basic_material = serializers.CharField(required=False)
    paint_area = serializers.CharField(required=False)
    notes = serializers.CharField(required=True)
    is_change_15_years_to_10_years = serializers.BooleanField(required=False)
    is_approved_to_release_warranty = serializers.BooleanField(required=True)

    class Meta:
        model = ActualConstruction
        fields = [
            "id",
            "paint_parts_name",
            "basic_material",
            "hexcolor",
            "hexcolor_2",
            "paint_area",
            "notes",
            "is_approved_to_release_warranty",
            "is_change_15_years_to_10_years",
        ]
        depth = 1


class ActualConstructionDetailSerialize(serializers.ModelSerializer):
    actual_construction_methods = serializers.SerializerMethodField()
    is_approved_to_release_warranty = serializers.BooleanField(initial=True)

    class Meta:
        model = ActualConstruction
        fields = [
            "id",
            "paint_parts_name",
            "paint_area",
            "basic_material",
            "notes",
            "actual_construction_methods",
            "is_approved_to_release_warranty",
            "hexcolor",
            "hexcolor_2",
            "is_change_15_years_to_10_years",
        ]

    def get_actual_construction_methods(self, obj):
        construction_method = obj.diagnostic_construction_method.construction_method
        actual_cms = (
            ActualConstructionMethod.objects.select_related("material")
            .filter(
                actual_construction_id=obj.pk,
                material_id__in=construction_method.construction_method_step.values_list(
                    "material_id", flat=True
                ),
            )
            .order_by("id")
        )

        if actual_cms:
            serializer = ActualConstructionMethodSerialize(
                actual_cms,
                many=True,
            )
            return serializer.data
        else:
            return None


class ManagementSheetSerializer(serializers.ModelSerializer):
    type = serializers.ChoiceField(choices=ManagementSheetType.choices, required=True)
    approve_completed_date = serializers.SerializerMethodField(read_only=True)
    cms_title = serializers.SerializerMethodField(read_only=True)
    applicant_name = serializers.SerializerMethodField(read_only=True)
    warranty_issue_date = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ManagementSheet
        fields = [
            "id",
            "cms_uuid",
            "cms_title",
            "construction_no",
            "type",
            "applicant_name",
            "workplace_address",
            "contractor_company",
            "construction_zipcode",
            "city",
            "full_address",
            "prefecture",
            "building_name",
            "client_name",
            "tel",
            "status",
            "number_of_guide_to_using",
            "warranty_registration_no",
            "completed_date",
            "approve_completed_date",
            "expect_approve_completed_date",
            "is_agree_info_above",
            "is_agree_to_send_survey",
            "building_owner_name",
            "start_warranty_date",
            "ten_years_end_warranty_date",
            "fifteen_years_end_warranty_date",
            "approved_at",
            "warranty_issue_date",
        ]

    def create(self, validated_data):
        actual_construction_methods = validated_data.pop("actual_construction_methods")
        actual_construction_data = validated_data.pop("actual_construction")
        # create managementsheet
        management_sheet = ManagementSheet.objects.create(**validated_data)
        # create actual construction
        actual_construction = ActualConstruction.objects.create(
            **actual_construction_data, management_sheet=management_sheet
        )

        # Create actual construction method
        for at_cms in actual_construction_methods:
            item = ActualConstructionMethod(
                **{
                    "construction_time": at_cms["construction_time"],
                    "material_id": at_cms["material_id"],
                    "number_of_crates": at_cms["number_of_crates"],
                    "amount_of_use": at_cms["amount_of_use"],
                    "actual_construction_id": actual_construction.pk,
                }
            )
            item.save()

        return ManagementSheetSerializer(
            management_sheet, context={"user": self.context["user"]}
        ).data

    # def get_pic(self, obj):
    #     diagnostic = Diagnostic.objects.filter(management_sheet=obj).first()
    #     if diagnostic is not None:
    #         serializer = UserCommonSerializer(diagnostic.person)
    #         return serializer.data
    #     return None

    def get_applicant_name(self, obj):
        diagnostic = Diagnostic.objects.filter(management_sheet=obj).first()
        if diagnostic is not None:
            # serializer = UserCommonSerializer(diagnostic.person)
            return diagnostic.person.get_full_name()
        return None

    def get_cms_title(self, obj):
        cms_title = ""
        # Check if cms_data is available in context
        cms_data_map = self.context.get("cms_data_map", {})
        cms_data = cms_data_map.get(obj.cms_uuid)

        if cms_data and isinstance(cms_data, dict):
            return cms_data.get("title", "")

        if "request" in self.context:
            try:
                cms = get_cms_kanna(
                    obj.cms_uuid, self.context["request"].user.kanna_user
                )
                cms_title = cms.get("title")
            except Exception:
                pass
        return cms_title

    def get_approve_completed_date(self, obj):
        approve_step = (
            ApproveFlowStep.objects.filter(management_sheet=obj).order_by("-id").first()
        )
        approve_completed_date = ""
        if approve_step is not None:
            approve_completed_date = approve_step.updated_at
        return approve_completed_date

    def get_warranty_issue_date(self, obj):
        warranty_issue_date = ""
        warranty_obj = (
            Warranty.objects.filter(
                Q(management_sheet_id=obj.id),
            )
            .values_list("created_at", flat=True)
            .first()
        )
        if warranty_obj is not None:
            warranty_issue_date = warranty_obj
        return warranty_issue_date


class MsDiagnosticSerializer(ListDiagnosticSerializer):
    construction_methods = serializers.SerializerMethodField()
    actual_constructions = serializers.SerializerMethodField()

    class Meta:
        model = Diagnostic
        fields = (
            "id",
            "cms_uuid",
            "type",
            "status",
            "construction_zipcode",
            "construction_prefecture",
            "construction_address",
            "construction_address_extend",
            "construction_owner",
            "phone_number",
            "construction_methods",
            "actual_constructions",
        )

    def get_actual_constructions(self, obj):
        diagnostic_cm = DiagnosticConstructionMethod.objects.filter(diagnostic=obj)
        return [
            {**
             ActualConstructionDetailSerialize(
                 ActualConstruction.objects.filter(diagnostic_construction_method=item).first()).data,
             "diagnostic_construction_method_id": item.id, }
            for item in diagnostic_cm
        ]

    def get_construction_methods(self, obj):
        diag_construction_methods = DiagnosticConstructionMethod.objects.filter(diagnostic=obj)

        return [
            {**ListConstructionMethodSerializer(diag_construction_method.construction_method).data,
             "painting_area": diag_construction_method.painting_area}
            for diag_construction_method in diag_construction_methods
        ]


class ElectronicMarkSerializer(serializers.ModelSerializer):
    user = UserKannaLiteSerializer()

    class Meta:
        model = UserElectronicMark
        fields = ("user", "management_sheet", "image_base64")


class GetManagementSheetDetailSerialzer(ManagementSheetSerializer):
    diagnostics = serializers.SerializerMethodField()
    company = serializers.SerializerMethodField(read_only=True)
    pic = serializers.SerializerMethodField(read_only=True)
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    actions = serializers.SerializerMethodField(read_only=True)
    comments = serializers.SerializerMethodField(read_only=True)
    electronic_mark = serializers.SerializerMethodField(read_only=True)
    owner_electronic_mark = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ManagementSheet
        fields = [
            "id",
            "cms_uuid",
            "construction_no",
            "company",
            "pic",
            "type",
            "contractor_company",
            "construction_zipcode",
            "city",
            "full_address",
            "prefecture",
            "building_name",
            "client_name",
            "tel",
            "approve_flow_setting",
            "diagnostics",
            "status",
            "approve_shop_comment",
            "number_of_guide_to_using",
            "warranty_registration_no",
            "completed_date",
            "expect_approve_completed_date",
            "is_agree_info_above",
            "is_agree_to_send_survey",
            "building_owner_name",
            "start_warranty_date",
            "ten_years_end_warranty_date",
            "fifteen_years_end_warranty_date",
            "expected_approve_date",
            "approved_at",
            "actions",
            "comments",
            "electronic_mark",
            "owner_electronic_mark",
            "updated_at",
        ]

    def get_diagnostics(self, obj):
        diagnostics = (
            Diagnostic.objects.filter(management_sheet=obj)
            .exclude(status=DiagnosticStatus.REJECT)
            .order_by("id")
        )
        if diagnostics is not None:
            serializer = MsDiagnosticSerializer(
                diagnostics,
                many=True,
            )
            return serializer.data
        return None

    def get_pic(self, obj):
        diagnostic = (
            Diagnostic.objects.filter(management_sheet=obj)
            .exclude(status=DiagnosticStatus.REJECT)
            .first()
        )
        if diagnostic is not None:
            serializer = UserCommonSerializer(diagnostic.person)
            return serializer.data
        return None

    def get_company(self, obj):
        diagnostic = (
            Diagnostic.objects.filter(management_sheet=obj)
            .exclude(status=DiagnosticStatus.REJECT)
            .first()
        )
        if diagnostic is not None:
            return CompanySerializer(diagnostic.company).data
        return None

    # Get list allow actions what current user can do
    def get_actions(self, obj):
        user = self.context["user"]
        company = user.kanna_user.company

        level = user.kanna_user.level
        list_actions = []
        if user.kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            if obj.status in (ManagementSheetStatus.WAITING_APPROVAL,):
                list_actions.extend(["approve", "reject", "return", "signed_by_admin"])
        elif company:
            max_level = None
            if company.type != CompanyType.SHOP:
                _, max_level = get_min_max_level_in_company(company)

            if (
                hasattr(obj, "management_sheet_approve_flow_setting_instance")
                and obj.management_sheet_approve_flow_setting_instance is not None
            ):
                approve_flow_setting_instance = (
                    obj.management_sheet_approve_flow_setting_instance
                )
                # Handle approve by level or not
                if (
                    not approve_flow_setting_instance.is_approve_level_dealer
                    and company.type == CompanyType.DEALER
                ) or (
                    not approve_flow_setting_instance.is_approve_level_act
                    and company.type == CompanyType.ACT
                ):
                    last_step = ApproveFlowStep.objects.filter(
                        management_sheet=obj,
                        approve_flow_setting_instance=approve_flow_setting_instance,
                        company=company,
                    ).first()
                else:
                    last_step = ApproveFlowStep.objects.filter(
                        status=ApproveFlowStepStatusChoices.OPEN,  # Avoid Step with duplicate status
                        management_sheet=obj,
                        approve_flow_setting_instance=approve_flow_setting_instance,
                        company=company,
                        level=level,
                    ).first()
            else:
                last_step = None

            if last_step:
                if last_step.status == ApproveFlowStepStatusChoices.OPEN:
                    list_actions.extend(["approve", "reject", "return"])
                if (
                    last_step.status == ApproveFlowStepStatusChoices.OPEN
                    and approve_flow_setting_instance.is_signed_by_dealer
                    and (
                        (
                            max_level == level
                            and approve_flow_setting_instance.is_approve_level_dealer
                        )
                        or not approve_flow_setting_instance.is_approve_level_dealer
                    )
                    and company.type == CompanyType.DEALER
                ):
                    list_actions.append("signed_by_dealer")
                if (
                    last_step.status == ApproveFlowStepStatusChoices.OPEN
                    and approve_flow_setting_instance.is_signed_by_act
                    and company.type == CompanyType.ACT
                    and (
                        (
                            max_level == level
                            and approve_flow_setting_instance.is_approve_level_act
                        )
                        or not approve_flow_setting_instance.is_approve_level_act
                    )
                ):
                    list_actions.append("signed_by_act")
            else:
                if (
                    company.type == CompanyType.SHOP
                    and obj.status
                    in (ManagementSheetStatus.TEMP, ManagementSheetStatus.RETURN)
                    # and obj.approve_flow_setting is not None
                ):
                    list_actions.append("approve")
        return list_actions

    def get_comments(self, obj):
        shop_comment, dealer_comment, act_comment, admin_comment = (None,) * 4
        if (
            hasattr(obj, "management_sheet_approve_flow_setting_instance")
            and obj.management_sheet_approve_flow_setting_instance
        ):
            approve_flow_instance = obj.management_sheet_approve_flow_setting_instance
            shop_comment = (
                ApproveFlowStep.objects.filter(
                    company=approve_flow_instance.shop, management_sheet=obj
                )
                .only("comment")
                .first()
            )
            if shop_comment:
                # Get dealer comment
                dealer_comment = (
                    ApproveFlowStep.objects.filter(
                        company=approve_flow_instance.dealer,
                        management_sheet=obj,
                        pk__gt=shop_comment.id,
                    )
                    .only("comment", "level")
                    .all()
                )
                dealer_comment_serializer = GetCommentAndLevelSerializer(
                    dealer_comment, many=True
                ).data
                # Get act comment
                act_comment = (
                    ApproveFlowStep.objects.filter(
                        company=approve_flow_instance.act,
                        management_sheet=obj,
                        pk__gt=shop_comment.id,
                    )
                    .only("comment", "level", "user")
                    .all()
                )
                act_comment_serializer = GetCommentAndLevelSerializer(
                    act_comment, many=True
                ).data

                # Get admin comment
                admin_comment = ApproveFlowStep.objects.filter(
                    management_sheet=obj,
                    pk__gt=shop_comment.id,
                    is_admin_approved=True
                ).only("comment", "level", "user")
                admin_comment_serializer = GetCommentAndLevelSerializer(
                    admin_comment, many=True
                ).data

        comments = {
            "shop": shop_comment.comment if shop_comment else None,
            "dealer": dealer_comment_serializer if dealer_comment else None,
            "act": act_comment_serializer if act_comment else None,
            "admin": admin_comment_serializer if admin_comment else None,
        }
        return comments

    def get_electronic_mark(self, obj):

        user_list_electronic_mark = UserElectronicMark.objects.filter(
            management_sheet=obj
        )
        serializer = ElectronicMarkSerializer(user_list_electronic_mark, many=True)

        return serializer.data

    def get_owner_electronic_mark(self, obj):
        owner_electronic_mark = OwnerElectronicMark.objects.filter(
            management_sheet=obj
        ).first()
        if owner_electronic_mark:
            return owner_electronic_mark.image_base64
        return None


class UpdateManagementSheetSerialzer(serializers.ModelSerializer):
    type = serializers.ListField(child=serializers.CharField())
    actual_constructions = serializers.ListField(
        child=MsActualConstructionUpdateSerialize(), write_only=True
    )
    approve_flow_setting_id = serializers.IntegerField(required=False, allow_null=True)
    latest_updated_at = serializers.DateTimeField(read_only=True)

    class Meta:
        model = ManagementSheet
        fields = [
            "id",
            "cms_uuid",
            "construction_no",
            "type",
            "contractor_company",
            "construction_zipcode",
            "city",
            "full_address",
            "prefecture",
            "building_name",
            "client_name",
            "tel",
            "number_of_guide_to_using",
            "warranty_registration_no",
            "completed_date",
            "expect_approve_completed_date",
            "is_agree_info_above",
            "is_agree_to_send_survey",
            "building_owner_name",
            "start_warranty_date",
            "ten_years_end_warranty_date",
            "fifteen_years_end_warranty_date",
            "approve_flow_setting_id",
            "actual_constructions",
            "approve_shop_comment",
            "expected_approve_date",
            "approved_at",
            "latest_updated_at",
        ]

    def validate(self, attrs):
        try:
            if approve_flow_setting_id := attrs.get("approve_flow_setting_id", None):
                ApproveFlowSetting.objects.get(id=approve_flow_setting_id)
            return super().validate(attrs)
        except ApproveFlowSetting.DoesNotExist:
            raise serializers.ValidationError(
                {"related_fields": "Approve Flow Setting not found"}
            )

    def update(self, instance, validated_data):
        def create_new_approve_flow_setting_instance(approve_flow_setting_id):
            if approve_flow_setting_id:
                approve_flow_setting = ApproveFlowSetting.objects.get(
                    pk=approve_flow_setting_id
                )
                approve_flow_setting_instance = ApproveFlowSettingInstance(
                    name=approve_flow_setting.name,
                    type=approve_flow_setting.type,
                    shop=approve_flow_setting.shop,
                    dealer=approve_flow_setting.dealer,
                    act=approve_flow_setting.act,
                    is_approve_level_dealer=approve_flow_setting.is_approve_level_dealer,
                    is_approve_level_act=approve_flow_setting.is_approve_level_act,
                    is_signed_by_shop=approve_flow_setting.is_signed_by_shop,
                    is_signed_by_dealer=approve_flow_setting.is_signed_by_dealer,
                    is_signed_by_act=approve_flow_setting.is_signed_by_act,
                    ref_setting=approve_flow_setting,
                    management_sheet=instance,
                )
                approve_flow_setting_instance.save()

        # Remove status out of MS from payload
        validated_data.pop("status", None)
        actual_construction_datas = validated_data.pop("actual_constructions", None)
        for actual_construction_data in actual_construction_datas:
            actual_construction_methods = actual_construction_data.pop(
                "actual_construction_methods", None
            )

            # Update or create actual construction
            diagnostic_cm_id = actual_construction_data.pop("diagnostic_construction_method_id", None)
            actual_construction, __ = ActualConstruction.objects.update_or_create(
                management_sheet=instance,
                diagnostic_construction_method_id=diagnostic_cm_id,
                defaults={**actual_construction_data},
            )

            # update or create actual construction method
            for at_cms in actual_construction_methods:
                ActualConstructionMethod.objects.update_or_create(
                    actual_construction=actual_construction,
                    type=at_cms["type"],
                    material_id=at_cms["material_id"],
                    defaults={
                        "construction_time": at_cms["construction_time"],
                        "number_of_crates": at_cms["number_of_crates"],
                        "amount_of_use": at_cms["amount_of_use"],
                    },
                )

        # Create Instance for ApproveFlowSetting
        approve_flow_setting_id = validated_data.get("approve_flow_setting_id", None)
        # If instance has approve_flow_setting
        if instance.approve_flow_setting:
            # If current approve_flow_setting difference with new approve_flow_setting
            if (
                approve_flow_setting_id
                and approve_flow_setting_id != instance.approve_flow_setting.id
            ):
                ApproveFlowSettingInstance.objects.filter(
                    management_sheet=instance
                ).update(management_sheet=None)
                create_new_approve_flow_setting_instance(approve_flow_setting_id)
        else:
            create_new_approve_flow_setting_instance(approve_flow_setting_id)

        super().update(instance, validated_data)

        # Update owner name and building name for SDS
        Diagnostic.objects.filter(
            Q(cms_uuid=instance.cms_uuid), ~Q(status=DiagnosticStatus.REJECT)
        ).update(
            building_name=validated_data["building_name"],
            construction_owner=validated_data["client_name"],
        )

        return GetManagementSheetDetailSerialzer(
            instance, context={"user": self.context["user"]}
        ).data


class ManagementSheetRequestApproveSerializer(serializers.Serializer):
    approve_flow_setting_id = serializers.IntegerField()
    expected_approve_date = serializers.DateField()


class ManagementSheetApproveFLowSerializer(serializers.Serializer):
    comment = serializers.CharField(required=False, allow_blank=True)
    latest_updated_at = serializers.DateTimeField(required=False, read_only=True)

    def validate(self, attrs):
        management_sheet = self.context["management_sheet"]
        action = self.context["action"]
        if action not in ["approve", "reject", "return"]:
            raise ValidationError({"detail": "Action is invalid"})
        ManagementSheetRequestApproveSerializer(
            data={
                "approve_flow_setting_id": management_sheet.approve_flow_setting_id,
                "expected_approve_date": management_sheet.expected_approve_date,
            }
        ).is_valid(raise_exception=True)
        return super().validate(attrs)


class ManagementSheetCommonInfoApproveHistorySerializer(serializers.ModelSerializer):
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    document_type = serializers.CharField(read_only=True, default="MS")
    application_date = serializers.SerializerMethodField(read_only=True)
    application_name = serializers.SerializerMethodField(read_only=True)
    owner_name = serializers.SerializerMethodField(read_only=True)
    owner_id = serializers.SerializerMethodField(read_only=True)
    construction_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ManagementSheet
        fields = (
            "approve_flow_setting",
            "pic",
            "status",
            "building_owner_name",
            "status",
            "document_type",
            "application_date",
            "application_name",
            "cms_uuid",
            "construction_name",
            "owner_name",
            "owner_id",
        )

    def get_application_date(self, obj):

        user_mark = UserElectronicMark.objects.filter(
            management_sheet=obj, user_role_type=UserRoleType.SHOP
        ).first()

        created_at = ""
        if user_mark is not None:
            user_request = ApproveFlowStep.objects.filter(
                management_sheet=obj,
                user=user_mark.user,
                status=ApproveFlowStepStatusChoices.APPROVED,
            ).first()
            created_at = user_request.created_at

        return created_at

    def get_application_name(self, obj):

        user_mark = UserElectronicMark.objects.filter(
            management_sheet=obj, user_role_type=UserRoleType.SHOP
        ).first()

        application_name = ""
        if user_mark:
            application_name = user_mark.user.kanna_user.name
        else:

            # if MS return
            if obj.status == ManagementSheetStatus.RETURN:
                approve_flow_step = ApproveFlowStep.objects.filter(
                    management_sheet=obj,
                    status=ApproveFlowStepStatusChoices.APPROVED,
                    company__type=CompanyType.SHOP,
                ).last()
                application_name = approve_flow_step.user.kanna_user.name

        return application_name

    def get_owner_name(self, obj):
        owner_cms = UserConstruction.objects.filter(
            construction_uuid=obj.cms_uuid
        ).first()
        owner_name = ""
        if owner_cms:
            owner = User.objects.filter(id=owner_cms.owner_id).first()
            owner_name = owner.last_name + owner.first_name

        return owner_name

    def get_owner_id(self, obj):
        owner_cms = UserConstruction.objects.filter(
            construction_uuid=obj.cms_uuid
        ).first()
        owner_id = ""
        if owner_cms:
            owner_id = owner_cms.owner_id

        return owner_id

    def get_construction_name(self, obj):
        construction_name = ""
        if "request" in self.context:
            cms = get_cms_kanna(obj.cms_uuid, self.context["request"].user.kanna_user)
            construction_name = cms.get("title")
        return construction_name


class ManagementSheetApproveHistorySerializer(serializers.ModelSerializer):
    # approve_flow_setting_instance = ApproveFlowSettingSerializer(read_only=True)
    # company = CompanySerializer(read_only=True)
    user = UserKannaLiteSerializer(read_only=True)
    role_type = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ApproveFlowStep
        exclude = (
            "approve_flow_setting_instance",
            "company",
        )

    def get_role_type(self, obj):
        if obj.user:
            if obj.user.kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                return KANNA_USER_ROLE["admin"]
        return obj.company.type


class UserKannaLiteSerializer(serializers.ModelSerializer):
    kanna_user = UserKannaSerializer(read_only=True)

    class Meta:
        model = User
        fields = ("kanna_user",)


class ManagementSheetRequestFlowApproveSerializer(serializers.ModelSerializer):
    user = UserLiteSerializer(read_only=True)

    class Meta:
        model = ApproveFlowStep
        fields = (
            "created_at",
            "user",
        )


class ManagementSheetApproveByConstructionSerializer(serializers.ModelSerializer):
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    user_request_approve = serializers.SerializerMethodField(read_only=True)
    approve_date = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = (
            "id",
            "user_request_approve",
            "status",
            "approve_flow_setting",
            "approve_date",
        )
        extra_kwargs = {}

    def get_user_request_approve(self, obj):
        user_request = UserElectronicMark.objects.filter(
            management_sheet=obj, user_role_type=UserRoleType.SHOP
        ).first()
        approve_flow_steps = ""
        if user_request:
            approve_flow_steps = ApproveFlowStep.objects.filter(
                management_sheet=obj,
                status=ApproveFlowStepStatusChoices.APPROVED,
                user=user_request.user,
            ).first()
        # if MS return
        if obj.status == ManagementSheetStatus.RETURN:
            approve_flow_steps = ApproveFlowStep.objects.filter(
                management_sheet=obj,
                status=ApproveFlowStepStatusChoices.APPROVED,
                company__type=CompanyType.SHOP,
            ).last()

        serializer = ManagementSheetRequestFlowApproveSerializer(approve_flow_steps)

        return serializer.data

    def get_approve_date(self, obj):

        approve_date = (
            ApproveFlowStep.objects.values_list("updated_at", flat=True)
            .filter(
                Q(management_sheet__id=obj.id),
                ~Q(status=ApproveFlowStepStatusChoices.OPEN),
            )
            .first()
        )

        return approve_date


class ManagementSheetTemporaryUpdateSerializer(serializers.ModelSerializer):
    type = serializers.ListField(child=serializers.CharField())
    actual_constructions = serializers.ListField(
        child=MsActualConstructionUpdateSerialize(), write_only=True
    )
    approve_flow_setting_id = serializers.IntegerField(required=False, allow_null=True)
    latest_updated_at = serializers.DateTimeField(read_only=True)

    class Meta:
        model = ManagementSheet
        fields = [
            "id",
            "cms_uuid",
            "construction_no",
            "type",
            "contractor_company",
            "construction_zipcode",
            "city",
            "full_address",
            "prefecture",
            "building_name",
            "client_name",
            "tel",
            "number_of_guide_to_using",
            "warranty_registration_no",
            "completed_date",
            "expect_approve_completed_date",
            "is_agree_info_above",
            "is_agree_to_send_survey",
            "building_owner_name",
            "start_warranty_date",
            "ten_years_end_warranty_date",
            "fifteen_years_end_warranty_date",
            "approve_flow_setting_id",
            "actual_constructions",
            "approve_shop_comment",
            "expected_approve_date",
            "approved_at",
            "latest_updated_at",
        ]

    def update(self, instance, validated_data):
        # Remove status, actual_construction out of MS from payload
        validated_data.pop("status", None)
        actual_construction_datas = validated_data.pop("actual_constructions", None)

        for actual_construction_data in actual_construction_datas:
            actual_construction_methods = actual_construction_data.pop(
                "actual_construction_methods", None
            )

            # Update or create actual construction
            diagnostic_cm_id = actual_construction_data.pop("diagnostic_construction_method_id", None)
            actual_construction, __ = ActualConstruction.objects.update_or_create(
                management_sheet=instance,
                diagnostic_construction_method_id=diagnostic_cm_id,
                defaults={**actual_construction_data},
            )

            # update or create actual construction method
            for at_cms in actual_construction_methods:
                ActualConstructionMethod.objects.update_or_create(
                    actual_construction=actual_construction,
                    type=at_cms["type"],
                    material_id=at_cms["material_id"],
                    defaults={
                        "construction_time": at_cms.get("construction_time", None),
                        "number_of_crates": at_cms.get("number_of_crates", None),
                        "amount_of_use": at_cms.get("amount_of_use", None),
                    },
                )

        approve_flow_setting_id = validated_data.get("approve_flow_setting_id", None)

        # Update or create approve flow setting instance
        if approve_flow_setting_id:
            try:
                approve_flow_setting = ApproveFlowSetting.objects.get(
                    pk=approve_flow_setting_id
                )
                ApproveFlowSettingInstance.objects.update_or_create(
                    management_sheet=instance,
                    defaults={
                        "name": approve_flow_setting.name,
                        "type": approve_flow_setting.type,
                        "shop": approve_flow_setting.shop,
                        "dealer": approve_flow_setting.dealer,
                        "act": approve_flow_setting.act,
                        "is_approve_level_dealer": approve_flow_setting.is_approve_level_dealer,
                        "is_approve_level_act": approve_flow_setting.is_approve_level_act,
                        "is_signed_by_shop": approve_flow_setting.is_signed_by_shop,
                        "is_signed_by_dealer": approve_flow_setting.is_signed_by_dealer,
                        "is_signed_by_act": approve_flow_setting.is_signed_by_act,
                        "ref_setting": approve_flow_setting,
                    },
                )
            except ApproveFlowSetting.DoesNotExist:
                logger.info("ApproveFlowSetting not found")

        super().update(instance, validated_data)
        return GetManagementSheetDetailSerialzer(
            instance, context={"user": self.context["user"]}
        ).data
