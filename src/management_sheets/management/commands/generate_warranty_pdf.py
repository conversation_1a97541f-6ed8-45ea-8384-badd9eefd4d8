import logging
from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q

from management_sheets.models import ManagementSheet, ActualConstruction
from management_sheets.choices import ManagementSheetStatus
from warranty.models import Warranty, WarrantyYearChoices
from kanna_api.models.user_kanna import UserKanna
from approve_flow.models import ApproveFlowStep, ApproveFlowStepStatusChoices
from approve_flow.services import render_pdf_to_agc, render_to_pdf_and_send_to_kanna
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)

# Only generate PDF
# python manage.py generate_warranty_pdf 1234

# Generate PDF and send to Kanna
# python manage.py generate_warranty_pdf 1234 --send-to-kanna
class Command(BaseCommand):
    help = "Generate warranty PDF files for a management sheet"

    def add_arguments(self, parser):
        parser.add_argument(
            "management_sheet_id", type=int, help="ID of the management sheet"
        )
        parser.add_argument(
            "--send-to-kanna",
            action="store_true",
            help="Also send the PDF to Kanna system",
        )

    def handle(self, *args, **options):
        management_sheet_id = options["management_sheet_id"]
        send_to_kanna = options["send_to_kanna"]

        # Get management sheet
        management_sheet = self._get_management_sheet(management_sheet_id)

        # Check if management sheet is approved
        if not self._is_management_sheet_approved(management_sheet):
            self.stdout.write(
                self.style.ERROR(
                    f"Management sheet with ID {management_sheet_id} is not approved. Current status: {management_sheet.status}"
                )
            )
            return

        # Get the last approver
        kanna_user = self._get_last_approver(management_sheet)
        if not kanna_user:
            return

        # Display existing warranties (if any)
        self._display_existing_warranties(management_sheet)

        # Get actual constructions for different warranty types
        ten_year_constructions = self._get_ten_year_constructions(management_sheet)
        fifteen_year_constructions = self._get_fifteen_year_constructions(
            management_sheet
        )

        # Generate warranties
        self._generate_warranty(
            management_sheet,
            kanna_user,
            ten_year_constructions,
            WarrantyYearChoices.TEN_YEARS,
            send_to_kanna,
        )

        self._generate_warranty(
            management_sheet,
            kanna_user,
            fifteen_year_constructions,
            WarrantyYearChoices.FIFTEEN_YEARS,
            send_to_kanna,
        )

        self.stdout.write(self.style.SUCCESS("Warranty PDF generation completed"))

    def _get_management_sheet(self, management_sheet_id: int) -> ManagementSheet:
        """Get the management sheet object"""
        try:
            management_sheet = ManagementSheet.objects.get(id=management_sheet_id)
            self.stdout.write(
                f"Found management sheet: {management_sheet.id} (CMS UUID: {management_sheet.cms_uuid})"
            )
            return management_sheet
        except ManagementSheet.DoesNotExist:
            raise CommandError(
                f"Management sheet with ID {management_sheet_id} does not exist"
            )

    def _is_management_sheet_approved(self, management_sheet: ManagementSheet) -> bool:
        """Check if the management sheet is approved"""
        return management_sheet.status == ManagementSheetStatus.APPROVED

    def _get_last_approver(
        self, management_sheet: ManagementSheet
    ) -> Optional[UserKanna]:
        """Get the last approver of the management sheet"""
        last_approve_step = (
            ApproveFlowStep.objects.filter(
                management_sheet=management_sheet,
                status=ApproveFlowStepStatusChoices.APPROVED,
            )
            .order_by("-created_at")
            .first()
        )

        if not last_approve_step or not last_approve_step.user:
            self.stdout.write(
                self.style.ERROR(
                    "Could not find last approver for this management sheet."
                )
            )
            return None

        kanna_user = last_approve_step.user.kanna_user
        self.stdout.write(
            f"Using last approver: {kanna_user.user.get_full_name()} ({kanna_user.email})"
        )
        return kanna_user

    def _display_existing_warranties(self, management_sheet: ManagementSheet) -> None:
        """Display information about existing warranties"""
        existing_warranties = Warranty.objects.filter(management_sheet=management_sheet)
        if existing_warranties.exists():
            self.stdout.write(
                self.style.WARNING(
                    f"Found existing warranties for this management sheet. They will be updated."
                )
            )
            for warranty in existing_warranties:
                self.stdout.write(
                    f"  - {warranty.get_warranty_year_type_display()} warranty (ID: {warranty.id})"
                )

    def _get_ten_year_constructions(
        self, management_sheet: ManagementSheet
    ) -> List[ActualConstruction]:
        """Get actual constructions eligible for 10-year warranty"""
        actual_constructions = (
            management_sheet.actual_constructions.filter(
                Q(
                    diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.TEN_YEARS,
                )
                | Q(
                    diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
                    is_change_15_years_to_10_years=True,
                ),
                is_approved_to_release_warranty=True,
            )
            .select_related("diagnostic_construction_method")
            .order_by("id")
        )
        return list(actual_constructions)

    def _get_fifteen_year_constructions(
        self, management_sheet: ManagementSheet
    ) -> List[ActualConstruction]:
        """Get actual constructions eligible for 15-year warranty"""
        actual_constructions = (
            management_sheet.actual_constructions.filter(
                diagnostic_construction_method__construction_method__warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
                is_approved_to_release_warranty=True,
                is_change_15_years_to_10_years=False,
            )
            .select_related("diagnostic_construction_method")
            .order_by("id")
        )
        return list(actual_constructions)

    def _generate_warranty(
        self,
        management_sheet: ManagementSheet,
        kanna_user: UserKanna,
        actual_constructions: List[ActualConstruction],
        warranty_year_type: str,
        send_to_kanna: bool,
    ) -> None:
        """Generate warranty PDF for the specified warranty year type"""
        year_display = (
            "10-year"
            if warranty_year_type == WarrantyYearChoices.TEN_YEARS
            else "15-year"
        )

        if not actual_constructions:
            self.stdout.write(
                self.style.WARNING(
                    f"No constructions eligible for {year_display} warranty"
                )
            )
            return

        self.stdout.write(f"Generating {year_display} warranty PDF...")
        warranty = Warranty.objects.get(
            management_sheet=management_sheet, warranty_year_type=warranty_year_type
        )
        self.stdout.write(f"Using existing {year_display} warranty (ID: {warranty.id})")

        try:
            # Generate PDF
            self._generate_warranty_pdf(
                warranty, kanna_user, actual_constructions, management_sheet.cms_uuid
            )

            # Send to Kanna if requested
            if send_to_kanna:
                self._send_warranty_to_kanna(
                    warranty,
                    kanna_user,
                    actual_constructions,
                    management_sheet.cms_uuid,
                    year_display,
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f"Error generating {year_display} warranty PDF: {str(e)}"
                )
            )
            logger.exception(f"Error generating {year_display} warranty PDF")

    def _generate_warranty_pdf(
        self,
        warranty: Warranty,
        kanna_user: UserKanna,
        actual_constructions: List[ActualConstruction],
        cms_uuid: str,
    ) -> None:
        """Generate warranty PDF and save URL"""
        warranty_url = render_pdf_to_agc(
            kanna_user,
            warranty,
            actual_constructions,
            cms_uuid,
        )
        warranty.pdf_url = warranty_url
        warranty.save()
        self.stdout.write(self.style.SUCCESS(f"Generated warranty PDF: {warranty_url}"))

    def _send_warranty_to_kanna(
        self,
        warranty: Warranty,
        kanna_user: UserKanna,
        actual_constructions: List[ActualConstruction],
        cms_uuid: str,
        year_display: str,
    ) -> None:
        """Send warranty PDF to Kanna"""
        self.stdout.write(f"Sending {year_display} warranty PDF to Kanna system...")
        render_to_pdf_and_send_to_kanna(
            kanna_user,
            warranty,
            actual_constructions,
            cms_uuid,
        )
        self.stdout.write(
            self.style.SUCCESS(f"Sent {year_display} warranty PDF to Kanna system")
        )
