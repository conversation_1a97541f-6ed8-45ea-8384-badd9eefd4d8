from django.db import models
from django.utils.translation import gettext_lazy as _


class ManagementSheetStatus(models.TextChoices):
    TEMP = "TEMP", _("Temporary")
    WAITING_APPROVAL = "WAITING_APPROVAL", _("Waiting for approval")
    APPROVED = "APPROVED", _("Approval")
    REJECT = "REJECT", _("Reject")
    RETURN = "RETURN", _("Return")


class ManagementSheetType(models.TextChoices):
    NEW_CONSTRUCTION = "NEW_CONSTRUCTION"
    RENOVATION = "RENOVATION"
