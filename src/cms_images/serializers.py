from rest_framework import serializers
from rest_framework.fields import Serializer<PERSON>ethod<PERSON>ield
from rest_framework.serializers import (
    <PERSON>oleanField,
    CharField,
    ImageField,
    IntegerField,
    URLField,
    UUIDField,
)


class ImageSeriarizer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()
    updated_at = CharField()
    image_url = URLField()
    image_thumb_url = URLField()
    is_blackboard = BooleanField()
    is_in_root_folder = SerializerMethodField()
    image_category = serializers.DictField(allow_null=True)

    def get_is_in_root_folder(self, obj):
        root_uuids = self.context.get("root_img_diagnostic_type_uuids", [])
        image_category = obj.pop("image_category", None)

        return image_category and image_category.get("uuid") in root_uuids


class ImageCategorySerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()
    total_count = IntegerField()
    updated_at = Cha<PERSON><PERSON><PERSON>()


class CmsImageListSerializer(serializers.Serializer):
    image_categories = ImageCategorySerializer(many=True)
    images = ImageSeriarizer(many=True)


class CmsImagePageSerializer(serializers.Serializer):
    total_count = IntegerField()
    results = CmsImageListSerializer()


class CmsImageDetailCategorySerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()


class CmsImageDetailSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()
    image_url = URLField()
    image_thumb_url = URLField()
    shooting_at = CharField(required=False)
    note = CharField(required=False)
    cms_uuid = UUIDField()
    created_at = CharField()
    updated_at = CharField()
    is_blackboard = BooleanField()
    image_category = CmsImageDetailCategorySerializer()


class CreateImageRequestSerializer(serializers.Serializer):
    file = ImageField()


class UpdateImageRequestSerializer(serializers.Serializer):
    file_name = CharField()
    comment = CharField(required=False)
    move_category_uuid = UUIDField()


class AllCategorySerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()
