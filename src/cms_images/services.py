from __future__ import annotations

import concurrent.futures
import logging

from kanna_api.cms_image import get_images, get_image

logger = logging.getLogger(__name__)


def mold_children(
    response_data: list,
    child_image_category_list: list,
    parent_image_category_uuids: list,
):
    children = []
    for child in child_image_category_list:
        child_image_category = {
            "uuid": child.get("uuid"),
            "name": child.get("name"),
        }
        if child.get("uuid") in parent_image_category_uuids:
            decendants = list(
                filter(
                    lambda child_data: child_data.get("parent_image_category_uuid")
                    == child.get("uuid"),
                    response_data,
                )
            )
            child_image_category["children"] = mold_children(
                response_data, decendants, parent_image_category_uuids
            )
        else:
            child_image_category["children"] = []

        children.append(child_image_category)

    return children


def fetch_images_concurrently(kanna_user, cms_uuid, img_category_uuids):
    images = []

    # Using ThreadPoolExecutor to parallelize the requests
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_to_uuid = {
            executor.submit(get_images, kanna_user, cms_uuid, uuid): uuid
            for uuid in img_category_uuids
        }

        for future in concurrent.futures.as_completed(future_to_uuid):
            try:
                root_category_images = future.result()
                images.extend(root_category_images)
            except Exception as e:
                logger.error(f"Error fetching images for UUID {future_to_uuid[future]}: {e}")

    return images


def fetch_image_concurrently(
        user,
        cms_uuid,
        image_uuids=None,
        image_uuids_by_cate_uuid: dict[str, list[str]] = None,
):
    """
    Fetches images concurrently based on provided parameters.

    Parameters:
    user: The user requesting the images.
    cms_uuid: The unique identifier for the CMS.
    image_uuids_by_cate_uuid: A dictionary where the keys are category UUIDs and the values are lists of image UUIDs. Default is None.
    image_uuids: A list of image UUIDs. Default is None.

    Returns:
    The result from the appropriate image fetching function based on the provided parameters.

    Raises:
    ValueError: If neither image_uuids_by_cate_uuid nor image_uuids are provided.
    """
    if image_uuids_by_cate_uuid:
        return _fetch_images_by_category(user, cms_uuid, image_uuids_by_cate_uuid)
    elif image_uuids:
        return _fetch_images(user, cms_uuid, image_uuids)
    else:
        print("Either image_uuids_by_cate_uuid or image_uuids must be provided")


def _fetch_images_by_category(user, cms_uuid, img_uuids_by_cate_uuid: dict[str, list[str]]):
    results = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = {}
        for image_category_uuid, image_uuids in img_uuids_by_cate_uuid.items():
            for image_uuid in image_uuids:
                future = executor.submit(get_image, user, cms_uuid, image_category_uuid, image_uuid)
                futures[future] = (image_category_uuid, image_uuid)

        for future in concurrent.futures.as_completed(futures):
            image_category_uuid, image_uuid = futures[future]
            try:
                result = future.result()
                if image_category_uuid not in results:
                    results[image_category_uuid] = {}
                results[image_category_uuid][image_uuid] = result
            except Exception as e:
                print(f"Error fetching image {image_uuid} in category {image_category_uuid}: {e}")
    return results


def _fetch_images(user, cms_uuid, image_uuids: list[str]):
    results = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(
                get_image,
                user,
                cms_uuid,
                None,
                image_uuid
            ): image_uuid for image_uuid in image_uuids
        }

        for future in concurrent.futures.as_completed(futures):
            image_uuid = futures[future]
            try:
                results[image_uuid] = future.result()
            except Exception as e:
                print(f"Error fetching image {image_uuid}: {e}")
    return results
