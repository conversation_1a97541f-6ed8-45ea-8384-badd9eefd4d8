from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from cms_images.services import mold_children, fetch_images_concurrently
from kanna_api.cms_image import (
    cms_image_filter,
    get_image,
    get_image_categories,
    get_image_category,
)
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.cms_image import CmsImageParam
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import Has<PERSON><PERSON>Permission
from .serializers import (
    AllCategorySerializer,
    CmsImageDetailSerializer,
    CmsImagePageSerializer,
    ImageCategorySerializer,
)


class CmsImageViewSet(viewsets.GenericViewSet):
    permission_classes = [HasKannaPermission]

    def get_serializer_class(self):
        if self.action == "cms_image_list":
            return CmsImagePageSerializer
        elif self.action == "cms_image_category_detail":
            return ImageCategorySerializer
        elif self.action in ["cms_image_detail"]:
            return CmsImageDetailSerializer
        elif self.action == "get_all_categories":
            return AllCategorySerializer

        return super().get_serializer_class()

    @action(
        methods=["get"], detail=False, url_path="(?P<cms_uuid>[a-zA-Z0-9\-_]+)"  # noqa
    )
    @swagger_auto_schema(
        manual_parameters=CmsImageParam.yasg_params(), tags=["cms_image"]
    )
    def cms_image_list(self, request, cms_uuid: str, *args, **kwargs):
        """
        写真フォルダ一覧
        """

        root_category_folder_names = {
            "ROOF": "屋根",
            "OUTER_WALL": "外壁"
        }

        kanna_user = UserKanna.objects.get(email=request.user.email)
        params = CmsImageParam(**request.query_params.dict())
        response = get_image_categories(kanna_user, cms_uuid)
        categories_response_data = camel_to_snake_json(response)
        image_categories = cms_image_filter(params, categories_response_data)
        results = {"image_categories": image_categories, "images": []}
        root_img_diagnostic_type_uuids = []
        img_category_uuids = []

        # Get all images in the root folder based on diagnostic_type (屋根 or 外壁) and images in parent category
        if diagnostic_type_name := root_category_folder_names.get(params.diagnostic_type):
            root_img_diagnostic_type_uuids = [
                item.get("uuid") for item in categories_response_data
                if item.get("name") == diagnostic_type_name
            ]
            img_category_uuids.extend(root_img_diagnostic_type_uuids)

        if params.parent_image_category_uuid:
            img_category_uuids.append(params.parent_image_category_uuid)

        images = fetch_images_concurrently(
            kanna_user=kanna_user,
            cms_uuid=cms_uuid,
            img_category_uuids=img_category_uuids
        )

        results["images"] = camel_to_snake_json(images)

        serializer = self.get_serializer(
            data={"total_count": len(image_categories) + len(images), "results": results},
            context={"root_img_diagnostic_type_uuids": root_img_diagnostic_type_uuids}
        )
        serializer.is_valid(raise_exception=True)

        return Response(data=serializer.data)

    @action(
        methods=["get"],
        detail=False,
        url_path="(?P<cms_uuid>[a-zA-Z0-9\-_]+)/image_categories/(?P<image_category_uuid>[a-zA-Z0-9\-_]+)",  # noqa
    )
    def cms_image_category_detail(
        self, request, cms_uuid, image_category_uuid, *args, **kwargs
    ):
        """
        写真フォルダ詳細
        """
        kanna_user = UserKanna.objects.get(email=request.user.email)
        response = get_image_category(kanna_user, cms_uuid, image_category_uuid)
        response_data = camel_to_snake_json(response.get("imageCategory"))

        serializer = self.get_serializer(data=response_data)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)

    @action(
        methods=["get"],
        detail=False,
        url_path="(?P<cms_uuid>[a-zA-Z0-9\-_]+)/image_categories/(?P<image_category_uuid>[a-zA-Z0-9\-_]+)/image/(?P<image_uuid>[a-zA-Z0-9\-_]+)",  # noqa
    )
    def cms_image_detail(
        self, request, cms_uuid, image_category_uuid, image_uuid, *args, **kwargs
    ):
        """
        写真詳細
        """
        kanna_user = UserKanna.objects.get(email=request.user.email)
        response = get_image(kanna_user, cms_uuid, image_category_uuid, image_uuid)
        response_data = camel_to_snake_json(response.get("image"))

        serializer = self.get_serializer(data=response_data)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)

    @action(
        methods=["get"],
        detail=False,
        url_path="(?P<cms_uuid>[a-zA-Z0-9\-_]+)/all_categories",  # noqa
    )
    def get_all_categories(self, request, cms_uuid, *args, **kwargs):
        """
        写真フォルダ全件取得
        """
        kanna_user = UserKanna.objects.get(email=request.user.email)
        try:
            response = get_image_categories(kanna_user, cms_uuid)
        except Exception:
            return Response(
                {"error": "cms not found"}, status=status.HTTP_404_NOT_FOUND
            )
        response_data = camel_to_snake_json(response)
        serializer = self.get_serializer(data=response_data, many=True)
        serializer.is_valid(raise_exception=True)

        parent_image_category_uuids = [
            image_category.get("parent_image_category_uuid")
            for image_category in response_data
        ]
        parent_image_category_list = list(
            filter(
                lambda parent: not parent.get("parent_image_category_uuid"),
                response_data,
            )
        )

        # ネストした状態に整型
        molded_data = []
        for parent in parent_image_category_list:
            child_image_category_list = list(
                filter(
                    lambda child: child.get("parent_image_category_uuid")
                    == parent.get("uuid"),
                    response_data,
                )
            )

            molded_data.append(
                {
                    "uuid": parent.get("uuid"),
                    "name": parent.get("name"),
                    "children": mold_children(
                        response_data,
                        child_image_category_list,
                        parent_image_category_uuids,
                    ),
                }
            )

        return Response(molded_data)
