from rest_framework import generics, permissions, status, viewsets
from rest_framework.response import Response

from django.db.models import Q

from utilities.permissions.custom_permission import HasKannaPermission

from .models import Template, TemplateStatus
from .serializers import (
    TemplateSerializer,
    UpdateTemplateStatusToActiveSerializer,
)


class TemplateViewSet(viewsets.ModelViewSet):
    serializer_class = TemplateSerializer
    http_method_names = ["get", "post", "put", "delete"]
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Template.objects.order_by("status").all()
        query_param = self.request.query_params
        if "name" in query_param:
            queryset = queryset.filter(Q(display_name__icontains=query_param["name"]))
        if "business_type" in query_param:
            queryset = queryset.filter(business_type=query_param["business_type"])
        if "sub_type" in query_param:
            queryset = queryset.filter(sub_type=query_param["sub_type"])
        if "status" in query_param:
            queryset = queryset.filter(status=query_param["status"])

        return queryset


class SetActiveTemplateApiView(generics.UpdateAPIView):
    queryset = Template.objects.all()
    serializer_class = UpdateTemplateStatusToActiveSerializer
    http_method_names = ["put"]
    permission_classes = [HasKannaPermission]

    def update_template_status_to_active(self, instance: Template):
        # Update all templates have same business_type to INACTIVE
        Template.objects.filter(business_type=instance.business_type).filter(
            sub_type=instance.sub_type
        ).update(status=TemplateStatus.INACTIVE.value)
        # Save current template status is ACTIVE
        instance.status = TemplateStatus.ACTIVE.value
        instance.save()
        return instance

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        instance_after_update = self.update_template_status_to_active(instance)
        serializer = self.serializer_class(data=instance_after_update)
        serializer.is_valid()
        return Response(data=serializer.data, status=status.HTTP_200_OK)
