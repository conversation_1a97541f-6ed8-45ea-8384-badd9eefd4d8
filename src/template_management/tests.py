from rest_framework.test import APITestCase

from django.urls import reverse

from .factories import TemplateFactory
from .models import BusinessSubTypes, BusinessTypes, TemplateStatus


class TemplateAPITests(APITestCase):
    def test_get_template_list(self):
        # Create 3 Template
        TemplateFactory.create_batch(3)
        url = reverse("templates-list", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(len(data["results"]), 3)

    def test_create_incorrect_template(self):
        url = reverse("templates-list", kwargs={"version": "v1"})
        body = {
            "display_name": "new-template",
            "description": "test-template",
            "business_type": BusinessTypes.DIAGNOSTIC.value,
        }
        response = self.client.post(url, data=body)
        self.assertEqual(response.status_code, 400)

    def test_create_correct_template(self):
        url = reverse("templates-list", kwargs={"version": "v1"})
        body = {
            "display_name": "new-template",
            "description": "test-template",
            "business_type": BusinessTypes.DIAGNOSTIC.value,
            "sub_type": BusinessSubTypes.OUTER_WALL_DIAGNOSTIC.value,
        }
        response = self.client.post(url, data=body)
        data = response.json()
        self.assertEqual(response.status_code, 201)
        self.assertNotEqual(data["id"], None)
        self.assertEqual(data["display_name"], body["display_name"])
        self.assertEqual(data["description"], body["description"])

    def test_update_template_status_to_active(self):
        faker_template_1 = TemplateFactory(
            business_type=BusinessTypes.MANAGEMENT_SHEET.value
        )
        faker_template_2 = TemplateFactory(
            business_type=BusinessTypes.MANAGEMENT_SHEET.value
        )
        faker_template_3 = TemplateFactory(
            business_type=BusinessTypes.MANAGEMENT_SHEET.value
        )
        # Change status template fake 1 to ACTIVE
        url = reverse(
            "active-template", kwargs={"version": "v1", "pk": faker_template_1.id}
        )
        response = self.client.put(url)
        faker_template_1.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(faker_template_1.status, TemplateStatus.ACTIVE.value)
        self.assertEqual(faker_template_2.status, TemplateStatus.INACTIVE.value)
        self.assertEqual(faker_template_3.status, TemplateStatus.INACTIVE.value)

        # Change status template fake 2 to ACTIVE
        url = reverse(
            "active-template", kwargs={"version": "v1", "pk": faker_template_2.id}
        )
        response = self.client.put(url)
        faker_template_1.refresh_from_db()
        faker_template_2.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(faker_template_1.status, TemplateStatus.INACTIVE.value)
        self.assertEqual(faker_template_2.status, TemplateStatus.ACTIVE.value)
        self.assertEqual(faker_template_3.status, TemplateStatus.INACTIVE.value)

    # @TODO: After confirm delete template flow
    def test_delete_template(self):
        pass
