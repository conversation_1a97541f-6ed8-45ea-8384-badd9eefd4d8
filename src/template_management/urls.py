from rest_framework.routers import SimpleRouter

from django.urls import include, path
from django.urls import re_path as url

from .views import SetActiveTemplateApiView, TemplateViewSet

router = SimpleRouter()
router.register(r"", TemplateViewSet, "templates")

urlpatterns = [
    url(r"^", include(router.urls)),
    path("<int:pk>/active", SetActiveTemplateApiView.as_view(), name="active-template"),
]
