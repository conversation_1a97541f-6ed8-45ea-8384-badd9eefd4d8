from django.contrib import admin

from .models import Template, TemplateStatus


class TemplateAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "display_name",
        "business_type",
        "sub_type",
        "status",
        "description",
        "created_at",
    ]

    def save_model(self, request, obj, form, change) -> None:
        if obj.status == TemplateStatus.ACTIVE.value:
            active_template = Template.objects.filter(
                business_type=obj.business_type,
                sub_type=obj.sub_type,
                status=TemplateStatus.ACTIVE.value,
            ).first()
            if active_template:
                active_template.status = TemplateStatus.INACTIVE.value
                active_template.save()
        return super().save_model(request, obj, form, change)


admin.site.register(Template, TemplateAdmin)
