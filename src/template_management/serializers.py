from rest_framework import serializers

from .models import BusinessTypes, Template


class TemplateSerializer(serializers.ModelSerializer):
    status = serializers.CharField(read_only=True)

    class Meta:
        model = Template
        exclude = ("template_name",)
        extra_kwargs = {
            "business_type": {"required": True},
            "sub_type": {"default": None},
        }

    def validate(self, attrs):
        """
        Check sub_type is required if business_type == SDS
        """
        if (
            attrs["business_type"] == BusinessTypes.DIAGNOSTIC.value
            and attrs["sub_type"] is None
        ):
            raise serializers.ValidationError(
                {"sub_type": "Daignostic should have sub type"}
            )
        return super().validate(attrs)


class UpdateTemplateStatusToActiveSerializer(serializers.ModelSerializer):
    class Meta:
        model = Template
        fields = ("status",)


class GetActiveTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Template
        fields = (
            "id",
            "display_name",
            "category",
        )
