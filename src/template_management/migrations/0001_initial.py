# Generated by Django 4.1 on 2022-08-25 02:52

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Template",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "display_name",
                    models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255, null=True),
                ),
                (
                    "template_name",
                    models.CharField(blank=True, max_length=32, null=True),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("MS", "Management Sheet"),
                            ("SDS_ROOF", "Roof Dianostis"),
                            ("SDS_OUTER_WALL", "Outer Wall Dianostis"),
                            ("WARRANTY", "Warranty"),
                        ],
                        max_length=255,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("ACTIVE", "Active"), ("INACTIVE", "Inactive")],
                        default="INACTIVE",
                        max_length=16,
                    ),
                ),
                ("description", models.Char<PERSON>ield(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("extra_fields", models.JSONField(default=dict)),
            ],
            options={
                "db_table": "template_management",
            },
        ),
    ]
