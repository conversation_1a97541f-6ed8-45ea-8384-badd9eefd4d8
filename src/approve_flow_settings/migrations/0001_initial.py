# Generated by Django 4.1 on 2022-09-19 04:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0004_alter_company_options"),
    ]

    operations = [
        migrations.CreateModel(
            name="ApproveFlowSetting",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("SDS_ROOF", "Sds Roof"),
                            ("SDS_OUTER_WALL", "Sds Outer Wall"),
                            ("MS", "Ms"),
                        ],
                        max_length=125,
                    ),
                ),
                ("is_approve_level_dealer", models.BooleanField(default=True)),
                ("is_approve_level_act", models.BooleanField(default=True)),
                ("is_signed_by_shop", models.BooleanField(default=True)),
                ("is_signed_by_dealer", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("is_signed_by_act", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "act",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_Setting_dealer_act",
                        to="companies.company",
                    ),
                ),
                (
                    "dealer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_Setting_shop_dealer",
                        to="companies.company",
                    ),
                ),
                (
                    "shop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_Setting_shop",
                        to="companies.company",
                    ),
                ),
            ],
            options={
                "db_table": "approve_flow_setting",
                "ordering": ["-id"],
            },
        ),
    ]
