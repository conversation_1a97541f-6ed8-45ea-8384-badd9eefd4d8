import codecs
import csv
import os

from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError, status
from rest_framework.response import Response

from django.db.models import Q

from api.constants import COLUMN_CSV_APPROVE_FLOW_SETTING, MAX_LENGTH_NAME
from companies.models import Company, CompanyType, ShopDealer
from prefectures.models import Prefectures

from .models import ApproveFlowSetting
from .serializers import (
    ApproveFlowSettingSerializer,
    GetListApproveFlowSerializer,
)


class ApproveFlowSettingViewSet(viewsets.ModelViewSet):

    serializer_class = ApproveFlowSettingSerializer
    http_method_names = ["get", "post", "put", "delete"]

    def get_serializer_class(self):
        if self.action in ["list", "retrieve"]:
            return GetListApproveFlowSerializer

        return super().get_serializer_class()

    def get_queryset(self):
        approve_flow_settings = ApproveFlowSetting.objects.all().filter(is_deleted=False)
        is_include_deleted = self.request.query_params.get('is_include_deleted', "false").lower()
        name = self.request.query_params.get("name", None)
        type_approve_flow = self.request.query_params.get("type", None)
        shop_name = self.request.query_params.get("shop_name", None)
        shop_id = self.request.query_params.get("shop_id", None)
        keyword = self.request.query_params.get("keyword", None)

        if is_include_deleted == 'true':
            approve_flow_settings = ApproveFlowSetting.objects.all()

        if name is not None:
            approve_flow_settings = approve_flow_settings.filter(
                Q(name__icontains=name)
            )
        if type_approve_flow is not None:
            approve_flow_settings = approve_flow_settings.filter(
                Q(type=type_approve_flow)
            )
        if shop_name is not None:
            approve_flow_settings = approve_flow_settings.filter(
                Q(shop__name__icontains=shop_name), Q(shop__type=CompanyType.SHOP)
            )
        if shop_id is not None:
            approve_flow_settings = approve_flow_settings.filter(Q(shop__id=shop_id))

        if keyword is not None:
            approve_flow_settings = approve_flow_settings.filter(
                Q(shop__name__icontains=keyword) | Q(dealer__name__icontains=keyword)
            ).distinct()

        return approve_flow_settings

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.approve_flow_setting_instance.all():
            raise ValidationError({"detail": "この承認フローは既に利用されているため、更新することができません。"})
        return super().update(request, *args, **kwargs)

    def perform_destroy(self, instance):
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    # import user kanna for AGC.
    @action(
        detail=False,
        methods=["post"],
        permission_classes=[permissions.IsAuthenticated],
        url_path="import",
    )
    def import_approve_flow_setting(self, request, **kwargs):
        """Upload data from CSV, with validation."""
        count_row_import = 0
        count_row_success = 0
        count_row_error = 0
        row_current = 0
        status_import = 0
        errors_list = []
        type = None
        is_approve_by_level_dealer = True
        is_approve_by_level_act = True
        is_assigned_by_dealer = True
        is_assigned_by_act = True
        prefectures = None
        company_shop = None
        company_dealer = None
        act = Company.objects.filter(type=CompanyType.ACT).first()
        file = request.FILES.get("csv_file")
        if not file:
            return Response(
                {
                    "message": "このファイル形式は保護ビューでサポートされていません。",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        reader = csv.DictReader(codecs.iterdecode(file, "utf-8"), delimiter=",")
        num_cols = len(reader.fieldnames)
        data = list(reader)

        def validate_csv_file(file):
            # Format must be .csv
            file_ext = os.path.splitext(file.name)[1]
            print(file_ext)
            if (
                file_ext.lower() == ".csv"
                and num_cols == COLUMN_CSV_APPROVE_FLOW_SETTING
            ):
                # Valid
                return True
            # Invalid
            return False

        # Validate format csv file (structure, format)
        csv_data = validate_csv_file(file)

        if csv_data is False:
            return Response(
                {
                    "message": "このファイル形式は保護ビューでサポートされていません。",
                },
                status=400,
            )

        for row in data:
            count_row_import = count_row_import + 1
            row_current = row_current + 1

            try:

                # Validate name
                if list(row.values())[0] is None:
                    errors_list.append(
                        {"row": row_current, "detail": "名前ところがNullになっているレコードがあります。"}
                    )
                    count_row_error = count_row_error + 1
                    continue

                else:
                    if len(list(row.values())[0]) > MAX_LENGTH_NAME:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "名前の桁数は255より超えてはいけません。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate type
                if list(row.values())[1] is None:
                    errors_list.append(
                        {"row": row_current, "detail": "承認フローのタイプは必須項目です。"}
                    )
                    count_row_error = count_row_error + 1
                    continue

                else:
                    # Validate type:
                    if list(row.values())[1] == "0":
                        type = "SDS_ROOF"
                    elif list(row.values())[1] == "1":
                        type = "SDS_OUTER_WALL"
                    elif list(row.values())[1] == "2":
                        type = "MS"
                    else:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "承認フロータイプは”0: 屋根診断書, 1:外壁診断書, 2:管理シート”中から入力してください。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # Validate shop
                if list(row.values())[3] is None:
                    errors_list.append(
                        {"row": row_current, "detail": "メイクUpショップIDは必須項目です。"}
                    )
                    count_row_error = count_row_error + 1
                    continue
                else:

                    # validate prefectures
                    if list(row.values())[2]:
                        prefectures = Prefectures.objects.filter(
                            id=list(row.values())[2]
                        ).first()
                        if prefectures is None:
                            errors_list.append(
                                {
                                    "row": row_current,
                                    "detail": "エラーを表示している県はシステム中に存在していません。",
                                }
                            )
                            count_row_error = count_row_error + 1
                            continue
                        else:

                            company_prefecture = Company.objects.filter(
                                id=list(row.values())[3],
                                prefectures_id=list(row.values())[2],
                            ).first()
                            if company_prefecture is None:
                                errors_list.append(
                                    {
                                        "row": row_current,
                                        "detail": "エラーを表示している県には配属しているメイクUpショップが存在していません。",
                                    }
                                )
                                count_row_error = count_row_error + 1
                                continue

                    company_shop = Company.objects.filter(
                        id=list(row.values())[3], type=CompanyType.SHOP
                    ).first()

                    if company_shop is None:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "エラーを表示しているメイクUpショップIDは適切でないため、再度確認してください。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                # validate dealer:
                if list(row.values())[5]:
                    company_dealer = Company.objects.filter(
                        id=list(row.values())[5], type=CompanyType.DEALER
                    ).first()
                    if company_dealer is None:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "エラーを表示しているディーラーIDは適切でないため、再度確認してください。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                    else:

                        shop_dealer = ShopDealer.objects.filter(
                            shop_id=list(row.values())[3],
                            dealer_id=list(row.values())[5],
                        ).first()
                        if shop_dealer is None:
                            errors_list.append(
                                {
                                    "row": row_current,
                                    "detail": "ディーラーとメイクUpショップの関係を再度確認してください。",
                                }
                            )
                            count_row_error = count_row_error + 1
                            continue

                    if list(row.values())[7]:

                        # Validate type:
                        if list(row.values())[7] == "1":
                            is_approve_by_level_dealer = True
                        elif list(row.values())[7] == "0":
                            is_approve_by_level_dealer = False

                        else:
                            errors_list.append(
                                {
                                    "row": row_current,
                                    "detail": "承認フローにはディーラーグループ承認場合：０、ディーラーグループ承認でない場合：１で記入してください。",
                                }
                            )
                            count_row_error = count_row_error + 1
                            continue

                    if list(row.values())[8]:

                        # Validate type:
                        if list(row.values())[8] == "1":
                            is_assigned_by_dealer = True
                        elif list(row.values())[8] == "0":
                            is_assigned_by_dealer = False

                        else:
                            errors_list.append(
                                {
                                    "row": row_current,
                                    "detail": "ディーラーの電子印が必要場合：０、ディーラーの電子印が必要でない場合：１で記入してください。",
                                }
                            )
                            count_row_error = count_row_error + 1
                            continue
                    else:
                        # Validate type:
                        if list(row.values())[1] == "0" or list(row.values())[1] == "1":
                            is_assigned_by_dealer = False
                        elif list(row.values())[1] == "2":
                            is_assigned_by_dealer = True

                else:
                    is_approve_by_level_dealer = False
                    is_assigned_by_dealer = False

                if list(row.values())[9]:
                    # Validate type:
                    if list(row.values())[9] == "1":
                        is_approve_by_level_act = True
                    elif list(row.values())[9] == "0":
                        is_approve_by_level_act = False

                    else:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "承認フローには塗装メーカーグループ承認場合：０、塗装メーカーグループ承認でない場合：１で記入してください。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                else:
                    is_approve_by_level_act = True

                if list(row.values())[10]:
                    # Validate type:
                    if list(row.values())[10] == "1":
                        is_assigned_by_act = True
                    elif list(row.values())[10] == "0":
                        is_assigned_by_act = False

                    else:
                        errors_list.append(
                            {
                                "row": row_current,
                                "detail": "塗装メーカーの電子印が必要場合：０、塗装メーカーの電子印が必要でない場合：１で記入してください。",
                            }
                        )
                        count_row_error = count_row_error + 1
                        continue

                else:
                    is_assigned_by_act = True

                approve_flow_setting = ApproveFlowSetting.objects.create(
                    name=list(row.values())[0],
                    type=type,
                    shop_prefecture=prefectures,
                    shop=company_shop,
                    dealer=company_dealer,
                    act=act,
                    is_approve_level_dealer=is_approve_by_level_dealer,
                    is_approve_level_act=is_approve_by_level_act,
                    is_signed_by_dealer=is_assigned_by_dealer,
                    is_signed_by_act=is_assigned_by_act,
                )

                approve_flow_setting.save()
                count_row_success = count_row_success + 1
                prefectures = None
                company_dealer = None
                company_shop = None

            except Exception as e:
                print(e)
                errors_list.append(
                    {"row": row_current, "detail": "エラーが発生しました。記入した内容をもう一度確認してください。"}
                )
                count_row_error = count_row_error + 1
                continue

        if count_row_success == 0:
            status_import = status.HTTP_400_BAD_REQUEST
        else:
            status_import = status.HTTP_201_CREATED
        return Response(
            {
                "total_row:": count_row_import,
                "success:": count_row_success,
                "error:": count_row_error,
                "errors_list": errors_list,
            },
            status=status_import,
        )
