from django.contrib import admin

from .models import ApproveFlowSetting


class ApproveFlowSettingAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name",
        "type",
        "shop_prefecture",
        "shop",
        "dealer",
        "act",
        "is_approve_level_dealer",
        "is_approve_level_act",
        "is_signed_by_shop",
        "is_signed_by_dealer",
        "is_signed_by_act",
    ]
    autocomplete_fields = ["shop", "dealer", "act"]


admin.site.register(ApproveFlowSetting, ApproveFlowSettingAdmin)
