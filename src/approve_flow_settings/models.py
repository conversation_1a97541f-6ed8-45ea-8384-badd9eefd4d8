from django.db import models

from companies.models import Company
from prefectures.models import Prefectures
from utilities.model.custom_model import CustomModel


class ApproveFlowType(models.TextChoices):
    SDS_ROOF = ("SDS_ROOF",)
    SDS_OUTER_WALL = ("SDS_OUTER_WALL",)
    MS = ("MS",)


class ApproveFlowSettingAbstract(CustomModel):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    type = models.CharField(
        max_length=125,
        choices=ApproveFlowType.choices,
    )
    is_approve_level_dealer = models.BooleanField(default=True)
    is_approve_level_act = models.BooleanField(default=True)
    is_signed_by_shop = models.BooleanField(default=True)
    is_signed_by_dealer = models.BooleanField(default=True)
    is_signed_by_act = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class ApproveFlowSettingManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

class ApproveFlowSetting(ApproveFlowSettingAbstract):
    shop_prefecture = models.ForeignKey(
        Prefectures,
        to_field="id",
        related_name="shop_prefecture",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    shop = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_Setting_shop",
        on_delete=models.DO_NOTHING,
    )
    dealer = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_Setting_shop_dealer",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    act = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_Setting_dealer_act",
        on_delete=models.DO_NOTHING,
    )

    # Override the default manager
    objects = ApproveFlowSettingManager()

    class Meta:
        db_table = "approve_flow_setting"
        ordering = ["-id"]

    def __str__(self) -> str:
        return self.name
