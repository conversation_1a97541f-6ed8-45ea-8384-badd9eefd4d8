from rest_framework import serializers

from companies.models import Company

from .models import ApproveFlowSetting


class CompanyFlowSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = (
            "id",
            "name",
            "type",
        )


class ApproveFlowSettingSerializer(serializers.ModelSerializer):
    is_include_deleted = serializers.BooleanField(required=False, read_only=True)

    class Meta:
        model = ApproveFlowSetting
        fields = [
            "id",
            "name",
            "type",
            "shop_prefecture",
            "shop",
            "dealer",
            "act",
            "is_approve_level_dealer",
            "is_approve_level_act",
            "is_signed_by_shop",
            "is_signed_by_dealer",
            "is_signed_by_act",
            "is_deleted",
            "updated_at",
            "created_at",
            "is_include_deleted"
        ]

    def create(self, validated_data):
        approve_flow_setting = ApproveFlowSetting.objects.create(**validated_data)

        company = (
            ApproveFlowSetting.objects.filter(pk=approve_flow_setting.id)
            .values(
                "shop__name",
                "shop__id",
                "shop__type",
                "dealer__id",
                "dealer__name",
                "dealer__type",
                "act__id",
                "act__name",
                "act__type",
            )
            .first()
        )
        shop = Company(
            {
                "id": approve_flow_setting.shop_id,
                "name": company["shop__name"],
                "type": company["shop__type"],
            }
        )
        dealer = Company(
            {
                "id": approve_flow_setting.dealer_id,
                "name": company["dealer__name"],
                "type": company["dealer__type"],
            }
        )
        act = Company(
            {
                "id": approve_flow_setting.act_id,
                "name": company["act__name"],
                "type": company["act__type"],
            }
        )

        approve_flow_setting.shop = shop
        approve_flow_setting.dealer = dealer
        approve_flow_setting.act = act

        return approve_flow_setting


class GetListApproveFlowSerializer(ApproveFlowSettingSerializer):
    shop = CompanyFlowSettingSerializer(read_only=True)
    dealer = CompanyFlowSettingSerializer(read_only=True)
    act = CompanyFlowSettingSerializer(read_only=True)
