import factory

from companies.factories import CompanyFactory
from prefectures.factories import PrefecturesFactory
from .models import ApproveFlowSetting


class ApproveFlowSettingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ApproveFlowSetting

    name = factory.Sequence(lambda n: f"Approve Flow Setting {n}")
    type = "SDS_ROOF"
    shop_prefecture = factory.SubFactory(PrefecturesFactory)
    shop = factory.SubFactory(CompanyFactory, type="SHOP")
    dealer = factory.SubFactory(CompanyFactory, type="DEALER")
    act = factory.SubFactory(CompanyFactory, type="ACT")
    is_approve_level_dealer = True
    is_approve_level_act = True
    is_signed_by_shop = True
    is_signed_by_dealer = True
    is_signed_by_act = True
