from api import constants
from kanna_api.common import KaRequests
from kanna_api.models import UserKanna


def get_users(user: <PERSON>r<PERSON><PERSON>, page: int, limit: int):
    response = KaRequests(user).get("/v1/users", params={"page": page, "limit": limit})
    total_count = response.headers.get("total_count")
    total_page = response.headers.get("total_page")
    has_next_page = response.headers.get("has_next_page")
    return response.json() | {
        "total_count": total_count,
        "total_page": total_page,
        "has_next_page": has_next_page,
    }


def get_user(user: User<PERSON><PERSON>, user_uuid: str):
    response = KaRequests(user).get(f"/v1/users/{user_uuid}")
    return response.json()


def get_members(user: UserKanna):
    response = KaRequests(user).get(
        "/v1/users",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    members = response_data.get("users")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            "/v1/users", params={"page": page, "limit": constants.KANNA_LIST_LIMIT}
        )
        res_data = res.json()
        members += res_data.get("users")

    return members


def update_member(user: UserKanna, user_uuid: str, params):
    response = KaRequests(user).patch(f"/v1/users/{user_uuid}", json=params)
    return response.json()
