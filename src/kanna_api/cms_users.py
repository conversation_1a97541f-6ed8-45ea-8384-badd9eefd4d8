from requests import Response

from .common import KaRequests
from .models.user_kanna import <PERSON><PERSON><PERSON><PERSON>


def add_cms_users(user: <PERSON><PERSON><PERSON><PERSON>, cms_uuid: str, data: dict) -> dict:
    """案件担当者追加"""

    response = KaRequests(user).post(f"/v1/cms/{cms_uuid}/cms_users", data=data)
    return response.json()


def del_cms_users(user: User<PERSON><PERSON>, cms_uuid: str, user_uuid: str) -> Response:
    """案件担当者削除"""

    response = KaRequests(user).delete(f"/v1/cms/{cms_uuid}/cms_users/{user_uuid}")
    return response
