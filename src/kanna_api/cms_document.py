from api import constants

from .common import KaRequests
from .models.user_kanna import Use<PERSON><PERSON><PERSON>


def get_document_categories(user: <PERSON><PERSON><PERSON><PERSON>, cms_uuid: str) -> list:
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/document_categories",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    document_categories = response_data.get("documentCategories")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            f"/v1/cms/{cms_uuid}/document_categories",
            params={
                "page": page,
                "limit": constants.KANNA_LIST_LIMIT,
            },
        )
        res_data = res.json()
        document_categories += res_data.get("documentCategory")

    return document_categories


def get_document_category(
    user: <PERSON><PERSON><PERSON><PERSON>, cms_uuid: str, document_category_uuid: str
) -> dict:
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}"
    )
    return response.json()


def update_document_category(
    user: UserKanna, cms_uuid: str, document_category_uuid: str, params: dict
) -> dict:
    response = KaRequests(user).patch(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}", params
    )
    return response.json()


def create_document_category(user: UserKanna, cms_uuid: str, params: dict) -> dict:
    response = KaRequests(user).post(f"/v1/cms/{cms_uuid}/document_categories", params)
    return response.json()


def get_documents(user: UserKanna, cms_uuid: str, document_category_uuid: str) -> list:
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    images = response_data.get("documents")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents",
            params={"page": page, "limit": constants.KANNA_LIST_LIMIT},
        )
        res_data = res.json()
        images += res_data.get("documents")

    return images


def get_document(
    user: UserKanna, cms_uuid: str, document_category_uuid: str, document_uuid: str
) -> dict:
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents/{document_uuid}"
    )
    return response.json()


def create_document(
    user: UserKanna, cms_uuid: str, document_category_uuid: str, files
) -> dict:
    response = KaRequests(user).post(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents",
        files=files,
        data={"documentCategoryUuid": document_category_uuid},
    )
    return response.json()


def update_document(
    user: UserKanna,
    cms_uuid: str,
    document_category_uuid: str,
    document_uuid: str,
    params: dict,
) -> dict:
    response = KaRequests(user).patch(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents/{document_uuid}",
        params,
    )
    return response.json()


def delete_document(
    user: UserKanna, cms_uuid: str, document_category_uuid: str, document_uuid: str
):
    response = KaRequests(user).delete(
        f"/v1/cms/{cms_uuid}/document_categories/{document_category_uuid}/documents/{document_uuid}"
    )
    return response
