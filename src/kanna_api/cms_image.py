from api import constants
from authentication.models import User
from utilities.kanna_api.multi_filter import MultiFilter

from .common import KaRequests


def get_image_categories(user: User, cms_uuid):
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/image_categories",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    image_categories = response_data.get("imageCategories")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            f"/v1/cms/{cms_uuid}/image_categories",
            params={"page": page, "limit": constants.KANNA_LIST_LIMIT},
        )
        res_data = res.json()
        image_categories += res_data.get("imageCategories")

    return image_categories


def get_image_category(user: User, cms_uuid, image_category_uuid):
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}"
    )
    return response.json()


def create_image_category(user: User, cms_uuid, data):
    response = KaRequests(user).post(f"/v1/cms/{cms_uuid}/image_categories", data)
    return response.json()


def get_images(user: User, cms_uuid, image_category_uuid=None):
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    images = response_data.get("images")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images",
            params={"page": page, "limit": constants.KANNA_LIST_LIMIT},
        )
        res_data = res.json()
        images += res_data.get("images")

    return images


def get_image(user: User, cms_uuid, image_category_uuid, image_uuid):
    response = KaRequests(user).get(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images/{image_uuid}"
    )
    return response.json()


def create_image(user: User, cms_uuid: str, image_category_uuid: str, files):
    response = KaRequests(user).post(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images",
        files=files,
        data={"imageCategoryUuid": image_category_uuid},
    )
    return response.json()


def update_image(
    user: User, cms_uuid: str, image_category_uuid: str, image_uuid: str, params: dict
):
    response = KaRequests(user).patch(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images/{image_uuid}",
        params,
    )
    return response.json()


def delete_image(user: User, cms_uuid: str, image_category_uuid: str, image_uuid: str):
    response = KaRequests(user).delete(
        f"/v1/cms/{cms_uuid}/image_categories/{image_category_uuid}/images/{image_uuid}"
    )
    return response


def cms_image_filter(params, cms_image_list_data: list) -> list:
    multi_filter = MultiFilter()

    if params.parent_image_category_uuid:
        multi_filter.add(
            lambda image_category: params.parent_image_category_uuid
            == image_category.get("parent_image_category_uuid")
        )
    else:
        multi_filter.add(
            lambda image_category: not image_category.get("parent_image_category_uuid")
        )

    cms_image_list_data = multi_filter.run(cms_image_list_data)

    return cms_image_list_data
