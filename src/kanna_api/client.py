import datetime

import dateutil.parser

from kanna_api.common import KaRequests
from kanna_api.models import UserKanna
from kanna_api.query_params.client import ClientParam
from kanna_api.utils.compare import contain_ja
from kanna_api.utils.multi_filter import MultiFilter


def get_clients(user: User<PERSON><PERSON>):
    """顧客全件取得"""

    response = KaRequests(user).get("/v1/clients", params={"page": 1, "limit": 100})
    response_data = response.json()
    total_page = response.headers.get("total_page")
    clients = response_data.get("clients")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get("/v1/clients", params={"page": page, "limit": 100})
        res_data = res.json()
        clients += res_data.get("clients")

    return clients


def get_client(user: UserKanna, client_uuid: str) -> dict:
    """顧客詳細取得"""

    response = KaRequests(user).get(f"/v1/clients/{client_uuid}")
    return response.json()


def update_client(user: User<PERSON><PERSON>, client_uuid: str, data: dict) -> dict:
    """顧客更新"""

    response = KaRequests(user).patch(f"/v1/clients/{client_uuid}", data=data)
    return response.json()


def clients_filter(params: ClientParam, response_data: list[dict]):
    multi_filter = MultiFilter()
    # timezoneをJSTに設定
    jst = datetime.timezone(datetime.timedelta(hours=+9), "JST")

    # 顧客名
    if params.name:
        multi_filter.add(
            lambda client: contain_ja(params.name, client.get("full_name", ""))
            or contain_ja(params.name, client.get("company_name", ""))
            or contain_ja(params.name, client.get("full_name_kana", ""))
            or contain_ja(params.name, client.get("company_name_kana", ""))
        )

    # 区分
    if params.business_form_type:
        multi_filter.add(
            lambda client: client.get("business_form_type", "")
            == params.business_form_type.value
        )

    # 住所
    if params.address:
        multi_filter.add(
            lambda client: contain_ja(params.address, client.get("address", ""))
        )

    # 電話番号
    if params.phone_number:
        multi_filter.add(
            lambda client: params.phone_number in client.get("phone_number", "")
        )

    # メールアドレス
    if params.email:
        multi_filter.add(lambda client: params.email in client.get("email", ""))

    # 登録日（開始）
    if params.created_at_from:
        multi_filter.add(
            lambda client: not client.get("created_at")
            or params.created_at_from
            <= dateutil.parser.parse(client.get("created_at")).astimezone(jst).date()
        )

    # 登録日（終了）
    if params.created_at_to:
        multi_filter.add(
            lambda client: not client.get("created_at")
            or params.created_at_to
            >= dateutil.parser.parse(client.get("created_at")).astimezone(jst).date()
        )

    response_data = multi_filter.run(response_data)

    return response_data
