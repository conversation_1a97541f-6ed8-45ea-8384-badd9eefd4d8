from kanna_api.common import KaRequests
from kanna_api.models import User<PERSON>anna


def get_profile(user: UserK<PERSON>) -> dict:
    response = KaRequests(user).get("/v1/profile")
    return response.json()


def get_profile_with_token(access_token: str) -> dict:
    request = KaRequests()
    request.set_authorization_header(access_token)
    response = request.get("/v1/profile")
    return response.json()
