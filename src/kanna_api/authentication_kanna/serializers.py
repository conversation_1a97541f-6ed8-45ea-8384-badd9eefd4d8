from rest_framework import serializers

from kanna_api.models.user_kanna import UserKanna

from ..choices.app_div import AppDiv


class TokenResponseSerializer(serializers.Serializer):
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()
    access_token_expires = serializers.DateTimeField()
    refresh_token_expires = serializers.DateTimeField()


class LoginURLSerializer(serializers.Serializer):
    redirect_url = serializers.CharField()


class LoginURLResponseSerializer(serializers.Serializer):
    kanna_login_url = serializers.CharField()


class TokenObtainSerializer(serializers.Serializer):
    authorization_code = serializers.CharField()
    app_div = serializers.ChoiceField(choices=AppDiv.choices, default=None)


class TokenRefreshSerializer(serializers.Serializer):
    refresh_token = serializers.CharField()

    class Meta:
        ref_name = "Kana Refresh Token"


class UserKannaSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserKanna
        fields = ["id"]
