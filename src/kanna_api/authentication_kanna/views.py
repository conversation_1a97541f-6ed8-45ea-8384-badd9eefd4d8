import logging
import os

from drf_yasg.utils import swagger_auto_schema
from rest_framework import exceptions, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken

from django.db import transaction
from django.utils import timezone
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import smart_bytes

from api import constants
from authentication.models import User
from cms.services import gen_verify_code, update_user_assign_cms
from companies.models import Company, CompanyType
from companies.serializers import CompanySerializer
from templates.sms.sms import send_verify_code_sms

from kanna_api.models import Role, UserKanna
from kanna_api.utils.jwt_token import (
    check_refresh_token,
    create_token_pair,
    identify_user,
)
from kanna_api.authentication_kanna.auth import get_login_url, get_token_auth_code, get_token_refresh
from kanna_api.authentication_kanna.profile import get_profile_with_token
from kanna_api.authentication_kanna.serializers import (
    LoginURLResponseSerializer,
    LoginURLSerializer,
    TokenObtainSerializer,
    TokenRefreshSerializer,
    TokenResponseSerializer,
    UserKannaSerializer,
)
from kanna_api.authentication_kanna.yasg_schemas import AuthLoginRequestSchema

logger = logging.getLogger(__name__)

logger_login = logging.getLogger("login")


class AuthViewSet(viewsets.GenericViewSet):
    @swagger_auto_schema(
        responses={status.HTTP_200_OK: LoginURLResponseSerializer},
        request_body=AuthLoginRequestSchema(),
    )
    @action(methods=["post"], detail=False, serializer_class=LoginURLSerializer)
    def login(self, request, *args, **kwargs):
        """
        KANNAのログインURLを取得する
        """
        response = get_login_url(redirect_url=request.data["redirectUrl"])
        return Response(
            data={
                "kannaLoginUrl": response["kanna_redirect_url"],
            }
        )

    @swagger_auto_schema(responses={status.HTTP_200_OK: TokenResponseSerializer})
    @action(methods=["post"], detail=False, serializer_class=TokenObtainSerializer)
    def token(self, request, *args, **kwargs):
        """
        KANNAログインで取得したauthorization_codeでトークンを発行する
        #"""
        # serializer = self.get_serializer(data=request.data)
        # serializer.is_valid(raise_exception=True)

        # KANNAトークン取得
        response = get_token_auth_code(
            authorization_code=request.data["authorizationCode"]
        )

        # KANNA ログインユーザー取得
        ka_user = get_profile_with_token(response["access_token"])
        try:
            with transaction.atomic():
                user_kanna = UserKanna.objects.filter(
                    user_uuid=ka_user["user"]["uuid"]
                ).first()
                if user_kanna and user_kanna.email != ka_user["user"]["email"]:
                    user_online_certificate = user_kanna.user
                    if (
                        user_online_certificate_exist := User.objects.filter(
                            email=ka_user["user"]["email"]
                        )
                        .exclude(id=user_kanna.user.id)
                        .all()
                    ):
                        user_online_certificate_exist.delete()

                    user_online_certificate.email = ka_user["user"]["email"]
                    user_online_certificate.username = ka_user["user"]["email"].replace(
                        "@", ""
                    )
                    user_online_certificate.save()

                    if user_kanna.email != ka_user["user"]["email"]:
                        user_kanna.email = ka_user["user"]["email"]
                        user_kanna.save()
        except Exception as e:
            logger.error(e)
            raise ValidationError(e) from e

        # role_kanna = None
        company_act = None
        try:
            user_online_certificate = User.objects.get(
                email=ka_user["user"]["email"],
            )
            user_online_certificate.username = ka_user["user"]["email"].replace("@", "")
            user_online_certificate.save()
        except User.DoesNotExist:
            user_online_certificate = User.objects.create(
                email=ka_user["user"]["email"],
                username=ka_user["user"]["email"].replace("@", ""),
            )

        user_online_certificate.first_name = ka_user["user"]["firstName"]
        user_online_certificate.last_name = ka_user["user"]["lastName"]
        user_online_certificate.save()

        # ユーザー登録済みの場合はトークン等更新
        user_kanna = UserKanna.objects.filter(email=ka_user["user"]["email"]).first()
        if user_kanna:
            user_kanna = UserKanna.objects.get(email=ka_user["user"]["email"])

            if ka_user["user"]["company"]["uuid"] == os.environ.get(
                "INTERNAL_COMPANY_UUID", ""
            ):

                role_admin = Role.objects.get(
                    id=constants.KANNA_USER_ROLE["ADMIN_SYSTEM"]
                )
                company_act = Company.objects.filter(type=CompanyType.ACT).first()
                if user_kanna.role == role_admin:
                    user_kanna.company = None
                else:
                    role_act = Role.objects.get(id=constants.KANNA_USER_ROLE["ACT"])
                    user_kanna.company = company_act
                    user_kanna.role = role_act

            user_kanna.user_uuid = ka_user["user"]["uuid"]
            user_kanna.name = ka_user["user"]["fullName"]
            user_kanna.ka_access_token = response["access_token"]
            user_kanna.ka_refresh_token = response["refresh_token"]
            user_kanna.company_kanna_uuid = ka_user["user"]["company"]["uuid"]
            user_kanna.company_kanna_name = ka_user["user"]["company"]["name"]
            user_kanna.last_login_at = timezone.now()

            user_kanna.save()

        # 登録の場合は作成者、営業者、ロールを設定、whitelistとuserを紐付け
        else:
            if ka_user["user"]["company"]["uuid"] == os.environ.get(
                "INTERNAL_COMPANY_UUID", ""
            ):

                company_act = Company.objects.filter(type=CompanyType.ACT).first()

            user_kanna = UserKanna.objects.create(
                user_uuid=ka_user["user"]["uuid"],
                ka_access_token=response["access_token"],
                ka_refresh_token=response["refresh_token"],
                last_login_at=timezone.now(),
                created_by=ka_user["user"]["uuid"],
                email=ka_user["user"]["email"],
                name=ka_user["user"]["fullName"],
                user=user_online_certificate,
                company_kanna_uuid=ka_user["user"]["company"]["uuid"],
                company_kanna_name=ka_user["user"]["company"]["name"],
                company=company_act,
                level=None,
            )
        if user_kanna.is_active is False:
            raise exceptions.PermissionDenied("User is inactive")

        update_user_assign_cms(user_kanna)

        phone_number = user_kanna.tel or user_online_certificate.phone

        if user_kanna.two_auth == 1 and phone_number:
            gen_code = gen_verify_code(user_online_certificate)

            send_verify_code_sms(user_kanna, gen_code)

            return Response(
                {
                    "is_2auth": True,
                    "email": user_online_certificate.email,
                    "uidb64": urlsafe_base64_encode(smart_bytes(user_online_certificate.id)),
                },
                status=status.HTTP_200_OK,
            )

        if user_kanna.two_auth == 1 and not phone_number:
            return Response(
                {
                    "is_2auth": False,
                    "email": user_online_certificate.email,
                    "uidb64": urlsafe_base64_encode(smart_bytes(user_online_certificate.id)),
                },
                status=status.HTTP_200_OK,
            )

        token = user_online_certificate.tokens()

        logger_login.info(f"User {user_kanna.email} logged in")

        return Response(
            token,
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(responses={status.HTTP_200_OK: TokenResponseSerializer})
    @action(methods=["post"], detail=False, serializer_class=TokenRefreshSerializer)
    def refresh(self, request, *args, **kwargs):
        """
        token再発行
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_id = check_refresh_token(serializer.data["refresh_token"])

        # KANNAカスタマイズのトークン再発行
        token_pair = create_token_pair(user_id)

        # KANNAトークン再発行
        user = UserKanna.objects.get(pk=user_id)
        response = get_token_refresh(user.ka_refresh_token)
        user.ka_access_token = response["access_token"]
        user.ka_refresh_token = response["refresh_token"]
        user.last_login_at = timezone.now()
        user.save()

        return Response(data=token_pair)

    @swagger_auto_schema(responses={status.HTTP_200_OK: TokenResponseSerializer})
    @action(methods=["post"], detail=False, serializer_class=TokenRefreshSerializer)
    def new_refresh(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token_string = serializer.data["refresh_token"]
        # TODO Uncomment this line when we want to respond with the old refresh token, but only if the refresh token has not expired.
        # try:
        #     refresh_token = RefreshToken(token_string)
        #     new_access_token = refresh_token.access_token
        #     return Response(
        #         {"refresh_token": token_string, "access_token": str(new_access_token)}
        #     )
        # except TokenError:
        #     user_id = identify_user(token_string)
        #     RefreshToken(token_string, verify=False).blacklist()
        #     user_online_certificate = User.objects.filter(id=user_id).first()
        #     new_refresh_token = RefreshToken.for_user(user_online_certificate)
        #     new_access_token = new_refresh_token.access_token
        user_id = identify_user(token_string)
        RefreshToken(token_string, verify=False).blacklist()
        user_online_certificate = User.objects.filter(id=user_id).first()
        new_refresh_token = RefreshToken.for_user(user_online_certificate)
        new_access_token = new_refresh_token.access_token

        return Response(
            data={
                "refresh_token": str(new_refresh_token),
                "access_token": str(new_access_token),
            }
        )

    @swagger_auto_schema(responses={status.HTTP_200_OK: UserKannaSerializer})
    @action(
        methods=["get"],
        detail=False,
        serializer_class=UserKannaSerializer,
    )
    def me(self, request, *args, **kwargs):
        access_token_obj = AccessToken(
            request.META.get("HTTP_AUTHORIZATION", "").replace("Bearer ", "")
        )
        user_online_certificate = User.objects.filter(
            id=access_token_obj["user_id"]
        ).first()
        user_kanna = UserKanna.objects.filter(email=user_online_certificate).first()
        role = Role.objects.filter(id=user_kanna.role_id).first()

        if role is None:
            role_id = ""
            role_name = ""
        else:
            role_id = role.id
            role_name = role.name
        company = Company.objects.filter(id=user_kanna.company_id).first()
        company_data = {}
        if company is not None:
            serializer = CompanySerializer(company)
            company_data = serializer.data
        level = ""
        if user_kanna is not None:
            level = user_kanna.level
        return Response(
            data={
                "user_id": user_online_certificate.id,
                "first_name": user_online_certificate.first_name,
                "last_name": user_online_certificate.last_name,
                "email": user_online_certificate.email,
                "role_id": role_id,
                "role_name": role_name,
                "company_id": user_kanna.company_id,
                "company": company_data,
                "last_check_notification_at": user_online_certificate.last_check_notification_at,
                "total_unread_notifications": user_online_certificate.get_total_unread_notifications(),
                "level": level,
            },
            status=status.HTTP_200_OK,
        )
