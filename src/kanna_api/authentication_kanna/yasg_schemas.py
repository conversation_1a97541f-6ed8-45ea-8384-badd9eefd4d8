from drf_yasg import openapi


class AuthLoginRequestSchema(openapi.Schema):
    """ログインURL取得APIのリスエストボディ"""

    def __init__(self):
        super().__init__(
            type=openapi.TYPE_OBJECT,
            properties={
                "redirectUrl": openapi.Schema(
                    type=openapi.TYPE_STRING, example="http://localhost:3000/login/certified"
                ),
            },
        )
