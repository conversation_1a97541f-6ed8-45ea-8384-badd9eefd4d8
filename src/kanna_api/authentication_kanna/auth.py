from django.conf import settings

from kanna_api.common import KaRequests


def get_login_url(redirect_url: str) -> dict:
    response = KaRequests().post(
        "/v1/auth/login",
        json={
            "response_type": "code",
            "client_id": settings.KANNA_CLIENT_ID,
            "client_secret": settings.KANNA_CLIENT_SECRET,
            "redirect_url": redirect_url,
        },
    )
    return response.json()


def get_token_auth_code(authorization_code: str) -> dict:
    response = KaRequests().post(
        "/v1/auth/token",
        json={
            "grant_type": "authorization_code",
            "client_id": settings.KANNA_CLIENT_ID,
            "client_secret": settings.KANNA_CLIENT_SECRET,
            "authorization_code": authorization_code,
        },
    )
    return response.json()


def get_token_refresh(refresh_token: str) -> dict:
    response = KaRequests().post(
        "/v1/auth/token",
        json={
            "grant_type": "refresh_token",
            "client_id": settings.KANNA_CLIENT_ID,
            "client_secret": settings.KANNA_CLIENT_SECRET,
            "refresh_token": refresh_token,
        },
    )

    return response.json()
