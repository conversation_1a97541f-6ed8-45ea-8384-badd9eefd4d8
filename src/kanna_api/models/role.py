from django.db import models

from kanna_api.models._base import BaseModel


class Role(BaseModel):
    name = models.CharField(max_length=50)
    created_by = models.UUIDField(null=True, blank=True)
    updated_by = models.UUIDField(null=True, blank=True)
    authorities = models.ManyToManyField(
        "Authority", related_name="roles", through="RoleAuthority"
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "role"
