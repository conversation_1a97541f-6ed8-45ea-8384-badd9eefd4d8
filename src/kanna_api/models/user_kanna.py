from django.db import models
from django.utils.translation import gettext_lazy as _
from mirage import fields

from api.constants import TWO_AUTH_FLAG, KannaUserRoleEnum
from authentication.models import User
from companies.models import Company
from kanna_api.models._base import BaseModel
from kanna_api.models.role import Role


class LevelChoices(models.TextChoices):
    LEVEL_1 = "LEVEL_1", _("Level 1")
    LEVEL_2 = "LEVEL_2", _("Level 2")
    LEVEL_3 = "LEVEL_3", _("Level 3")


class UserKanna(BaseModel):
    user_uuid = models.UUIDField(unique=True, null=True, blank=True)
    company_kanna_uuid = models.UUIDField(null=True, blank=True)
    company_kanna_name = models.CharField(max_length=255, null=True, blank=True)
    ka_access_token = models.TextField()
    ka_refresh_token = models.TextField()
    last_login_at = models.DateTimeField(null=True, blank=True)
    created_by = models.UUIDField(null=True, blank=True)
    updated_by = models.UUIDField(null=True, blank=True)
    # email = models.EmailField(unique=True)
    email = fields.EncryptedEmailField(unique=True, db_index=True)

    role = models.ForeignKey(
        Role,
        to_field="id",
        related_name="role",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    is_active = models.BooleanField(default=False)
    # level = models.CharField(
    #     max_length=50, choices=LevelChoices.choices, null=True, blank=True
    # )
    level = fields.EncryptedCharField(
        null=True, blank=True, choices=LevelChoices.choices
    )
    two_auth = models.IntegerField(choices=TWO_AUTH_FLAG, default=1)

    # pic_position = models.CharField(max_length=255, null=True, blank=True)
    # tel = models.CharField(max_length=30, null=True, blank=True, default="")
    # address = models.CharField(blank=True, max_length=255, default="")
    # name = models.CharField(max_length=255)
    name = fields.EncryptedCharField(blank=True, null=True)
    pic_position = fields.EncryptedCharField(blank=True, null=True)
    tel = fields.EncryptedCharField(blank=True, null=True, default="")
    address = fields.EncryptedCharField(blank=True, null=True, default="")

    company = models.ForeignKey(
        Company,
        to_field="id",
        related_name="company",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )

    user = models.OneToOneField(
        User,
        related_name="kanna_user",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "user_kanna"
        ordering = ["-id"]

    @property
    def is_authenticated(self):
        """
        Always return True. This is a way to tell if the user has been
        authenticated in templates.
        """
        return True

    @property
    def is_admin(self):
        return self.role_id == KannaUserRoleEnum.ADMIN.value
