from django.db import models

from kanna_api.choices.app_div import AppDiv
from kanna_api.models._base import BaseModel


class Authority(BaseModel):
    app_div = models.IntegerField(choices=AppDiv.choices)
    name = models.CharField(max_length=50)
    key = models.CharField(max_length=50)
    order = models.PositiveIntegerField()
    parent = models.ForeignKey("Authority", related_name="children", on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        db_table = "authority"
