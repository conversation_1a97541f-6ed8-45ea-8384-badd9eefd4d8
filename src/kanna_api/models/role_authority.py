from django.db import models

from kanna_api.models._base import BaseModel


class RoleAuthority(BaseModel):
    role = models.ForeignKey("Role", related_name="role_authorities", on_delete=models.CASCADE)
    authority = models.ForeignKey("Authority", related_name="role_authorities", on_delete=models.CASCADE)

    class Meta:
        db_table = "role_authority"
        constraints = [
            models.UniqueConstraint(
                fields=("role", "authority"),
                name="unique_role_authority",
            ),
        ]
