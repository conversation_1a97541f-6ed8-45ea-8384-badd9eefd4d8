from django.db import models

from kanna_api.models._base import BaseModel


class UserWhitelist(BaseModel):
    user = models.OneToOneField(
        "UserKanna", related_name="user_whitelists", on_delete=models.SET_NULL, null=True, blank=True
    )
    business_office = models.ForeignKey("BusinessOffice", related_name="user_whitelists", on_delete=models.PROTECT)
    email = models.EmailField(unique=True)
    created_by = models.UUIDField()
    updated_by = models.UUIDField(null=True, blank=True)
    role = models.ForeignKey("Role", related_name="user_whitelists", on_delete=models.PROTECT, null=True)

    class Meta:
        db_table = "user_whitelist"
