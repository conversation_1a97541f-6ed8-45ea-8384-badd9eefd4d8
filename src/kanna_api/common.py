import requests
from requests import Response, codes
from rest_framework import exceptions

from django.conf import settings
from django.utils import timezone

from .models import UserKanna


class KaRequests:
    URL = settings.KANNA_URL

    def __init__(self, user: UserKanna = None):
        self.headers = {}
        self.user = user

        if user:
            self.set_authorization_header(user.ka_access_token)

    def set_authorization_header(self, access_token: str):
        self.headers["Authorization"] = "Bearer " + access_token

    def remove_authorization_header(self):
        del self.headers["Authorization"]

    def get(self, url: str, params: dict = None, retry=True, **kwargs) -> Response:
        headers = self.headers | kwargs.get("headers", {})
        response = requests.get(self.URL + url, params, headers=headers, **kwargs)
        ok = self._check_status_code(response.status_code)

        # トークン再取得後のリトライ処理
        if not ok and retry:
            return self.get(url, params, retry=False, **kwargs)

        return response

    def post(
        self, url: str, data: dict = None, json: dict = None, retry=True, **kwargs
    ) -> Response:
        headers = self.headers
        if headers is None:
            headers = kwargs.get("headers", {})
        response = requests.post(self.URL + url, data, json, headers=headers, **kwargs)
        ok = self._check_status_code(response.status_code)

        # トークン再取得後のリトライ処理
        if not ok and retry:
            return self.post(url, data, json, retry=False, **kwargs)

        return response

    def put(self, url: str, data: dict = None, retry=True, **kwargs) -> Response:
        headers = self.headers | kwargs.get("headers", {})
        response = requests.put(self.URL + url, data, headers=headers, **kwargs)
        ok = self._check_status_code(response.status_code)

        # トークン再取得後のリトライ処理
        if not ok and retry:
            return self.put(url, data, retry=False, **kwargs)

        return response

    def patch(self, url: str, data: dict = None, retry=True, **kwargs) -> Response:
        headers = self.headers | kwargs.get("headers", {})
        response = requests.patch(self.URL + url, data, headers=headers, **kwargs)
        ok = self._check_status_code(response.status_code)

        # トークン再取得後のリトライ処理
        if not ok and retry:
            return self.patch(url, data, retry=False, **kwargs)

        return response

    def delete(self, url: str, retry=True, **kwargs) -> Response:
        headers = self.headers | kwargs.get("headers", {})
        response = requests.delete(self.URL + url, headers=headers, **kwargs)
        ok = self._check_status_code(response.status_code)

        # トークン再取得後のリトライ処理
        if not ok and retry:
            return self.delete(url, retry=False, **kwargs)

        return response

    def _check_status_code(self, status_code: int):
        """レスポンスのstatus_codeによって例外の発生、トークンの再取得を行う
        Returns:
            True: 正常終了
            False: 認証エラー(再試行可能)
        """
        if status_code == codes.bad_request:
            raise exceptions.ParseError()

        elif status_code == codes.unauthorized:
            if self.user:
                response = self._get_token_refresh(self.user.ka_refresh_token)
                if response.status_code == codes.created:
                    data = response.json()
                    self.user.ka_access_token = data["access_token"]
                    self.user.ka_refresh_token = data["refresh_token"]
                    self.user.last_login_at = timezone.now()
                    self.user.save()
                    self.set_authorization_header(data["access_token"])
                    return False
            raise exceptions.AuthenticationFailed()

        elif status_code == codes.forbidden:
            raise exceptions.PermissionDenied()

        elif status_code == codes.not_found:
            raise exceptions.NotFound()

        elif status_code == codes.internal_server_error:
            raise exceptions.APIException()

        return True

    @classmethod
    def _get_token_refresh(cls, refresh_token: str) -> Response:
        response = requests.post(
            settings.KANNA_URL + "/v1/auth/token",
            json={
                "grant_type": "refresh_token",
                "client_id": settings.KANNA_CLIENT_ID,
                "client_secret": settings.KANNA_CLIENT_SECRET,
                "refresh_token": refresh_token,
            },
        )
        return response
