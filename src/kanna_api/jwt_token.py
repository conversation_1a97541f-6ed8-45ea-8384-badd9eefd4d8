import uuid
from datetime import datetime

import jwt
from django.conf import settings
from django.utils import timezone
from rest_framework import exceptions

from .models import UserToken


def check_token(token: str):
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
    except jwt.exceptions.ExpiredSignatureError:
        raise exceptions.AuthenticationFailed("Token has expired")
    except Exception:
        raise exceptions.AuthenticationFailed("Invalid Token")

    return payload


def check_access_token(token: str):
    payload = check_token(token)

    if payload["token_type"] != "access_token":
        raise exceptions.AuthenticationFailed("Invalid Token")

    return payload["user_id"]


def check_refresh_token(token: str):
    payload = check_token(token)

    if payload["token_type"] != "refresh_token":
        raise exceptions.AuthenticationFailed("Invalid Token")

    user_id = payload["user_id"]

    # dbに同じrefresh_tokenが登録されていることを確認後に削除
    user_token = UserToken.objects.filter(jti=payload["jti"]).first()
    if not user_token:
        raise exceptions.AuthenticationFailed("Invalid Token")
    user_token.delete()

    return user_id


def create_token(user_id, token_type: str, exp: datetime):
    payload = {
        "user_id": user_id,
        "token_type": token_type,
        "exp": exp,
        "jti": uuid.uuid4().hex,
    }

    token = jwt.encode(payload, settings.SECRET_KEY, "HS256")
    return token, payload


def create_token_pair(user_id) -> dict:
    access_token_expires = timezone.now() + settings.SIMPLE_JWT["ACCESS_TOKEN_LIFETIME"]
    refresh_token_expires = timezone.now() + settings.SIMPLE_JWT["REFRESH_TOKEN_LIFETIME"]

    access_token, payload = create_token(user_id, "access_token", access_token_expires)
    refresh_token, payload = create_token(user_id, "refresh_token", refresh_token_expires)

    # refresh_tokenの登録
    UserToken.objects.create(
        user_id=user_id,
        token=refresh_token,
        expires_at=refresh_token_expires,
        jti=payload["jti"],
    )

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "access_token_expires": access_token_expires,
        "refresh_token_expires": refresh_token_expires,
    }
