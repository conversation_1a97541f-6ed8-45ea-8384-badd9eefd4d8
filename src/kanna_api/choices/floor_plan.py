from enum import Enum

from _base import BaseEnumMeta


class FloorPlan(str, Enum, metaclass=BaseEnumMeta):
    """間取り"""

    ONE_ROOM = "ONE_ROOM"  # ワンルーム
    ONE_K = "ONE_K"  # 1K
    ONE_DK = "ONE_DK"  # 1DK
    ONE_LDK = "ONE_LDK"  # 1LDK
    TWO_K = "TWO_K"  # 2K
    TWO_DK = "TWO_DK"  # 2DK
    TWO_LDK = "TWO_LDK"  # 2LDK
    THREE_K = "THREE_K"  # 3K
    THREE_DK = "THREE_DK"  # 3DK
    THREE_LDK = "THREE_LDK"  # 3LDK
    FOUR_K = "FOUR_K"  # 4K
    FOUR_DK = "FOUR_DK"  # 4DK
    MORE_THAN_FOUR_LDK = "MORE_THAN_FOUR_LDK"  # 4LDK以上
    EMPTY = "EMPTY"
