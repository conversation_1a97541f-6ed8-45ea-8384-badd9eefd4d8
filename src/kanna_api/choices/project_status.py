from enum import Enum

from kanna_api.choices._base import BaseEnumMeta


class ProjectStatus(str, Enum, metaclass=BaseEnumMeta):
    """案件フロー"""

    BEFORE_CONSTRUCTION = "BEFORE_CONSTRUCTION"
    BEFORE_CONTRACT = "BEFORE_CONTRACT"
    BEFORE_QUOTATION = "BEFORE_QUOTATION"
    COMPLETE_CONSTRUCTION = "COMPLETE_CONSTRUCTION"
    FAILURE = "FAILURE"
    IN_PROGRESS_CONSTRUCTION = "IN_PROGRESS_CONSTRUCTION"
    PAYMENT_COMPLETED = "PAYMENT_COMPLETED"
    RECEPTION = "RECEPTION"
