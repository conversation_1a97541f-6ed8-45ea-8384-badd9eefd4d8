from datetime import date
from typing import Optional

from pydantic import Field

from kanna_api.choices.property_kind import PropertyKind
from kanna_api.query_params._base import BaseParam


class PropertyParam(BaseParam):
    name: Optional[str] = Field(default=None)
    code: Optional[str] = Field(default=None)
    kind: PropertyKind = Field(default=None)
    address: Optional[str] = Field(default=None)
    created_at_from: date = Field(default=None)
    created_at_to: date = Field(default=None)
