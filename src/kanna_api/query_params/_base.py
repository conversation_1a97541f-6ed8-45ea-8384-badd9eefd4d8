from drf_yasg import openapi
from pydantic import BaseModel


class BaseParam(BaseModel):
    @classmethod
    def yasg_params(cls) -> list[openapi.Parameter]:
        params = []
        schema = cls.schema()
        properties = schema.get("properties")
        required = schema.get("required", [])
        definitions = schema.get("definitions")

        for k, v in properties.items():
            if "$ref" in v.keys():
                v = definitions.get(v["$ref"].split("/")[-1])
            elif "$ref" in v.get("items", {}).keys():
                v["items"] = definitions.get(v["items"]["$ref"].split("/")[-1])
            params.append(
                openapi.Parameter(
                    k,
                    openapi.IN_QUERY,
                    type=v.get("type"),
                    default=v.get("default"),
                    required=k in required,
                    enum=v.get("enum"),
                    items=v.get("items"),
                    format=v.get("format"),
                    pattern=v.get("pattern"),
                )
            )

        return params
