from datetime import date
from typing import Optional

from pydantic import Field, validator

from kanna_api.choices.business_form_type import BusinessFormType
from kanna_api.query_params._base import BaseParam


class ClientParam(BaseParam):
    name: Optional[str] = Field(default=None)
    business_form_type: BusinessFormType = Field(default=None)
    address: Optional[str] = Field(default=None)
    phone_number: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    created_at_from: date = Field(default=None)
    created_at_to: date = Field(default=None)
