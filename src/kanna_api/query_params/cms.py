from datetime import date
from typing import Optional

from pydantic import Field

from kanna_api.choices.project_status import ProjectStatus
from kanna_api.query_params._base import BaseParam


class CmsParam(BaseParam):
    title: Optional[str] = Field()
    on_user_uuid: Optional[str] = Field()
    not_on_user_uuid: Optional[str] = Field()
    not_on_group_id: Optional[int] = Field()
    industry_code: Optional[str] = Field(default=None)
    construction_no: Optional[str] = Field(default=None)
    project_status: ProjectStatus = Field(default=None)
    started_on_from: date = Field(default=None)
    started_on_to: date = Field(default=None)
    finished_on_from: date = Field(default=None)
    finished_on_to: date = Field(default=None)
    business_office_name: str = Field(default=None)
    include_all_business_office: bool = Field(default=None)
    is_available_for_sds: bool = Field(default=None)
    owner_id: Optional[int] = Field()
    client_uuid: Optional[str] = Field()
    property_uuid: Optional[str] = Field()
    created_at_from: date = Field(default=None)
    created_at_to: date = Field(default=None)
