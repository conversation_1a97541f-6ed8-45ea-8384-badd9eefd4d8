import datetime

import dateutil

from authentication.models import User
from kanna_api.query_params.property import PropertyParam
from kanna_api.utils.compare import contain_ja
from kanna_api.utils.multi_filter import MultiFilter

from .common import KaRequests


def get_properties(user: User):
    """KANNA物件全件取得"""

    response = KaRequests(user).get("/v1/properties", params={"page": 1, "limit": 100})
    response_data = response.json()
    total_page = response.headers.get("total_page")
    properties = response_data.pop("properties")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            "/v1/properties", params={"page": page, "limit": 100}
        )
        res_data = res.json()
        properties += res_data.pop("properties")

    return properties


def get_property(user: User, property_uuid: str):
    """KANNA物件詳細取得"""
    response = KaRequests(user).get(f"/v1/properties/{property_uuid}")
    return response.json()


def update_property(user: User, property_uuid: str, data: dict):
    """KANNA物件更新"""
    response = KaRequests(user).patch(f"/v1/properties/{property_uuid}", json=data)
    return response.json()


def property_filter(params: PropertyParam, response_data: list[dict]):
    multi_filter = MultiFilter()
    # timezoneをJSTに設定
    jst = datetime.timezone(datetime.timedelta(hours=+9), "JST")
    # 物件名
    if params.name:
        multi_filter.add(
            lambda property: contain_ja(params.name, property.get("name", ""))
        )
    # 物件コード
    if params.code:
        multi_filter.add(lambda property: params.code in property.get("code", ""))
    # 物件種別
    if params.kind:
        multi_filter.add(lambda property: property.get("kind", "") == params.kind.value)
    # 住所
    if params.address:
        multi_filter.add(
            lambda property: contain_ja(params.address, property.get("address", ""))
        )
    # 登録日（開始）
    if params.created_at_from:
        multi_filter.add(
            lambda property: property.get("created_at")
            and params.created_at_from
            <= dateutil.parser.parse(property.get("created_at")).astimezone(jst).date()
        )
    # 登録日（終了）
    if params.created_at_to:
        multi_filter.add(
            lambda property: property.get("created_at")
            and params.created_at_to
            >= dateutil.parser.parse(property.get("created_at")).astimezone(jst).date()
        )
    response_data = multi_filter.run(response_data)
    return response_data
