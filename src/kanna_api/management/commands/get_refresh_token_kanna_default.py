import logging
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Q

from kanna_api.authentication_kanna.auth import get_token_refresh
from kanna_api.models import UserKanna

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    def handle(self, *args, **options):
        today = datetime.now()
        print("Get refresh token day: ", today)

        try:
            user_kanna = UserKanna.objects.get(
                Q(user__email=settings.USER_KANNA_EMAIL_TO_CREATE_CMS)
            )
        except Exception as e:
            logger.exception(e)
            print("User kanna not found")
            return

        refresh_token = user_kanna.ka_refresh_token
        new_token = get_token_refresh(refresh_token)
        user_kanna.ka_refresh_token = new_token["refresh_token"]
        user_kanna.ka_access_token = new_token["access_token"]
        user_kanna.save()

        print("Done\n")
