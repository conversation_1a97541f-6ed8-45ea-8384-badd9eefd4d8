from django.core.management.base import BaseCommand

from ...models import UserWhitelist


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("email", type=str)
        parser.add_argument("--role_id", type=int)
        parser.add_argument("--office_id", type=int)

    def handle(self, *args, **options):
        email: str = options.get("email")
        role_id: int = options.get("role_id")
        office_id: int = options.get("office_id")
        UserWhitelist.objects.create(
            email=email,
            role_id=role_id,
            business_office_id=office_id,
            created_by="ce24c675-854c-1945-30ef-955aa9c4c1fc",
        )
