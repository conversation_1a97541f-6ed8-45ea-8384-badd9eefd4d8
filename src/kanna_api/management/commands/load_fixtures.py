import glob
import os

from django.conf import settings
from django.core.management import call_command
from django.core.management.base import BaseCommand

fixture_root_dir = os.path.join(settings.BASE_DIR, "fixtures")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("path", nargs="?", default="")

    def handle(self, *args, **options):
        path: str = options.get("path")

        if not path:
            fixture_dir = os.path.join(fixture_root_dir, "**", "*.json")
        elif path.endswith(".json"):
            fixture_dir = os.path.join(fixture_root_dir, path)
        else:
            fixture_dir = os.path.join(fixture_root_dir, path, "**", "*.json")

        # ファイル名取得、ソート
        fixture_files = glob.glob(fixture_dir, recursive=True)
        fixture_files.sort()

        # fixture読み込み
        test_dir = os.path.join(fixture_root_dir, "test")
        for fixture_file in fixture_files:
            # パスの指定なしの場合、testフォルダ除外
            if not path and fixture_file.startswith(test_dir):
                continue
            call_command("loaddata", fixture_file)
