# Generated by Django 4.0.6 on 2022-08-25 12:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('kanna_api', '0002_userkanna_company_name_userkanna_company_uuid_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userkanna',
            name='is_active',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='userkanna',
            name='role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='role', to='kanna_api.role'),
        ),
        migrations.AlterField(
            model_name='role',
            name='created_by',
            field=models.UUIDField(blank=True, null=True),
        ),
    ]
