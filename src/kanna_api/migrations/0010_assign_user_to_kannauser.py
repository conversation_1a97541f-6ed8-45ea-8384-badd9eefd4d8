# Generated by Django 4.1 on 2022-09-20 09:11

from django.db import migrations


def unassigned_user_to_kanna_user(apps, schema_editor):
    kanna_user_model = apps.get_model("kanna_api", "UserKanna")
    kanna_user_model.objects.update(user=None)


def assigned_user_to_kanna_user(apps, schema_editor):
    user_model = apps.get_model("authentication", "User")
    kanna_user_model = apps.get_model("kanna_api", "UserKanna")
    users = user_model.objects.all()
    for user in users:
        try:
            kanna_user = kanna_user_model.objects.get(email=user.email)
            kanna_user.user = user
            kanna_user.save()
        except Exception:
            pass


class Migration(migrations.Migration):
    dependencies = [
        ("kanna_api", "0009_userkanna_user"),
    ]

    operations = [
        migrations.RunPython(assigned_user_to_kanna_user, reverse_code=unassigned_user_to_kanna_user),
    ]
