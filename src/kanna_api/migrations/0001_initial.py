# Generated by Django 4.1 on 2022-08-07 09:42

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone

from django.core.management import call_command

def load_fixtures(apps, schema_editor):
    call_command('loaddata', 'fixtures/00-meta/00-authority.json')
    call_command('loaddata', 'fixtures/00-meta/01-businessoffice.json')
    call_command('loaddata', 'fixtures/01-master/00-role.json')


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Authority",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("app_div", models.IntegerField(choices=[(1, "ADMIN"), (2, "CLIENT")])),
                ("name", models.Char<PERSON>ield(max_length=50)),
                ("key", models.Char<PERSON>ield(max_length=50)),
                ("order", models.PositiveInteger<PERSON>ield()),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="kanna_api.authority",
                    ),
                ),
            ],
            options={
                "db_table": "authority",
            },
        ),
        migrations.CreateModel(
            name="BusinessOffice",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("code", models.IntegerField()),
                ("name", models.CharField(max_length=50)),
            ],
            options={
                "db_table": "business_office",
            },
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("name", models.CharField(max_length=50)),
                ("created_by", models.UUIDField()),
                ("updated_by", models.UUIDField(blank=True, null=True)),
            ],
            options={
                "db_table": "role",
            },
        ),
        migrations.CreateModel(
            name="UserKanna",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("user_uuid", models.UUIDField(unique=True)),
                ("ka_access_token", models.TextField()),
                ("ka_refresh_token", models.TextField()),
                ("last_login_at", models.DateTimeField(blank=True, null=True)),
                ("created_by", models.UUIDField()),
                ("updated_by", models.UUIDField(blank=True, null=True)),
                (
                    "business_office",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="userKanna",
                        to="kanna_api.businessoffice",
                    ),
                ),
            ],
            options={
                "db_table": "user_kanna",
            },
        ),
        migrations.CreateModel(
            name="UserWhitelist",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("created_by", models.UUIDField()),
                ("updated_by", models.UUIDField(blank=True, null=True)),
                (
                    "business_office",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="user_whitelists",
                        to="kanna_api.businessoffice",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="user_whitelists",
                        to="kanna_api.role",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_whitelists",
                        to="kanna_api.userkanna",
                    ),
                ),
            ],
            options={
                "db_table": "user_whitelist",
            },
        ),
        migrations.CreateModel(
            name="UserToken",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                ("token", models.TextField()),
                ("expires_at", models.DateTimeField()),
                ("jti", models.UUIDField(unique=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_tokens",
                        to="kanna_api.userkanna",
                    ),
                ),
            ],
            options={
                "db_table": "user_token",
            },
        ),
        migrations.CreateModel(
            name="UserRole",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="user_roles",
                        to="kanna_api.role",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_roles",
                        to="kanna_api.userkanna",
                    ),
                ),
            ],
            options={
                "db_table": "user_role",
            },
        ),
        migrations.AddField(
            model_name="userkanna",
            name="roles",
            field=models.ManyToManyField(
                related_name="UserKanna",
                through="kanna_api.UserRole",
                to="kanna_api.role",
            ),
        ),
        migrations.CreateModel(
            name="RoleAuthority",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "authority",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_authorities",
                        to="kanna_api.authority",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_authorities",
                        to="kanna_api.role",
                    ),
                ),
            ],
            options={
                "db_table": "role_authority",
            },
        ),
        migrations.AddField(
            model_name="role",
            name="authorities",
            field=models.ManyToManyField(
                related_name="roles",
                through="kanna_api.RoleAuthority",
                to="kanna_api.authority",
            ),
        ),
        migrations.AddConstraint(
            model_name="userrole",
            constraint=models.UniqueConstraint(
                fields=("user", "role"), name="unique_user_role"
            ),
        ),
        migrations.AddConstraint(
            model_name="roleauthority",
            constraint=models.UniqueConstraint(
                fields=("role", "authority"), name="unique_role_authority"
            ),
        ),
    ]
