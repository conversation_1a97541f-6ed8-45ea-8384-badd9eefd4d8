# Generated by Django 4.1.3 on 2022-12-09 09:47

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("kanna_api", "0016_alter_userkanna_address_alter_userkanna_name_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userkanna",
            name="email",
            field=mirage.fields.EncryptedEmailField(
                db_index=True, max_length=254, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="userkanna",
            name="level",
            field=mirage.fields.EncryptedCharField(
                blank=True,
                choices=[
                    ("LEVEL_1", "Level 1"),
                    ("LEVEL_2", "Level 2"),
                    ("LEVEL_3", "Level 3"),
                ],
                max_length=255,
                null=True,
            ),
        ),
    ]
