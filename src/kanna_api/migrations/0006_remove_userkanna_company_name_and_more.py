# Generated by Django 4.0.6 on 2022-08-29 09:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_initial'),
        ('kanna_api', '0005_userkanna_level_userkanna_pic_role_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userkanna',
            name='company_name',
        ),
        migrations.RemoveField(
            model_name='userkanna',
            name='company_uuid',
        ),
        migrations.AddField(
            model_name='userkanna',
            name='address',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='userkanna',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company', to='companies.company'),
        ),
        migrations.AddField(
            model_name='userkanna',
            name='tel',
            field=models.CharField(blank=True, default='', max_length=30, null=True),
        ),
    ]
