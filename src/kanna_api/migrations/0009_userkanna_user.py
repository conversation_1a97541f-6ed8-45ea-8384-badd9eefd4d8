# Generated by Django 4.1 on 2022-10-02 14:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("kanna_api", "0008_remove_userkanna_business_office_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userkanna",
            name="user",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="kanna_user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
