# Generated by Django 4.0.6 on 2022-08-24 09:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('kanna_api', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userkanna',
            name='company_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='userkanna',
            name='company_uuid',
            field=models.UUIDField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userkanna',
            name='email',
            field=models.EmailField(default='', max_length=254, unique=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='userkanna',
            name='name',
            field=models.CharField(default='', max_length=50),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='userkanna',
            name='business_office',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='userKanna', to='kanna_api.businessoffice'),
        ),
        migrations.AlterField(
            model_name='userkanna',
            name='created_by',
            field=models.UUIDField(blank=True, null=True),
        ),
    ]
