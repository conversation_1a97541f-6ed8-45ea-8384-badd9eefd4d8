# Generated by Django 4.1.3 on 2022-12-13 02:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("kanna_api", "0017_alter_userkanna_email_alter_userkanna_level"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="userkanna",
            name="email",
            field=models.EmailField(max_length=254, unique=True),
        ),
        migrations.AlterField(
            model_name="userkanna",
            name="level",
            field=models.CharField(
                blank=True,
                choices=[
                    ("LEVEL_1", "Level 1"),
                    ("LEVEL_2", "Level 2"),
                    ("LEVEL_3", "Level 3"),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
