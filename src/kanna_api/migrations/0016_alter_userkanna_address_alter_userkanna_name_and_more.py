# Generated by Django 4.1.3 on 2022-12-07 15:02

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("kanna_api", "0015_alter_userkanna_name"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userkanna",
            name="address",
            field=mirage.fields.EncryptedCharField(
                blank=True, default="", max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="userkanna",
            name="name",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="userkanna",
            name="pic_position",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="userkanna",
            name="tel",
            field=mirage.fields.EncryptedCharField(
                blank=True, default="", max_length=255, null=True
            ),
        ),
    ]
