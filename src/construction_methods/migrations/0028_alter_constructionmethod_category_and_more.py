# Generated by Django 4.1.3 on 2022-12-13 02:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("construction_methods", "0027_alter_constructionmethod_warranty_year_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="constructionmethod",
            name="category",
            field=models.CharField(
                choices=[("SDS_ROOF", "屋根"), ("SDS_OUTER_WALL", "外壁")], max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="complete",
            field=models.CharField(
                blank=True,
                choices=[
                    ("TRANSPARENT", "透明"),
                    ("ENAMEL", "エナメル"),
                    ("COMPLETE_MATTE", "完全艶消"),
                    ("CERAMIC_STEEL", "セラミック鋼"),
                    ("STONE_TONE", "石材調"),
                    ("NATURAL_STONE", "天然石調"),
                    ("CLAY_WALL", "土壁調"),
                    ("CLEAR", "クリヤー"),
                ],
                max_length=125,
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="warranty_year_type",
            field=models.IntegerField(
                choices=[(10, "10年保証対象"), (15, "15年保証仕様")], default=10
            ),
        ),
    ]
