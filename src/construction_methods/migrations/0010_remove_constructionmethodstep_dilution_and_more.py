# Generated by Django 4.1 on 2022-09-26 14:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("construction_methods", "0009_alter_constructionmethodstep_dilution_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="constructionmethodstep",
            name="dilution",
        ),
        migrations.RemoveField(
            model_name="constructionmethodstep",
            name="paint_distance",
        ),
        migrations.RemoveField(
            model_name="constructionmethodstep",
            name="required_volume",
        ),
        migrations.AddField(
            model_name="constructionmethodstep",
            name="dilution_max",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="constructionmethodstep",
            name="dilution_min",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="constructionmethodstep",
            name="paint_distance_max",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="constructionmethodstep",
            name="paint_distance_min",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="constructionmethodstep",
            name="required_volume_max",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="constructionmethodstep",
            name="required_volume_min",
            field=models.FloatField(blank=True, null=True),
        ),
    ]
