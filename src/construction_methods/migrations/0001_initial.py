# Generated by Django 4.1 on 2022-09-13 03:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("materials", "0001_initial"),
        ("application_bases", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ConstructionMethod",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("spec_name", models.Char<PERSON>ield(max_length=255)),
                ("glossy_paint", models.BooleanField(default=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("SDS_ROOF", "Roof Dianostis"),
                            ("SDS_OUTER_WALL", "Outer Wall Dianostis"),
                        ],
                        max_length=255,
                    ),
                ),
                ("washing", models.CharField(max_length=255)),
                ("note", models.CharField(blank=True, max_length=1000, null=True)),
                ("number_step", models.IntegerField()),
                (
                    "complete",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("TRANSPARENT", "Transparent"),
                            ("DURABLE_PAINT_COLOR", "Durable Paint Color"),
                            ("FOG", "Fog"),
                        ],
                        max_length=125,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "application_base",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="Construction_method_application_base",
                        to="application_bases.applicationbase",
                    ),
                ),
            ],
            options={
                "db_table": "construction_method",
            },
        ),
        migrations.CreateModel(
            name="ConstructionMethodStep",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("PAINT_BOTTOM_1", "Paint Bottom 1"),
                            ("PAINT_BOTTOM_2", "Paint Bottom 2"),
                            ("PAINT_MIDDLE", "Paint Middle"),
                            ("PAINT_OVER_1", "Paint Over 1"),
                            ("PAINT_OVER_2", "Paint Over 2"),
                        ],
                        max_length=255,
                    ),
                ),
                ("dilution", models.FloatField()),
                ("required_volume", models.FloatField()),
                ("number_paints", models.IntegerField()),
                ("paint_distance", models.FloatField()),
                ("methods_implement", models.CharField(max_length=1000)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "construction_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="construction_method_step",
                        to="construction_methods.constructionmethod",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="Construction_methed_step_material",
                        to="materials.materials",
                    ),
                ),
            ],
            options={
                "db_table": "construction_method_step",
            },
        ),
    ]
