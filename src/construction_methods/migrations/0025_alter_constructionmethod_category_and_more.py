# Generated by Django 4.1.3 on 2022-11-29 07:38

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("construction_methods", "0024_import-master-data-construction-method-step"),
    ]

    operations = [
        migrations.AlterField(
            model_name="constructionmethod",
            name="category",
            field=mirage.fields.EncryptedCharField(
                choices=[("SDS_ROOF", "屋根"), ("SDS_OUTER_WALL", "外壁")], max_length=125
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="complete",
            field=mirage.fields.EncryptedCharField(
                blank=True,
                choices=[
                    ("TRANSPARENT", "透明"),
                    ("ENAMEL", "エナメル"),
                    ("COMPLETE_MATTE", "完全艶消"),
                    ("CERAMIC_STEEL", "セラミック鋼"),
                    ("STONE_TONE", "石材調"),
                    ("NATURAL_STONE", "天然石調"),
                    ("CLAY_WALL", "土壁調"),
                    ("CLEAR", "クリヤー"),
                ],
                max_length=125,
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="number_step",
            field=mirage.fields.EncryptedIntegerField(max_length=64),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="spec_name",
            field=mirage.fields.EncryptedCharField(max_length=255),
        ),
        # migrations.AlterField(
        #     model_name="constructionmethod",
        #     name="warranty_year_type",
        #     field=mirage.fields.EncryptedCharField(
        #         choices=[(10, "10年保証対象"), (15, "15年保証仕様")], default=10, max_length=255
        #     ),
        # ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="washing",
            field=mirage.fields.EncryptedCharField(max_length=500),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="dilution",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="methods_implement",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=1000, null=True
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="number_paints",
            field=mirage.fields.EncryptedIntegerField(
                blank=True, max_length=64, null=True
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="paint_distance_date",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="paint_distance_min",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="required_volume_max",
            field=mirage.fields.EncryptedCharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="required_volume_min",
            field=mirage.fields.EncryptedCharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="type",
            field=mirage.fields.EncryptedCharField(
                choices=[
                    ("PAINT_BOTTOM_1", "下塗1工程"),
                    ("PAINT_BOTTOM_2", "下塗2工程"),
                    ("PAINT_MIDDLE", "中塗工程"),
                    ("PAINT_OVER_1", "上塗1工程"),
                    ("PAINT_OVER_2", "上塗2工程"),
                ],
                max_length=255,
            ),
        ),
    ]
