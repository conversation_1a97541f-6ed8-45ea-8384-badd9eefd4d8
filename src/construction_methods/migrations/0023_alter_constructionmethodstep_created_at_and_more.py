# Generated by Django 4.1.2 on 2022-11-20 07:00

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("construction_methods", "0022_import-master-data-construction-method"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="constructionmethodstep",
            name="created_at",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="paint_distance_date",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="constructionmethodstep",
            name="paint_distance_min",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="required_volume_max",
            field=models.Char<PERSON>ield(max_length=255),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="constructionmethodstep",
            name="required_volume_min",
            field=models.Char<PERSON><PERSON>(max_length=255),
        ),
        migrations.Alter<PERSON>ield(
            model_name="constructionmethodstep",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
