# Generated by Django 4.1.2 on 2022-12-29 08:25

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("construction_methods", "0029_alter_constructionmethod_category_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="constructionmethod",
            name="complete",
            field=mirage.fields.EncryptedCharField(
                blank=True,
                choices=[
                    ("TRANSPARENT", "透明"),
                    ("ENAMEL", "エナメル"),
                    ("COMPLETE_MATTE", "完全艶消"),
                    ("CERAMIC_STEEL", "セラミック鋼"),
                    ("STONE_TONE", "石材調"),
                    ("NATURAL_STONE", "天然石調"),
                    ("CLAY_WALL", "土壁調"),
                    ("CLEAR", "クリヤー"),
                ],
                max_length=125,
                null=True,
            ),
        ),
    ]
