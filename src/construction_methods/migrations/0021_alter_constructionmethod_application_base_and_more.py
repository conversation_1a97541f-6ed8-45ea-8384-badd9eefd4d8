# Generated by Django 4.1.2 on 2022-11-19 09:52

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("application_bases", "0003_auto_20221119_1605"),
        ("construction_methods", "0020_alter_constructionmethod_warranty_year_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="constructionmethod",
            name="application_base",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="construction_method_application_base",
                to="application_bases.applicationbase",
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="category",
            field=models.CharField(
                choices=[("SDS_ROOF", "屋根"), ("SDS_OUTER_WALL", "外壁")], max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="complete",
            field=models.CharField(
                blank=True,
                choices=[
                    ("TRANSPARENT", "透明"),
                    ("ENAMEL", "エナメル"),
                    ("COMPLETE_MATTE", "完全艶消"),
                    ("CERAMIC_STEEL", "セラミック鋼"),
                    ("STONE_TONE", "石材調"),
                    ("NATURAL_STONE", "天然石調"),
                    ("CLAY_WALL", "土壁調"),
                    ("CLEAR", "クリヤー"),
                ],
                max_length=125,
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="created_at",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name="constructionmethod",
            name="warranty_year_type",
            field=models.IntegerField(
                choices=[(10, "10年保証対象"), (15, "15年保証仕様")], default=10
            ),
        ),
        migrations.AlterField(
            model_name="constructionmethodstep",
            name="type",
            field=models.CharField(
                choices=[
                    ("PAINT_BOTTOM_1", "下塗1工程"),
                    ("PAINT_BOTTOM_2", "下塗2工程"),
                    ("PAINT_MIDDLE", "中塗工程"),
                    ("PAINT_OVER_1", "上塗1工程"),
                    ("PAINT_OVER_2", "上塗2工程"),
                ],
                max_length=255,
            ),
        ),
    ]
