from django.contrib import admin

from .models import ConstructionMethod, ConstructionMethodStep


class ConstructionMethodAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "spec_name",
        "category",
        "washing",
        "note",
        "number_step",
        "complete",
        "application_base",
        "updated_at",
        "created_at",
    ]


admin.site.register(ConstructionMethod, ConstructionMethodAdmin)


class ConstructionMethodStepAdmin(admin.ModelAdmin):
    list_display = [
        "type",
        "dilution",
        "required_volume_min",
        "required_volume_max",
        "number_paints",
        "paint_distance_min",
        "paint_distance_date",
        "methods_implement",
        "construction_method_id",
        "material_id",
    ]


admin.site.register(ConstructionMethodStep, ConstructionMethodStepAdmin)
