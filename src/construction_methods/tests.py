from django.core.management import call_command
from django.test import TestCase
from faker import Faker
from rest_framework.test import APIClient

from api.constants import KannaUserRoleEnum
from authentication.factories import UserFactory
from construction_methods.factories import ConstructionMethodFactory
from construction_methods.models import ConstructionMethod
from kanna_api.models import UserKanna


class ConstructionMethodTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        call_command("loaddata", "fixtures/01-master/00-role.json")

    def setUp(self):
        self.faker = Faker()
        admin_email = self.faker.email()
        normal_user_email = self.faker.email()
        self.admin_user = UserFactory(email=admin_email)
        UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=admin_email,
            name=self.faker.name(),
            role_id=KannaUserRoleEnum.ADMIN.value,
            user=self.admin_user,
        )
        self.normal_user = UserFactory(email=normal_user_email)
        UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=normal_user_email,
            name=self.faker.name(),
            role_id=KannaUserRoleEnum.SHOP.value,
            user=self.normal_user,
        )

        self.client = APIClient()
        self.method1 = ConstructionMethodFactory(spec_name="Test1")
        self.method2 = ConstructionMethodFactory(spec_name="Test2", is_active=False)

        self.view_url = "/api/v1/construction_methods/"

    def test_is_active_default(self):
        method = ConstructionMethod.objects.create(spec_name="DefaultActive")
        self.assertTrue(method.is_active)

    def test_list_api_filter_is_active(self):
        self.client.force_authenticate(user=self.admin_user)
        resp = self.client.get(f"{self.view_url}?is_active=true")
        self.assertEqual(resp.status_code, 200)
        ids = [item["id"] for item in resp.data["results"]]
        self.assertIn(self.method1.id, ids)
        self.assertNotIn(self.method2.id, ids)

    def test_toggle_api_admin(self):
        self.client.force_authenticate(user=self.admin_user)
        resp = self.client.post(
            f"{self.view_url}{self.method1.id}/toggle_is_active/",
            {"is_active": False},
            format="json",
        )
        self.assertEqual(resp.status_code, 200)
        self.method1.refresh_from_db()
        self.assertFalse(self.method1.is_active)

    def test_toggle_api_permission_denied_for_non_admin(self):
        self.client.force_authenticate(user=self.normal_user)
        resp = self.client.post(
            f"{self.view_url}{self.method1.id}/toggle_is_active/",
            {"is_active": False},
            format="json",
        )
        self.assertEqual(resp.status_code, 403)
