from distutils.util import strtobool
from rest_framework import status, viewsets, decorators
from utilities.permissions.custom_permission import IsKannaAdminPermission
from rest_framework.response import Response

from django.db.models import Q

from construction_methods.models import (
    ConstructionMethod,
    ConstructionMethodStep,
)
from construction_methods.serializers import (
    ConstructionMethodSerializer,
    ListConstructionMethodSerializer,
    ToggleIsActiveSerializer,
)
from diagnostics.models import Diagnostic
from logging import getLogger

logger = getLogger(__name__)


class ConstructionMedthodViewSet(viewsets.ModelViewSet):
    serializer_class = ConstructionMethodSerializer
    http_method_names = ["get", "post", "put", "delete"]

    def get_serializer_class(self):
        if self.action == "list" or self.action == "retrieve":
            return ListConstructionMethodSerializer

        return super().get_serializer_class()

    def get_queryset(self):
        queryset = ConstructionMethod.objects.all()
        queryset = queryset.select_related("application_base").prefetch_related(
            "construction_method_step__material"
        )
        query_param = self.request.query_params
        if "application_base" in query_param:
            queryset = queryset.filter(
                Q(application_base=query_param["application_base"])
            )
        if "spec_name" in query_param:
            construction_method_ids = []
            spec_name_query = query_param["spec_name"].lstrip().rstrip()
            for construction_method in queryset:
                if spec_name_query in construction_method.spec_name:
                    construction_method_ids.append(construction_method.id)
            queryset = queryset.filter(Q(id__in=construction_method_ids))
        if "category" in query_param:
            queryset = queryset.filter(Q(category=query_param["category"]))
        if "complete" in query_param:
            queryset = queryset.filter(Q(complete=query_param["complete"]))
        if "is_glossy_paint" in query_param:
            queryset = queryset.filter(
                Q(is_glossy_paint=query_param["is_glossy_paint"].capitalize())
            )
        if "material" in query_param:
            queryset = queryset.filter(
                Q(construction_method_step__material__name=query_param["material"])
            ).distinct()
        if "is_active" in query_param:
            try:
                val = bool(strtobool(query_param["is_active"]))
                queryset = queryset.filter(is_active=val)
            except ValueError:
                logger.warning(f"Invalid is_active value: {query_param['is_active']}")
        if "diagnostic_id" in query_param:
            try:
                diagnostic_id = int(query_param["diagnostic_id"])
                queryset = queryset.filter(
                    Q(diagnosticconstructionmethod__diagnostic_id=diagnostic_id) |
                    Q(is_active=True)
                ).distinct()
            except ValueError:
                logger.warning(f"Invalid diagnostic_id value: {query_param['diagnostic_id']}")

        return queryset

    @decorators.action(
        detail=True, methods=["post"], permission_classes=[IsKannaAdminPermission]
    )
    def toggle_is_active(self, request, *args, **kwargs):
        """Toggle or set is_active for a construction method (admin only)."""
        instance = self.get_object()
        serializer = ToggleIsActiveSerializer(instance=instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):

        instance = self.get_object()
        diagnostic = Diagnostic.objects.filter(
            diagnostic_construction_methods__construction_method=instance,
            is_deleted=False,
        )

        if diagnostic:
            return Response(
                {
                    "message": "この工法は既に利用されているので、削除できません。",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        self.perform_destroy(instance)

        return Response(status=status.HTTP_204_NO_CONTENT)

    def perform_destroy(self, instance):
        construnction_method_step = ConstructionMethodStep.objects.filter(
            construction_method_id=instance.id
        )
        construnction_method_step.update(is_deleted=True)
        instance.delete()
