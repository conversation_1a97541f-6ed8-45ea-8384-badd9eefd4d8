from typing import List

from rest_framework import serializers

from application_bases.serializers import ApplicationBaseSerializer
from construction_methods.models import (
    ConstructionMethod,
    ConstructionMethodStep,
    ConstructionStepType,
)
from diagnostics.models import Diagnostic
from materials.models import Materials


class MaterialDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Materials
        fields = ["id", "name", "capacity", "type"]


class ConstructionStepSerializer(serializers.ModelSerializer):
    material = MaterialDetailSerializer(read_only=True)

    class Meta:
        model = ConstructionMethodStep
        fields = [
            "id",
            "type",
            "dilution",
            "required_volume_min",
            "required_volume_max",
            "number_paints",
            "paint_distance_min",
            "paint_distance_date",
            "methods_implement",
            "construction_method_id",
            "material",
        ]


class ConstructionMethodStepSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False, allow_null=True)
    material_id = serializers.IntegerField()
    type_step = serializers.ChoiceField(
        choices=ConstructionStepType.choices, default=None
    )
    dilution = serializers.CharField(required=False, allow_null=True)
    required_volume_min = serializers.CharField()
    required_volume_max = serializers.CharField()
    number_paints = serializers.IntegerField(required=False, allow_null=True)
    paint_distance_min = serializers.CharField(required=False, allow_null=True)
    paint_distance_date = serializers.CharField(required=False, allow_null=True)
    methods_implement = serializers.CharField(required=False, allow_null=True)


class ListConstructionMethodSerializer(serializers.ModelSerializer):
    construction_method_step = ConstructionStepSerializer(many=True, read_only=True)
    application_base = ApplicationBaseSerializer(read_only=True)

    class Meta:
        model = ConstructionMethod
        fields = [
            "id",
            "spec_name",
            "category",
            "washing",
            "warranty_year_type",
            "note",
            "number_step",
            "complete",
            "application_base",
            "is_glossy_paint",
            "is_active",
            "construction_method_step",
            "updated_at",
            "created_at",
        ]

class ToggleIsActiveSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConstructionMethod
        fields = [
            "is_active",
        ]

class ConstructionMethodSerializer(serializers.ModelSerializer):
    construction_method_step = serializers.ListField(
        child=ConstructionMethodStepSerializer(), write_only=True
    )

    class Meta:
        model = ConstructionMethod
        fields = [
            "id",
            "spec_name",
            "is_glossy_paint",
            "is_active",
            "warranty_year_type",
            "category",
            "washing",
            "note",
            "complete",
            "application_base",
            "construction_method_step",
        ]

    def create(self, validate_data):
        steps = validate_data.pop("construction_method_step")
        number_step_count = 0

        for step in steps:
            if step["material_id"] is not None:
                number_step_count = number_step_count + 1

        validate_data["number_step"] = number_step_count

        constuction_method = ConstructionMethod.objects.create(**validate_data)
        arr_step: List[ConstructionMethodStep] = []

        for step in steps:
            step["constuction_method_id"] = constuction_method.id

            item = ConstructionMethodStep(
                **{
                    "type": step["type_step"],
                    "dilution": step["dilution"],
                    "required_volume_min": step["required_volume_min"],
                    "required_volume_max": step["required_volume_max"],
                    "number_paints": step["number_paints"],
                    "paint_distance_min": step["paint_distance_min"],
                    "paint_distance_date": step["paint_distance_date"],
                    "methods_implement": step["methods_implement"],
                    "construction_method_id": step["constuction_method_id"],
                    "material_id": step["material_id"],
                }
            )

            arr_step.append(item)

        ConstructionMethodStep.objects.bulk_create(arr_step)

        return constuction_method

    def update(self, instance, validated_data):

        # Check construction method using in Diagnostic
        if Diagnostic.objects.filter(
            diagnostic_construction_methods__construction_method=instance,
            is_deleted=False
        ).exists():
            raise serializers.ValidationError(
                {"message": "この工法は既に利用されているので、編集できません。"},
            )

        # save construction method info
        steps = validated_data.pop("construction_method_step")
        number_step_count = 0
        for step in steps:
            if step["material_id"] is not None:
                number_step_count = number_step_count + 1
        instance.number_step = number_step_count
        super().update(instance, validated_data)

        # save construction method step info
        ConstructionMethodStep.objects.filter(
            construction_method_id=instance.id
        ).delete()

        arr_step: List[ConstructionMethodStep] = []

        for step in steps:
            step["constuction_method_id"] = instance.id

            item = ConstructionMethodStep(
                **{
                    "type": step["type_step"],
                    "dilution": step["dilution"],
                    "required_volume_min": step["required_volume_min"],
                    "required_volume_max": step["required_volume_max"],
                    "number_paints": step["number_paints"],
                    "paint_distance_min": step["paint_distance_min"],
                    "paint_distance_date": step["paint_distance_date"],
                    "methods_implement": step["methods_implement"],
                    "construction_method_id": step["constuction_method_id"],
                    "material_id": step["material_id"],
                }
            )

            arr_step.append(item)

        ConstructionMethodStep.objects.bulk_create(arr_step)

        return instance
