from mirage import fields

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from application_bases.models import ApplicationBase
from materials.models import Materials
from utilities.model.custom_model import CustomModel


class ConstructionCategories(models.TextChoices):
    # 屋根
    ROOF_DIAGNOSTIC = ("SDS_ROOF", _("屋根"))
    # 外壁
    OUTER_WALL_DIANOSTIC = ("SDS_OUTER_WALL", _("外壁"))


class ConstructionStepType(models.TextChoices):
    # 下塗工程
    PAINT_BOTTOM_1 = ("PAINT_BOTTOM_1", _("下塗1工程"))
    PAINT_BOTTOM_2 = ("PAINT_BOTTOM_2", _("下塗2工程"))
    PAINT_MIDDLE = ("PAINT_MIDDLE", _("中塗工程"))
    PAINT_OVER_1 = ("PAINT_OVER_1", _("上塗1工程"))
    PAINT_OVER_2 = ("PAINT_OVER_2", _("上塗2工程"))


class CompleteChoices(models.TextChoices):
    TRANSPARENT = ("TRANSPARENT", _("透明"))
    ENAMEL = ("ENAMEL", _("エナメル"))
    COMPLETE_MATTE = ("COMPLETE_MATTE", _("完全艶消"))
    CERAMIC_STEEL = ("CERAMIC_STEEL", _("セラミック鋼"))
    STONE_TONE = ("STONE_TONE", _("石材調"))
    NATURAL_STONE = ("NATURAL_STONE", _("天然石調"))
    CLAY_WALL = ("CLAY_WALL", _("土壁調"))
    CLEAR = ("CLEAR", _("クリヤー"))


class WarrantyYearChoices(models.IntegerChoices):
    TEN_YEARS = 10, "10年保証対象"
    FIFTEEN_YEARS = (
        15,
        "15年保証仕様",
    )


class ConstructionMethod(CustomModel):
    id = models.BigAutoField(primary_key=True)
    # 仕様名（工法）
    spec_name = fields.EncryptedCharField(max_length=255)

    # 適用下地
    application_base = models.ForeignKey(
        ApplicationBase,
        to_field="id",
        related_name="construction_method_application_base",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    # warranty_year_type = models.IntegerField(
    #     choices=WarrantyYearChoices.choices,
    #     default=WarrantyYearChoices.TEN_YEARS,
    # )
    warranty_year_type = fields.EncryptedIntegerField(
        choices=WarrantyYearChoices.choices,
        default=WarrantyYearChoices.TEN_YEARS,
    )
    # category = models.CharField(max_length=255, choices=ConstructionCategories.choices)
    # complete = models.CharField(
    #     max_length=125,
    #     choices=CompleteChoices.choices,
    #     blank=True,
    # )
    # 艶
    is_glossy_paint = models.BooleanField(default=True)

    # タイプ
    category = fields.EncryptedCharField(
        max_length=125,
        choices=ConstructionCategories.choices,
    )
    # 洗浄
    washing = fields.EncryptedCharField(max_length=500)
    note = models.CharField(max_length=1000, null=True, blank=True)
    # 工程数
    number_step = fields.EncryptedIntegerField()
    # 仕上り

    complete = fields.EncryptedCharField(
        max_length=125,
        choices=CompleteChoices.choices,
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "construction_method"
        ordering = ["-id"]


class ConstructionMethodStep(CustomModel):
    id = models.BigAutoField(primary_key=True)
    # type = models.CharField(max_length=255, choices=ConstructionStepType.choices)
    type = fields.EncryptedCharField(
        max_length=255, choices=ConstructionStepType.choices
    )
    required_volume_min = fields.EncryptedCharField(max_length=255)
    required_volume_max = fields.EncryptedCharField(max_length=255)
    number_paints = fields.EncryptedIntegerField(null=True, blank=True)
    paint_distance_min = fields.EncryptedCharField(null=True, blank=True)
    paint_distance_date = fields.EncryptedCharField(null=True, blank=True)
    methods_implement = fields.EncryptedCharField(
        max_length=1000, null=True, blank=True
    )
    dilution = fields.EncryptedCharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    construction_method = models.ForeignKey(
        ConstructionMethod,
        to_field="id",
        related_name="construction_method_step",
        on_delete=models.DO_NOTHING,
    )
    material = models.ForeignKey(
        Materials,
        to_field="id",
        related_name="construction_method_step_material",
        on_delete=models.DO_NOTHING,
    )

    class Meta:
        db_table = "construction_method_step"
        ordering = ["-id"]
