# Generated by Django 4.1.2 on 2022-10-25 13:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("management_sheets", "0027_alter_actualconstruction_diagnostic_and_more"),
        (
            "warranty",
            "0003_remove_warrantyconstructionmethod_construction_methods_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="warranty",
            name="warranty_year_type",
            field=models.IntegerField(
                blank=True,
                choices=[(10, "Ten years"), (15, "fifteen years")],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="warranty",
            name="management_sheet",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="warranties",
                to="management_sheets.managementsheet",
            ),
        ),
    ]
