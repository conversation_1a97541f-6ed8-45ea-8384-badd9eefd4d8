# Generated by Django 4.1 on 2022-10-23 07:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("warranty", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="warranty",
            name="owner",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterModelTable(
            name="warranty",
            table="warranty",
        ),
        migrations.CreateModel(
            name="WarrantyConstructionMethod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "construction_methods",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warranty_construction_method",
                        to="construction_methods.constructionmethod",
                    ),
                ),
                (
                    "warranties",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warranty_construction_method",
                        to="warranty.warranty",
                    ),
                ),
            ],
            options={
                "db_table": "warranty_construction_method",
            },
        ),
    ]
