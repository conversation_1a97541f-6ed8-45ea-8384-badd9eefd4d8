# Generated by Django 4.0.6 on 2022-10-22 07:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('management_sheets', '0020_alter_actualconstructionmethod_construction_time'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Warranty',
            fields=[
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('RELEASED', 'Released')], default='OPEN', max_length=32)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('management_sheet', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='warranty_management_sheet', to='management_sheets.managementsheet')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='user', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='WarrantyDocument',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('kanna_uuid', models.UUIDField(blank=True, null=True, unique=True)),
                ('warranty', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='waranty', to='warranty.warranty')),
            ],
        ),
    ]
