from rest_framework import serializers
from typing import Dict, Any, List

from cms.services import get_cms_kanna, get_cms_list_detail
from users.models import UserConstruction
from warranty.models import Warranty, WarrantyYearChoices


def get_cms_data_for_warranties(
    warranties: List[Warranty], kanna_user
) -> Dict[str, Any]:
    """
    Fetch CMS data for multiple warranties in parallel.

    Args:
        warranties: List of warranty objects
        kanna_user: Kanna user for API authentication

    Returns:
        Dict mapping cms_uuid to cms_data, empty dict for failed requests
    """
    # Extract unique cms_uuids
    cms_uuids = list(
        set(
            [
                warranty.management_sheet.cms_uuid
                for warranty in warranties
                if warranty.management_sheet and warranty.management_sheet.cms_uuid
            ]
        )
    )

    if not cms_uuids:
        return {}

    # Fetch CMS data in parallel
    cms_data_list = get_cms_list_detail(cms_uuids, kanna_user)

    # Create mapping from cms_uuid to cms_data
    cms_data_map = {}
    for i, cms_uuid in enumerate(cms_uuids):
        if i < len(cms_data_list) and cms_data_list[i]:
            cms_data_map[cms_uuid] = cms_data_list[i]

    return cms_data_map


class WarrantySerializer(serializers.ModelSerializer):
    construction_no = serializers.SerializerMethodField(read_only=True)
    cms_title = serializers.SerializerMethodField(read_only=True)
    building_name = serializers.SerializerMethodField(read_only=True)
    start_warranty_date = serializers.SerializerMethodField(read_only=True)
    end_warranty_date = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Warranty
        fields = [
            "id",
            "construction_no",
            "owner",
            "warranty_year_type",
            "status",
            "created_at",
            "cms_title",
            "building_name",
            "start_warranty_date",
            "end_warranty_date",
        ]

    def get_construction_no(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.construction_no
        return None

    def get_cms_title(self, obj):
        # Check if cms_data is available in context
        cms_data_map = self.context.get("cms_data_map", {})

        if obj.management_sheet and obj.management_sheet.cms_uuid:
            cms_uuid = obj.management_sheet.cms_uuid
            cms_data = cms_data_map.get(cms_uuid)

            if cms_data and isinstance(cms_data, dict):
                return cms_data.get("title", "")

        # Fallback to original method if no cms_data in context
        if "request" in self.context:
            try:
                cms = get_cms_kanna(
                    obj.management_sheet.cms_uuid,
                    self.context["request"].user.kanna_user,
                )
                return cms.get("title", "") if cms else ""
            except Exception:
                return ""

        return ""

    def get_building_name(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.building_name
        return None

    def get_start_warranty_date(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.start_warranty_date
        return None

    def get_end_warranty_date(self, obj):
        if obj.management_sheet is not None:
            if obj.warranty_year_type == WarrantyYearChoices.TEN_YEARS:
                return obj.management_sheet.ten_years_end_warranty_date
            if obj.warranty_year_type == WarrantyYearChoices.FIFTEEN_YEARS:
                return obj.management_sheet.fifteen_years_end_warranty_date
        return None


class WarrantyOwnerSerializer(serializers.ModelSerializer):
    cms_title = serializers.SerializerMethodField(read_only=True)
    construction_no = serializers.SerializerMethodField(read_only=True)
    cms_title = serializers.SerializerMethodField(read_only=True)
    building_name = serializers.SerializerMethodField(read_only=True)
    start_warranty_date = serializers.SerializerMethodField(read_only=True)
    end_warranty_date = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Warranty
        fields = [
            "id",
            "construction_no",
            "warranty_year_type",
            "status",
            "owner",
            "cms_title",
            "building_name",
            "start_warranty_date",
            "end_warranty_date",
            "pdf_url",
            "created_at",
        ]

    def get_cms_title(self, obj):
        cms_title = ""
        if "request" in self.context:
            cms_title = (
                UserConstruction.objects.values_list("construction_name", flat=True)
                .filter(construction_uuid=obj.management_sheet.cms_uuid)
                .first()
            )
        return cms_title

    def get_construction_no(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.construction_no
        return None

    def get_building_name(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.building_name
        return None

    def get_start_warranty_date(self, obj):
        if obj.management_sheet is not None:
            return obj.management_sheet.start_warranty_date
        return None

    def get_end_warranty_date(self, obj):
        if obj.management_sheet is not None:
            if obj.warranty_year_type == WarrantyYearChoices.TEN_YEARS:
                return obj.management_sheet.ten_years_end_warranty_date
            if obj.warranty_year_type == WarrantyYearChoices.FIFTEEN_YEARS:
                return obj.management_sheet.fifteen_years_end_warranty_date
        return None


class WarrantyByConstructionSerializer(serializers.ModelSerializer):
    ten_years_end_warranty_date = serializers.SerializerMethodField(read_only=True)
    fifteen_years_end_warranty_date = serializers.SerializerMethodField(read_only=True)
    cms_title = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Warranty
        fields = [
            "id",
            "warranty_year_type",
            "status",
            "created_at",
            "ten_years_end_warranty_date",
            "fifteen_years_end_warranty_date",
            "cms_title",
        ]

    def get_ten_years_end_warranty_date(self, obj):
        management_sheet = obj.management_sheet
        return management_sheet.ten_years_end_warranty_date

    def get_fifteen_years_end_warranty_date(self, obj):
        management_sheet = obj.management_sheet
        return management_sheet.fifteen_years_end_warranty_date

    def get_cms_title(self, obj):
        return obj.management_sheet.building_name
