from django.contrib import admin

from warranty.models import Warranty, WarrantyConstructionMethod


class WarrantyAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "owner",
        "management_sheet",
        "status",
        "created_at",
    ]
    search_fields = ["id", "owner_id", "management_sheet_id"]


class WarrantyConstructionMethodAdmin(admin.ModelAdmin):
    list_display = [
        "warranties",
        "actual_constructions",
    ]


admin.site.register(Warranty, WarrantyAdmin)
admin.site.register(WarrantyConstructionMethod, WarrantyConstructionMethodAdmin)
