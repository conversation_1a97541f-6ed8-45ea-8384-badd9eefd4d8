from django.db import models

from authentication.models import User
from utilities.model.custom_model import CustomModel


class WarrantyYearChoices(models.IntegerChoices):
    TEN_YEARS = 10, "Ten years"
    FIFTEEN_YEARS = (
        15,
        "fifteen years",
    )


class WarrantyStatusChoices(models.TextChoices):
    OPEN = ("OPEN",)
    RELEASED = ("RELEASED",)


class Warranty(CustomModel):
    id = models.BigAutoField(primary_key=True)
    owner = models.ForeignKey(
        User, related_name="user", on_delete=models.DO_NOTHING, null=True, blank=True
    )
    management_sheet = models.ForeignKey(
        "management_sheets.ManagementSheet",
        related_name="warranties",
        on_delete=models.DO_NOTHING,
    )
    warranty_year_type = models.IntegerField(
        choices=WarrantyYearChoices.choices, blank=True, null=True
    )
    status = models.CharField(
        max_length=32,
        choices=WarrantyStatusChoices.choices,
        default=WarrantyStatusChoices.OPEN,
    )
    pdf_url = models.URLField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "warranty"
        ordering = ["-id"]


class WarrantyConstructionMethod(CustomModel):
    warranties = models.ForeignKey(
        Warranty,
        related_name="warranty_construction_method",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    actual_constructions = models.ForeignKey(
        "management_sheets.ActualConstruction",
        related_name="warranty_construction_method",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "warranty_construction_method"


class WarrantyDocument(models.Model):
    id = models.BigAutoField(primary_key=True)
    warranty = models.ForeignKey(
        Warranty,
        related_name="waranty",
        on_delete=models.DO_NOTHING,
    )
    kanna_uuid = models.UUIDField(unique=True, blank=True, null=True)
