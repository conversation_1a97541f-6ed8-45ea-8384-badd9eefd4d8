import csv
from time import strftime

from django.db.models import Q, TextField
from django.db.models.functions import Cast
from django.http import HttpResponse
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from api.constants import POSTAL_CODE_SYMBOL, KannaUserRoleEnum
from approve_flow.models import ApproveFlowSettingInstance
from authentication.models import User
from cms.cms import cms_filter, get_cms_list
from cms.serializers import CmsLiteSerializer
from cms.services import (
    get_cms_customize,
    get_cms_kanna,
    update_user_assign_cms,
)
from cms_document.models import DocumentDownloadInformation
from companies.models import Company, ShopDealer
from dashboard.utils import get_management_sheets_by_user
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.cms import CmsParam
from management_sheets.models import ActualConstruction, ManagementSheet
from templates.emails.mailer import send_mail_release_warranty
from users.models import UserConstruction
from utilities.json_convert.convert import camel_to_snake_json
from utilities.order_filtering.ordering import Ordering
from warranty.models import (
    Warranty,
    WarrantyStatusChoices,
    WarrantyYearChoices,
)
from warranty.serializers import WarrantySerializer, get_cms_data_for_warranties


class WarrantyViewSet(viewsets.ModelViewSet):
    # serializer_class = WarrantySerializer
    http_method_names = ["get", "put", "delete"]

    def get_serializer_class(self):
        if self.action == "construction_list":
            return CmsLiteSerializer

        return WarrantySerializer

    def get_queryset(self):
        user = self.request.user
        user_kanna = UserKanna.objects.filter(email=user).first()
        if user_kanna:
            update_user_assign_cms(user_kanna)
        # queryset = None
        if user_kanna.role_id == KannaUserRoleEnum.ADMIN.value:
            queryset = Warranty.objects.all()
        elif user_kanna.company:
            management_sheets_ids = get_management_sheets_by_user(user_kanna)
            queryset = Warranty.objects.filter(
                management_sheet_id__in=management_sheets_ids
            )
        status = self.request.query_params.get("status", None)
        if status is not None:
            queryset = queryset.filter(Q(status=status.upper()))

        building_name = self.request.query_params.get("building_name", None)
        if building_name is not None:
            queryset = queryset.filter(
                Q(management_sheet__building_name__icontains=building_name)
            )
        title = self.request.query_params.get("title", None)
        if title is not None:
            cms_uuids = queryset.values_list(
                "management_sheet__cms_uuid", flat=True
            ).distinct()
            kanna_user = user.kanna_user
            id_cms = []
            for cms_uuid in cms_uuids:
                cms = get_cms_kanna(cms_uuid, kanna_user)
                if "error" in cms:
                    continue
                if cms and title.lower() in cms["title"].lower():
                    id_cms.append(cms_uuid)

            queryset = queryset.filter(
                Q(management_sheet__cms_uuid__in=id_cms)
            ).distinct()

        return queryset

    def list(self, request, *args, **kwargs):
        # get query param
        queryset = self.filter_queryset(self.get_queryset())
        params = self.request.query_params.get("sort_by", None)

        if params:
            sort_by_list = {
                "id",
                "construction_no",
                "owner",
                "warranty_year_type",
                "status",
                "created_at",
                "cms_title",
                "building_name",
                "start_warranty_date",
                "end_warranty_date",
            }
            list_combine = []
            params_direction = self.request.query_params.get("order_by_direction", "")

            # Fetch CMS data for all warranties
            warranties = list(queryset)
            cms_data_map = get_cms_data_for_warranties(
                warranties, request.user.kanna_user
            )

            # get list ms on AGC
            serialize = self.get_serializer(
                queryset,
                many=True,
                context={"request": request, "cms_data_map": cms_data_map},
            )

            result_order = Ordering.order_filtering(
                serialize.data,
                params,
                params_direction,
                sort_by_list,
                list_combine,
                False,
            )

            page = self.paginate_queryset(result_order)

            if page is not None:
                return self.get_paginated_response(page)
            else:
                return Response(
                    result_order,
                    200,
                )
        else:
            page = self.paginate_queryset(queryset)
            if page is not None:
                # Fetch CMS data for paginated warranties
                warranties = list(page)
                cms_data_map = get_cms_data_for_warranties(
                    warranties, request.user.kanna_user
                )

                serialize = self.get_serializer(
                    page,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )
                return self.get_paginated_response(serialize.data)
            else:
                # Fetch CMS data for all warranties
                warranties = list(queryset)
                cms_data_map = get_cms_data_for_warranties(
                    warranties, request.user.kanna_user
                )

                serialize = self.get_serializer(
                    queryset,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )
                return Response(
                    serialize.data,
                    200,
                )

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/pdf-url",  # noqa
    )
    def get_pdf_url(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            document_info_instance = DocumentDownloadInformation.objects.get(
                warranty=instance
            )
            urls = document_info_instance.get_url_by_admin_account(
                request.user, document_type="warranty"
            )
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )
        except DocumentDownloadInformation.DoesNotExist:
            urls = {
                "url": "",
                "download_url": "",
            }
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/release",  # noqa
    )
    def release(self, request, *args, **kwargs):
        instance = self.get_object()

        # get cms_uuid
        cms_uuid = (
            ManagementSheet.objects.values_list("cms_uuid", flat=True)
            .filter(id=instance.management_sheet_id)
            .first()
        )

        try:
            # get owner by cms_uuid
            owner = UserConstruction.objects.values_list("owner", flat=True).get(
                construction_uuid=cms_uuid
            )
            # release warranty for owner from shop/admin
            if instance.status == WarrantyStatusChoices.OPEN:
                instance.owner_id = owner
                instance.status = WarrantyStatusChoices.RELEASED
                instance.save()

                # Send mail for owner
                kanna_user = UserKanna.objects.get(email=request.user.email)
                cms = get_cms_kanna(cms_uuid, kanna_user)
                cms_title = cms.get("title")
                user = User.objects.filter(id=owner).first()
                send_mail_release_warranty(user, cms_title)

        except UserConstruction.DoesNotExist:

            return Response(
                {"message": "この工事は施主に紐つけてください。"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            {"message": "Issuance of warranty to the owner is successful"},
            status=status.HTTP_200_OK,
        )

    @action(
        detail=False,
        methods=["get"],
        url_path="construction-list",  # noqa
    )
    def construction_list(self, request, *args, **kwargs):

        kanna_user = UserKanna.objects.get(email=request.user.email)
        company = kanna_user.company
        if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            management_sheet__id = Warranty.objects.values_list(
                "management_sheet__id", flat=True
            ).filter()

            management_sheet_cms_uuids = (
                ManagementSheet.objects.annotate(
                    str=Cast("cms_uuid", output_field=TextField())
                )
                .values_list("str", flat=True)
                .filter(id__in=management_sheet__id)
            )
        elif company:
            management_sheet_ids = (
                ApproveFlowSettingInstance.objects.filter(
                    Q(shop=company) | Q(dealer=company) | Q(act=company)
                )
                .values_list("management_sheet_id")
                .all()
            )

            management_sheet_ids_distinct = (
                ManagementSheet.objects.values_list("id", flat=True)
                .filter(
                    Q(id__in=(management_sheet_ids))
                    | Q(diagnostic_management_sheet__company=company)
                )
                .distinct()
                .all()
            )

            warranty_management_sheet = Warranty.objects.values_list(
                "management_sheet", flat=True
            ).filter(management_sheet_id__in=management_sheet_ids_distinct)

            management_sheet_cms_uuids = (
                ManagementSheet.objects.annotate(
                    str=Cast("cms_uuid", output_field=TextField())
                )
                .values_list("str", flat=True)
                .filter(id__in=warranty_management_sheet)
            )

        params = CmsParam(**request.query_params.dict())
        # cms取得

        cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)
        cms_list_data = camel_to_snake_json(cms_list)
        for cms in cms_list_data:
            get_cms_customize(cms)
        # クエリパラメータでフィルター
        cms_list_data = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=list(management_sheet_cms_uuids),
        )
        serializer = self.get_serializer(data=cms_list_data, many=True)
        serializer.is_valid(raise_exception=True)
        return Response(data={"data": serializer.data})

    def construction_method_export(self, sds_types, paint_part_names, paint_areas):
        category_choice = {
            "ROOF": "屋根",
            "OUTER_WALL": "外壁",
        }
        diagnostic_conditions = {
            "1": {"type": "", "method": "", "area": ""},
            "2": {"type": "", "method": "", "area": ""},
            "3": {"type": "", "method": "", "area": ""},
            "4": {"type": "", "method": "", "area": ""},
            "5": {"type": "", "method": "", "area": ""},
        }
        count = 0
        for type, part_name, area in zip(sds_types, paint_part_names, paint_areas):
            count += 1
            diagnostic_conditions[str(count)]["type"] = category_choice[type]
            diagnostic_conditions[str(count)]["method"] = part_name
            diagnostic_conditions[str(count)]["area"] = area

        return diagnostic_conditions

    @action(
        detail=False,
        methods=["get"],
        url_path="export-warranty-csv",  # noqa
    )
    def export_warranty_csv(self, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        headers = [
            "保証書Ｎｏ",
            "県",
            "ﾃﾞｨﾗｰ名",
            "MUS名",
            "MUS担当",
            "保証書発行先",
            "物件名",
            "〒",
            "物件所在地",
            "部位1",
            "工法",
            "㎡",
            "部位2",
            "工法",
            "㎡",
            "部位3",
            "工法",
            "㎡",
            "部位4",
            "工法",
            "㎡",
            "部位5",
            "工法",
            "㎡",
            "工事完了日",
            "工事番号",
            "保証書発行日",
            "ｺﾋﾟｰ返却",
            "管理ｼｰﾄ ",
            "保証書送付先",
            "保証年数",
            "備考",
            "重複ﾁｪｯｸ",
            "お礼状",
            "発送日",
        ]
        file_name = f"WarrantyList_{strftime('%Y/%m/%d-%H-%M')}"
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="{file_name}.csv"'
        response["File-Name"] = f"{file_name}.csv"

        writer = csv.writer(response)
        writer.writerow(headers)

        for warranty in queryset:
            tmp_data = []
            warranty_number = prefecture = dealer_name = shop_name = ""
            shop_pic = owner_name = address = postcode = building_name = ""
            completed_date = construction_number = warranty_issue_date = ""
            warranty_years = return_copy = origin_management_sheet = ""
            note = released = duplicate_check = released_day = ""

            if (
                warranty.management_sheet.warranty_registration_no
                and warranty.status == WarrantyStatusChoices.RELEASED
            ):
                warranty_number = warranty.management_sheet.warranty_registration_no
            if warranty.management_sheet.prefecture:
                prefecture = warranty.management_sheet.prefecture

            approve_follow_instance = ApproveFlowSettingInstance.objects.filter(
                management_sheet_id=warranty.management_sheet.id
            ).first()

            if approve_follow_instance.shop:
                shop_name = (
                    Company.objects.filter(id=approve_follow_instance.shop_id)
                    .first()
                    .name
                )
                shop_dealer = ShopDealer.objects.filter(
                    shop_id=approve_follow_instance.shop_id
                ).first()

                if shop_dealer:
                    dealer_name = (
                        Company.objects.filter(id=shop_dealer.dealer_id).first().name
                    )

            if warranty.management_sheet.pic:
                id_pic = warranty.management_sheet.pic.user.id
                user_pic = User.objects.filter(id=id_pic).first()
                shop_pic = f"{user_pic.last_name}{user_pic.first_name}"

            if warranty.owner:
                owner_name = f"{warranty.owner.last_name} {warranty.owner.first_name}"

            if warranty.management_sheet.building_name:
                building_name = warranty.management_sheet.building_name

            if warranty.management_sheet.construction_zipcode:
                postcode = warranty.management_sheet.construction_zipcode
                if POSTAL_CODE_SYMBOL in postcode:
                    postcode = postcode.replace(POSTAL_CODE_SYMBOL, "")
                if "-" not in postcode:
                    postcode = f"{postcode[:3]}-{postcode[3:]}"
            if (
                warranty.management_sheet.full_address
                or warranty.management_sheet.prefecture
                or warranty.management_sheet.city
            ):
                address = f"{warranty.management_sheet.prefecture}{warranty.management_sheet.city}{warranty.management_sheet.full_address}"

            if warranty.management_sheet.completed_date:
                completed_date = warranty.management_sheet.completed_date.strftime(
                    "%Y/%m/%d"
                )

            if warranty.management_sheet.construction_no:
                construction_number = warranty.management_sheet.construction_no

            if warranty.warranty_year_type == WarrantyYearChoices.TEN_YEARS:
                warranty_issue_date = (
                    warranty.management_sheet.ten_years_end_warranty_date.strftime(
                        "%Y/%m/%d"
                    )
                )
                actual_construction_list = ActualConstruction.objects.filter(
                    Q(management_sheet_id=warranty.management_sheet.id),
                    Q(
                        warranties__warranty_year_type=WarrantyYearChoices.TEN_YEARS
                    ),
                )
                sds_types = actual_construction_list.values_list(
                    "diagnostic_construction_method__diagnostic__type", flat=True
                )
                paint_part_names = actual_construction_list.values_list(
                    "paint_parts_name", flat=True
                )
                paint_areas = actual_construction_list.values_list(
                    "paint_area", flat=True
                )
                diagnostic_conditions = self.construction_method_export(
                    sds_types, paint_part_names, paint_areas
                )
            else:
                warranty_issue_date = (
                    warranty.management_sheet.fifteen_years_end_warranty_date.strftime(
                        "%Y/%m/%d"
                    )
                )
                actual_construction_list = ActualConstruction.objects.filter(
                    Q(management_sheet_id=warranty.management_sheet.id),
                    Q(
                        warranties__warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS
                    ),
                )
                sds_types = actual_construction_list.values_list(
                    "diagnostic_construction_method__diagnostic__type", flat=True
                )
                paint_part_names = actual_construction_list.values_list(
                    "paint_parts_name", flat=True
                )
                paint_areas = actual_construction_list.values_list(
                    "paint_area", flat=True
                )
                diagnostic_conditions = self.construction_method_export(
                    sds_types, paint_part_names, paint_areas
                )

            if warranty.warranty_year_type:
                warranty_years = warranty.warranty_year_type
            if warranty.status == WarrantyStatusChoices.RELEASED:
                released = "○"
                released_day = warranty.updated_at.strftime("%Y/%m/%d")

            tmp_data.append(warranty_number)
            tmp_data.append(prefecture)
            tmp_data.append(dealer_name)
            tmp_data.append(shop_name)
            tmp_data.append(shop_pic)
            tmp_data.append(owner_name)
            tmp_data.append(building_name)
            tmp_data.append(postcode)
            tmp_data.append(address)
            tmp_data.append(diagnostic_conditions["1"]["type"])
            tmp_data.append(diagnostic_conditions["1"]["method"])
            tmp_data.append(diagnostic_conditions["1"]["area"])
            tmp_data.append(diagnostic_conditions["2"]["type"])
            tmp_data.append(diagnostic_conditions["2"]["method"])
            tmp_data.append(diagnostic_conditions["2"]["area"])
            tmp_data.append(diagnostic_conditions["3"]["type"])
            tmp_data.append(diagnostic_conditions["3"]["method"])
            tmp_data.append(diagnostic_conditions["3"]["area"])
            tmp_data.append(diagnostic_conditions["4"]["type"])
            tmp_data.append(diagnostic_conditions["4"]["method"])
            tmp_data.append(diagnostic_conditions["4"]["area"])
            tmp_data.append(diagnostic_conditions["5"]["type"])
            tmp_data.append(diagnostic_conditions["5"]["method"])
            tmp_data.append(diagnostic_conditions["5"]["area"])
            tmp_data.append(completed_date)
            tmp_data.append(construction_number)
            tmp_data.append(warranty_issue_date)
            tmp_data.append(return_copy)
            tmp_data.append(origin_management_sheet)
            tmp_data.append(shop_name)
            tmp_data.append(warranty_years)
            tmp_data.append(note)
            tmp_data.append(duplicate_check)
            tmp_data.append(released)
            tmp_data.append(released_day)

            writer.writerow(tmp_data)

        return response
