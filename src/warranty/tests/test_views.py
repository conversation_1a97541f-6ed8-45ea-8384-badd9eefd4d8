from unittest.mock import patch, MagicMock
from faker import Faker
from django.test import TestCase
from rest_framework.test import APIRequestFactory, force_authenticate
from rest_framework import status

from warranty.models import Warranty, WarrantyYearChoices
from warranty.views import WarrantyViewSet
from warranty.serializers import get_cms_data_for_warranties
from management_sheets.models import ManagementSheet
from kanna_api.models.user_kanna import UserKanna
from authentication.models import User


class TestWarrantyViews(TestCase):
    def setUp(self):
        """Set up test data."""
        self.faker = Faker()
        self.factory = APIRequestFactory()
        
        # Create test user and kanna user
        self.user = User.objects.create(
            email=self.faker.email(),
            username=self.faker.user_name(),
        )
        self.kanna_user = UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=self.user.email,
            name=self.faker.name(),
        )
        self.user.kanna_user = self.kanna_user
        self.user.save()
        
        # Create test management sheets
        self.management_sheet1 = ManagementSheet.objects.create(
            cms_uuid="f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            building_name="Building 1",
            construction_no="CONST-001",
            start_warranty_date="2023-01-01",
            ten_years_end_warranty_date="2033-01-01",
            fifteen_years_end_warranty_date="2038-01-01",
        )
        self.management_sheet2 = ManagementSheet.objects.create(
            cms_uuid="d8734161-e718-40b5-b1a1-3e66aa9561de",
            building_name="Building 2",
            construction_no="CONST-002",
            start_warranty_date="2023-02-01",
            ten_years_end_warranty_date="2033-02-01",
            fifteen_years_end_warranty_date="2038-02-01",
        )
        
        # Create test warranties
        self.warranty1 = Warranty.objects.create(
            management_sheet=self.management_sheet1,
            owner="Owner 1",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        self.warranty2 = Warranty.objects.create(
            management_sheet=self.management_sheet2,
            owner="Owner 2",
            warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
        )

    @patch("warranty.views.get_cms_data_for_warranties")
    @patch("warranty.views.update_user_assign_cms")
    def test_warranty_list_with_cms_data(self, mock_update_cms, mock_get_cms_data):
        """Test warranty list endpoint with CMS data in context."""
        # Mock CMS data
        mock_cms_data = {
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": {"title": "Construction 1"},
            "d8734161-e718-40b5-b1a1-3e66aa9561de": {"title": "Construction 2"},
        }
        mock_get_cms_data.return_value = mock_cms_data
        mock_update_cms.return_value = []
        
        # Create request
        request = self.factory.get('/api/warranty/')
        force_authenticate(request, user=self.user)
        
        # Create viewset
        viewset = WarrantyViewSet()
        viewset.request = request
        
        # Mock queryset
        with patch.object(viewset, 'get_queryset') as mock_queryset:
            mock_queryset.return_value = Warranty.objects.all()
            
            # Mock pagination
            with patch.object(viewset, 'paginate_queryset') as mock_paginate:
                mock_paginate.return_value = None
                
                # Call list method
                response = viewset.list(request)
                
                # Verify response
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Verify get_cms_data_for_warranties was called
                mock_get_cms_data.assert_called_once()
                call_args = mock_get_cms_data.call_args
                self.assertEqual(len(call_args[0][0]), 2)  # 2 warranties
                self.assertEqual(call_args[0][1], self.kanna_user)

    @patch("warranty.views.get_cms_data_for_warranties")
    @patch("warranty.views.update_user_assign_cms")
    def test_warranty_list_with_pagination(self, mock_update_cms, mock_get_cms_data):
        """Test warranty list endpoint with pagination."""
        # Mock CMS data
        mock_cms_data = {
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": {"title": "Construction 1"},
        }
        mock_get_cms_data.return_value = mock_cms_data
        mock_update_cms.return_value = []
        
        # Create request
        request = self.factory.get('/api/warranty/')
        force_authenticate(request, user=self.user)
        
        # Create viewset
        viewset = WarrantyViewSet()
        viewset.request = request
        
        # Mock queryset and pagination
        with patch.object(viewset, 'get_queryset') as mock_queryset:
            mock_queryset.return_value = Warranty.objects.all()
            
            with patch.object(viewset, 'paginate_queryset') as mock_paginate:
                mock_paginate.return_value = [self.warranty1]  # Paginated result
                
                # Call list method
                response = viewset.list(request)
                
                # Verify response
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Verify get_cms_data_for_warranties was called with paginated data
                mock_get_cms_data.assert_called_once()
                call_args = mock_get_cms_data.call_args
                self.assertEqual(len(call_args[0][0]), 1)  # 1 warranty in pagination

    @patch("warranty.views.get_cms_data_for_warranties")
    @patch("warranty.views.update_user_assign_cms")
    def test_warranty_list_with_sorting(self, mock_update_cms, mock_get_cms_data):
        """Test warranty list endpoint with sorting."""
        # Mock CMS data
        mock_cms_data = {
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": {"title": "Construction 1"},
            "d8734161-e718-40b5-b1a1-3e66aa9561de": {"title": "Construction 2"},
        }
        mock_get_cms_data.return_value = mock_cms_data
        mock_update_cms.return_value = []
        
        # Create request with sorting
        request = self.factory.get('/api/warranty/?sort_by=id')
        force_authenticate(request, user=self.user)
        
        # Create viewset
        viewset = WarrantyViewSet()
        viewset.request = request
        
        # Mock queryset
        with patch.object(viewset, 'get_queryset') as mock_queryset:
            mock_queryset.return_value = Warranty.objects.all()
            
            # Mock pagination
            with patch.object(viewset, 'paginate_queryset') as mock_paginate:
                mock_paginate.return_value = None
                
                # Call list method
                response = viewset.list(request)
                
                # Verify response
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Verify get_cms_data_for_warranties was called
                mock_get_cms_data.assert_called_once()

    @patch("warranty.views.get_cms_data_for_warranties")
    @patch("warranty.views.update_user_assign_cms")
    def test_warranty_list_empty_queryset(self, mock_update_cms, mock_get_cms_data):
        """Test warranty list endpoint with empty queryset."""
        # Mock empty CMS data
        mock_get_cms_data.return_value = {}
        mock_update_cms.return_value = []
        
        # Create request
        request = self.factory.get('/api/warranty/')
        force_authenticate(request, user=self.user)
        
        # Create viewset
        viewset = WarrantyViewSet()
        viewset.request = request
        
        # Mock empty queryset
        with patch.object(viewset, 'get_queryset') as mock_queryset:
            mock_queryset.return_value = Warranty.objects.none()
            
            # Mock pagination
            with patch.object(viewset, 'paginate_queryset') as mock_paginate:
                mock_paginate.return_value = None
                
                # Call list method
                response = viewset.list(request)
                
                # Verify response
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Verify get_cms_data_for_warranties was called with empty list
                mock_get_cms_data.assert_called_once()
                call_args = mock_get_cms_data.call_args
                self.assertEqual(len(call_args[0][0]), 0)  # Empty warranties list

    @patch("warranty.views.get_cms_data_for_warranties")
    @patch("warranty.views.update_user_assign_cms")
    def test_warranty_list_exception_handling(self, mock_update_cms, mock_get_cms_data):
        """Test warranty list endpoint exception handling."""
        # Mock exception in get_cms_data_for_warranties
        mock_get_cms_data.side_effect = Exception("CMS data error")
        mock_update_cms.return_value = []
        
        # Create request
        request = self.factory.get('/api/warranty/')
        force_authenticate(request, user=self.user)
        
        # Create viewset
        viewset = WarrantyViewSet()
        viewset.request = request
        
        # Mock queryset
        with patch.object(viewset, 'get_queryset') as mock_queryset:
            mock_queryset.return_value = Warranty.objects.all()
            
            # Mock pagination
            with patch.object(viewset, 'paginate_queryset') as mock_paginate:
                mock_paginate.return_value = None
                
                # Call list method - should handle exception gracefully
                response = viewset.list(request)
                
                # Verify response still works
                self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_cms_data_for_warranties_integration(self):
        """Integration test for get_cms_data_for_warranties function."""
        # Create warranties list
        warranties = [self.warranty1, self.warranty2]
        
        # Mock get_cms_list_detail
        with patch("warranty.serializers.get_cms_list_detail") as mock_get_cms_list_detail:
            mock_cms_data = [
                {"title": "Construction 1", "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"},
                {"title": "Construction 2", "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de"},
            ]
            mock_get_cms_list_detail.return_value = mock_cms_data
            
            # Call the function
            result = get_cms_data_for_warranties(warranties, self.kanna_user)
            
            # Verify results
            self.assertEqual(len(result), 2)
            self.assertEqual(result["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]["title"], "Construction 1")
            self.assertEqual(result["d8734161-e718-40b5-b1a1-3e66aa9561de"]["title"], "Construction 2")
            
            # Verify get_cms_list_detail was called with correct parameters
            expected_cms_uuids = [
                "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "d8734161-e718-40b5-b1a1-3e66aa9561de",
            ]
            mock_get_cms_list_detail.assert_called_once_with(expected_cms_uuids, self.kanna_user)

    def test_get_cms_data_for_warranties_with_none_management_sheet(self):
        """Test get_cms_data_for_warranties with warranties having None management_sheet."""
        # Create warranty without management sheet
        warranty_no_sheet = Warranty.objects.create(
            management_sheet=None,
            owner="Owner No Sheet",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        
        # Create warranties list with mixed data
        warranties = [self.warranty1, warranty_no_sheet, self.warranty2]
        
        # Mock get_cms_list_detail
        with patch("warranty.serializers.get_cms_list_detail") as mock_get_cms_list_detail:
            mock_cms_data = [
                {"title": "Construction 1", "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"},
                {"title": "Construction 2", "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de"},
            ]
            mock_get_cms_list_detail.return_value = mock_cms_data
            
            # Call the function
            result = get_cms_data_for_warranties(warranties, self.kanna_user)
            
            # Verify results - should only process warranties with management_sheet
            self.assertEqual(len(result), 2)
            self.assertEqual(result["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]["title"], "Construction 1")
            self.assertEqual(result["d8734161-e718-40b5-b1a1-3e66aa9561de"]["title"], "Construction 2")
            
            # Verify get_cms_list_detail was called with only valid UUIDs
            expected_cms_uuids = [
                "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "d8734161-e718-40b5-b1a1-3e66aa9561de",
            ]
            mock_get_cms_list_detail.assert_called_once_with(expected_cms_uuids, self.kanna_user) 