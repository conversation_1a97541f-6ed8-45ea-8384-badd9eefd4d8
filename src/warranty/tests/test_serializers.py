from unittest.mock import patch, MagicMock
from faker import Faker
from django.test import TestCase
from rest_framework.test import APIRequestFactory

from warranty.models import Warranty, WarrantyYearChoices
from warranty.serializers import (
    get_cms_data_for_warranties,
    WarrantySerializer,
)
from management_sheets.models import ManagementSheet
from kanna_api.models.user_kanna import UserKanna
from authentication.models import User


class TestWarrantySerializers(TestCase):
    def setUp(self):
        """Set up test data."""
        self.faker = Faker()
        self.factory = APIRequestFactory()
        
        # Create test user and kanna user
        self.user = User.objects.create(
            email=self.faker.email(),
            username=self.faker.user_name(),
        )
        self.kanna_user = UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=self.user.email,
            name=self.faker.name(),
        )
        self.user.kanna_user = self.kanna_user
        self.user.save()
        
        # Create test management sheets
        self.management_sheet1 = ManagementSheet.objects.create(
            cms_uuid="f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            building_name="Building 1",
            construction_no="CONST-001",
            start_warranty_date="2023-01-01",
            ten_years_end_warranty_date="2033-01-01",
            fifteen_years_end_warranty_date="2038-01-01",
        )
        self.management_sheet2 = ManagementSheet.objects.create(
            cms_uuid="d8734161-e718-40b5-b1a1-3e66aa9561de",
            building_name="Building 2",
            construction_no="CONST-002",
            start_warranty_date="2023-02-01",
            ten_years_end_warranty_date="2033-02-01",
            fifteen_years_end_warranty_date="2038-02-01",
        )
        
        # Create test warranties
        self.warranty1 = Warranty.objects.create(
            management_sheet=self.management_sheet1,
            owner="Owner 1",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        self.warranty2 = Warranty.objects.create(
            management_sheet=self.management_sheet2,
            owner="Owner 2",
            warranty_year_type=WarrantyYearChoices.FIFTEEN_YEARS,
        )

    @patch("warranty.serializers.get_cms_list_detail")
    def test_get_cms_data_for_warranties_success(self, mock_get_cms_list_detail):
        """Test get_cms_data_for_warranties with successful API calls."""
        # Mock successful CMS data
        mock_cms_data = [
            {"title": "Construction 1", "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"},
            {"title": "Construction 2", "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data
        
        # Call the function
        warranties = [self.warranty1, self.warranty2]
        result = get_cms_data_for_warranties(warranties, self.kanna_user)
        
        # Verify results
        expected_cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
        ]
        self.assertEqual(len(result), 2)
        self.assertEqual(result["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]["title"], "Construction 1")
        self.assertEqual(result["d8734161-e718-40b5-b1a1-3e66aa9561de"]["title"], "Construction 2")
        
        # Verify get_cms_list_detail was called with correct parameters
        mock_get_cms_list_detail.assert_called_once_with(expected_cms_uuids, self.kanna_user)

    @patch("warranty.serializers.get_cms_list_detail")
    def test_get_cms_data_for_warranties_empty_input(self, mock_get_cms_list_detail):
        """Test get_cms_data_for_warranties with empty warranties list."""
        # Call the function with empty list
        result = get_cms_data_for_warranties([], self.kanna_user)
        
        # Verify results
        self.assertEqual(result, {})
        mock_get_cms_list_detail.assert_not_called()

    @patch("warranty.serializers.get_cms_list_detail")
    def test_get_cms_data_for_warranties_duplicate_cms_uuids(self, mock_get_cms_list_detail):
        """Test get_cms_data_for_warranties with duplicate CMS UUIDs."""
        # Create another warranty with same management sheet
        warranty3 = Warranty.objects.create(
            management_sheet=self.management_sheet1,  # Same as warranty1
            owner="Owner 3",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        
        # Mock successful CMS data
        mock_cms_data = [
            {"title": "Construction 1", "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data
        
        # Call the function
        warranties = [self.warranty1, self.warranty2, warranty3]
        result = get_cms_data_for_warranties(warranties, self.kanna_user)
        
        # Verify results - should deduplicate CMS UUIDs
        self.assertEqual(len(result), 1)
        self.assertEqual(result["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]["title"], "Construction 1")
        
        # Verify get_cms_list_detail was called with deduplicated UUIDs
        expected_cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
        ]
        mock_get_cms_list_detail.assert_called_once_with(expected_cms_uuids, self.kanna_user)

    @patch("warranty.serializers.get_cms_list_detail")
    def test_get_cms_data_for_warranties_with_none_management_sheet(self, mock_get_cms_list_detail):
        """Test get_cms_data_for_warranties with warranties having None management_sheet."""
        # Create warranty without management sheet
        warranty_no_sheet = Warranty.objects.create(
            management_sheet=None,
            owner="Owner No Sheet",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        
        # Mock successful CMS data
        mock_cms_data = [
            {"title": "Construction 1", "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data
        
        # Call the function
        warranties = [self.warranty1, warranty_no_sheet]
        result = get_cms_data_for_warranties(warranties, self.kanna_user)
        
        # Verify results - should only process warranties with management_sheet
        self.assertEqual(len(result), 1)
        self.assertEqual(result["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]["title"], "Construction 1")
        
        # Verify get_cms_list_detail was called with only valid UUIDs
        expected_cms_uuids = ["f0c3ca9b-06ff-479f-8337-cf2414fe18e5"]
        mock_get_cms_list_detail.assert_called_once_with(expected_cms_uuids, self.kanna_user)

    def test_warranty_serializer_get_cms_title_with_context(self):
        """Test WarrantySerializer.get_cms_title with cms_data_map in context."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context with cms_data_map
        context = {
            "request": request,
            "cms_data_map": {
                "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": {"title": "Construction 1"},
                "d8734161-e718-40b5-b1a1-3e66aa9561de": {"title": "Construction 2"},
            }
        }
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "Construction 1")

    @patch("warranty.serializers.get_cms_kanna")
    def test_warranty_serializer_get_cms_title_fallback(self, mock_get_cms_kanna):
        """Test WarrantySerializer.get_cms_title fallback to API call."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context without cms_data_map
        context = {"request": request}
        
        # Mock API response
        mock_get_cms_kanna.return_value = {"title": "Construction 1"}
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "Construction 1")
        mock_get_cms_kanna.assert_called_once_with(
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            self.kanna_user
        )

    @patch("warranty.serializers.get_cms_kanna")
    def test_warranty_serializer_get_cms_title_api_exception(self, mock_get_cms_kanna):
        """Test WarrantySerializer.get_cms_title when API call raises exception."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context without cms_data_map
        context = {"request": request}
        
        # Mock API exception
        mock_get_cms_kanna.side_effect = Exception("API Error")
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "")

    def test_warranty_serializer_get_cms_title_no_request(self):
        """Test WarrantySerializer.get_cms_title without request in context."""
        # Create context without request
        context = {}
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "")

    def test_warranty_serializer_get_cms_title_no_management_sheet(self):
        """Test WarrantySerializer.get_cms_title with warranty having no management_sheet."""
        # Create warranty without management sheet
        warranty_no_sheet = Warranty.objects.create(
            management_sheet=None,
            owner="Owner No Sheet",
            warranty_year_type=WarrantyYearChoices.TEN_YEARS,
        )
        
        # Create context with cms_data_map
        context = {
            "cms_data_map": {
                "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": {"title": "Construction 1"},
            }
        }
        
        # Create serializer
        serializer = WarrantySerializer(warranty_no_sheet, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(warranty_no_sheet)
        self.assertEqual(cms_title, "")

    def test_warranty_serializer_get_cms_title_empty_cms_data(self):
        """Test WarrantySerializer.get_cms_title with empty cms_data in context."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context with empty cms_data_map
        context = {
            "request": request,
            "cms_data_map": {}
        }
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "")

    def test_warranty_serializer_get_cms_title_invalid_cms_data(self):
        """Test WarrantySerializer.get_cms_title with invalid cms_data in context."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context with invalid cms_data_map
        context = {
            "request": request,
            "cms_data_map": {
                "f0c3ca9b-06ff-479f-8337-cf2414fe18e5": "not_a_dict",
            }
        }
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test get_cms_title
        cms_title = serializer.get_cms_title(self.warranty1)
        self.assertEqual(cms_title, "")

    def test_warranty_serializer_other_fields(self):
        """Test other fields in WarrantySerializer."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context
        context = {"request": request}
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty1, context=context)
        
        # Test other fields
        self.assertEqual(serializer.get_construction_no(self.warranty1), "CONST-001")
        self.assertEqual(serializer.get_building_name(self.warranty1), "Building 1")
        self.assertEqual(serializer.get_start_warranty_date(self.warranty1), "2023-01-01")
        self.assertEqual(serializer.get_end_warranty_date(self.warranty1), "2033-01-01")  # TEN_YEARS

    def test_warranty_serializer_fifteen_years_end_warranty_date(self):
        """Test WarrantySerializer.get_end_warranty_date for FIFTEEN_YEARS warranty."""
        # Create request
        request = self.factory.get('/')
        request.user = self.user
        
        # Create context
        context = {"request": request}
        
        # Create serializer
        serializer = WarrantySerializer(self.warranty2, context=context)
        
        # Test end_warranty_date for FIFTEEN_YEARS
        self.assertEqual(serializer.get_end_warranty_date(self.warranty2), "2038-02-01") 