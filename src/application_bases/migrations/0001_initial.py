# Generated by Django 4.1 on 2022-09-07 10:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ApplicationBase",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "application_base",
            },
        ),
    ]
