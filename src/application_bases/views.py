from rest_framework import viewsets
from rest_framework.exceptions import ValidationError

from django.db.models import Q

from application_bases.models import ApplicationBase
from application_bases.serializers import ApplicationBaseSerializer
from construction_methods.models import ConstructionMethod
from utilities.permissions.custom_permission import HasKannaPermission


class ApplicationBaseViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    serializer_class = ApplicationBaseSerializer
    http_method_names = ["get", "post", "put", "delete"]

    def get_queryset(self):
        queryset = ApplicationBase.objects.all()
        name = self.request.query_params.get("name", None)
        filter_list = []
        if name is not None:
            for instance in queryset:
                if instance.name and name in instance.name:
                    filter_list.append(instance.id)
            queryset = queryset.filter(Q(id__in=filter_list))
        queryset = queryset.order_by("-created_at")
        return queryset

    def perform_destroy(self, request, *args, **kwargs):

        instance = self.get_object()
        construction_method = ConstructionMethod.objects.filter(
            application_base=instance, is_deleted=False
        )

        if construction_method:
            raise ValidationError({"detail": "この適用下地は既に利用しています。削除することができません。"})

        return super().perform_destroy(instance)
