from mirage import fields

from django.db import models
from django.utils import timezone


class ApplicationBase(models.Model):
    id = models.BigAutoField(primary_key=True)
    # name = models.CharField(max_length=255)
    name = fields.EncryptedCharField(max_length=255)
    note = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "application_base"

    def __str__(self):
        return f"{self.name}"
