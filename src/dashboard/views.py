from datetime import datetime

from django.db.models import Q
from rest_framework import viewsets
from rest_framework.response import Response

from api.constants import KannaUserRoleEnum
from authentication.models import User, UserStatusType
from cms.cms import cms_filter
from cms.services import update_user_assign_cms
from companies.models import CompanyType
from dashboard.utils import (
    get_diagnostics_by_user,
    get_management_sheets_by_user, get_constructions_by_user,
)
from diagnostics.choices import DiagnosticStatus
from diagnostics.models import Diagnostic
from kanna_api.choices.project_status import ProjectStatus
from kanna_api.query_params.cms import CmsParam
from management_sheets.choices import ManagementSheetStatus
from management_sheets.models import ManagementSheet
from users.models import RequestOwner, RequestOwnerStatus
from utilities.permissions.custom_permission import HasKannaPermission
from warranty.models import Warranty, WarrantyStatusChoices


class DashboardViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    http_method_names = ["get"]

    def list(self, request, *args, **kwargs):
        user = self.request.user
        kanna_user = user.kanna_user
        company = kanna_user.company
        query_params = self.request.query_params.copy()
        list_management_ids = get_management_sheets_by_user(kanna_user)

        # Store cms_list from update_user_assign_cms to avoid duplicate API calls
        cms_list = None
        if kanna_user:
            cms_list = update_user_assign_cms(kanna_user)

        if "start_date" in query_params and query_params["start_date"]:
            if "end_date" not in query_params or not query_params["end_date"]:
                query_params["end_date"] = datetime.now().strftime("%Y-%m-%d")

        if "end_date" in query_params and query_params["end_date"]:
            if "start_date" not in query_params or not query_params["start_date"]:
                query_params["start_date"] = "2001-01-01"

        # Handle user_management
        if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            user_management = self._get_user_management(query_params, kanna_user, is_admin=True)
        elif company and company.type == CompanyType.SHOP:
            user_management = self._get_user_management(query_params, kanna_user, is_shop=True)

        # Handle diagnostic
        diagnostic = self._get_diagnostic(query_params, kanna_user)

        # Handle management_sheet
        management_sheet = self._get_management_sheet(query_params, list_management_ids)

        # Handle warranty
        warranty = self._get_warranty(query_params, list_management_ids)

        # Handle construction - pass cms_list to avoid duplicate API calls
        construction = self._get_construction(kanna_user, query_params, request, cms_list)

        if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            return Response(
                data={
                    "diagnostic": diagnostic,
                    "management_sheet": management_sheet,
                    "warranty": warranty,
                    "construction": construction,
                    "user_management": user_management,
                },
                status=200,
            )
        elif company:
            if company.type == CompanyType.SHOP:
                return Response(
                    data={
                        "user_management": user_management,
                        "diagnostic": diagnostic,
                        "management_sheet": management_sheet,
                        "warranty": warranty,
                        "construction": construction,
                    },
                    status=200,
                )

            else:
                return Response(
                    data={
                        "diagnostic": diagnostic,
                        "management_sheet": management_sheet,
                        "warranty": warranty,
                        "construction": construction,
                    },
                    status=200,
                )

    def _get_warranty(self, query_params, list_management_ids):
        warranty_queryset = Warranty.objects.filter(
            management_sheet_id__in=list_management_ids
        )
        notification = 0

        start_date = query_params.get("start_date")
        end_date = query_params.get("end_date")
        if start_date:
            warranty_queryset = warranty_queryset.filter(created_at__gt=start_date)
            notification += Warranty.objects.filter(
                management_sheet_id__in=list_management_ids,
                created_at__lt=start_date,
                status=WarrantyStatusChoices.OPEN,
            ).count()

        if end_date:
            warranty_queryset = warranty_queryset.filter(created_at__lt=end_date)
            notification += Warranty.objects.filter(
                management_sheet_id__in=list_management_ids,
                created_at__gt=end_date,
                status=WarrantyStatusChoices.OPEN,
            ).count()

        warranty = {
            "all": warranty_queryset.count(),
            "notification": notification or None,
        }
        return warranty

    def _get_management_sheet(self, query_params, list_management_ids):
        queryset_management_sheet = ManagementSheet.objects.filter(id__in=list_management_ids).all()
        notification = 0

        start_date = query_params.get("start_date")
        end_date = query_params.get("end_date")
        if start_date:
            queryset_management_sheet = queryset_management_sheet.filter(created_at__gt=start_date)
            notification += ManagementSheet.objects.filter(
                id__in=list_management_ids,
                created_at__lt=start_date,
                status=ManagementSheetStatus.WAITING_APPROVAL
            ).count()

        if end_date:
            queryset_management_sheet = queryset_management_sheet.filter(created_at__lt=query_params["end_date"])
            notification += ManagementSheet.objects.filter(
                id__in=list_management_ids,
                created_at__gt=end_date,
                status=ManagementSheetStatus.WAITING_APPROVAL
            ).count()

        management_sheet = {
            "all": queryset_management_sheet.count(),
            "status": [
                {
                    "type": ManagementSheetStatus.TEMP,
                    "count": queryset_management_sheet.filter(
                        status=ManagementSheetStatus.TEMP
                    ).count(),
                },
                {
                    "type": ManagementSheetStatus.WAITING_APPROVAL,
                    "count": queryset_management_sheet.filter(
                        status=DiagnosticStatus.WAITING_APPROVAL
                    ).count(),
                },
                {
                    "type": ManagementSheetStatus.APPROVED,
                    "count": queryset_management_sheet.filter(
                        status=ManagementSheetStatus.APPROVED
                    ).count(),
                },
                {
                    "type": ManagementSheetStatus.RETURN,
                    "count": queryset_management_sheet.filter(
                        status=ManagementSheetStatus.RETURN
                    ).count(),
                },
                {
                    "type": ManagementSheetStatus.REJECT,
                    "count": queryset_management_sheet.filter(
                        status=ManagementSheetStatus.REJECT
                    ).count(),
                },
            ],
            "notification": notification or None,
        }
        return management_sheet

    def _get_diagnostic(self, query_params, kanna_user):
        list_diagnostic_ids = get_diagnostics_by_user(kanna_user)
        diagnostic_queryset = Diagnostic.objects.filter(id__in=list_diagnostic_ids)
        notification_count = 0

        start_date = query_params.get("start_date")
        end_date = query_params.get("end_date")
        if start_date:
            diagnostic_queryset = diagnostic_queryset.filter(created_at__gt=start_date)
            notification_count += Diagnostic.objects.filter(
                id__in=list_diagnostic_ids,
                created_at__lt=start_date,
                status=DiagnosticStatus.WAITING_APPROVAL
            ).count()

        if end_date:
            diagnostic_queryset = diagnostic_queryset.filter(created_at__lt=end_date)
            notification_count += Diagnostic.objects.filter(
                id__in=list_diagnostic_ids,
                created_at__gt=end_date,
                status=DiagnosticStatus.WAITING_APPROVAL
            ).count()

        diagnostic = {
            "all": diagnostic_queryset.count(),
            "status": [
                {
                    "type": DiagnosticStatus.TEMP,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.TEMP
                    ).count(),
                },
                {
                    "type": DiagnosticStatus.WAITING_APPROVAL,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.WAITING_APPROVAL
                    ).count(),
                },
                {
                    "type": DiagnosticStatus.APPROVED,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.APPROVED
                    ).count(),
                },
                {
                    "type": DiagnosticStatus.RETURN,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.RETURN
                    ).count(),
                },
                {
                    "type": DiagnosticStatus.REJECT,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.REJECT
                    ).count(),
                },
                {
                    "type": DiagnosticStatus.UPDATE_AFTER_APPROVE,
                    "count": diagnostic_queryset.filter(
                        status=DiagnosticStatus.UPDATE_AFTER_APPROVE
                    ).count(),
                },
            ],
            "notification": notification_count or None,
        }
        return diagnostic

    def _get_user_management(self, query_params, kanna_user=None, is_shop=False, is_admin=False):
        owner_filter = {}
        request_owner_filter = {"status": RequestOwnerStatus.WAITING_FOR_APPROVE}

        if is_shop:
            owner_filter["company_id"] = kanna_user.company.id
            request_owner_filter["company_id"] = kanna_user.company.id
        elif is_admin:
            owner_filter["company_id__isnull"] = False
            request_owner_filter["company_id__isnull"] = False

        owner_queryset = User.objects.filter(**owner_filter)
        request_owner_queryset = RequestOwner.objects.filter(**request_owner_filter)
        request_owner_ids = request_owner_queryset.values_list("id", flat=True)
        notification_count = 0

        start_date = query_params.get("start_date")
        end_date = query_params.get("end_date")
        if start_date:
            owner_queryset = owner_queryset.filter(Q(created_at__gt=start_date))
            request_owner_queryset = request_owner_queryset.filter(Q(created__gt=start_date))
            notification_count += RequestOwner.objects.filter(
                id__in=request_owner_ids, created__lt=start_date
            ).count()

        if end_date:
            owner_queryset = owner_queryset.filter(Q(created_at__lt=end_date))
            request_owner_queryset = request_owner_queryset.filter(Q(created__lt=end_date))
            notification_count += RequestOwner.objects.filter(
                id__in=request_owner_ids, created__gt=end_date
            ).count()

        user_management = {
            "all": owner_queryset.count() + request_owner_queryset.count(),
            "status": [
                {
                    "type": RequestOwnerStatus.WAITING_FOR_APPROVE,
                    "count": request_owner_queryset.filter(
                        status=RequestOwnerStatus.WAITING_FOR_APPROVE
                    ).count()
                },
                {
                    "type": UserStatusType.APPROVED,
                    "count": owner_queryset.filter(
                        status=UserStatusType.APPROVED
                    ).count()
                },
                {
                    "type": UserStatusType.ACTIVE,
                    "count": owner_queryset.filter(
                        status=UserStatusType.ACTIVE
                    ).count()
                },
                {
                    "type": UserStatusType.INACTIVE,
                    "count": owner_queryset.filter(
                        status=UserStatusType.INACTIVE
                    ).count()
                },
            ],
            "notification": notification_count or None
        }
        return user_management

    def _get_construction(self, kanna_user, query_params, request, cms_list=None):
        """
        Get construction data for dashboard.

        Args:
            kanna_user: The Kanna user
            query_params: Query parameters for filtering
            request: HTTP request object
            cms_list: Optional pre-fetched CMS list to avoid duplicate API calls
        """
        (
            cms_list_data,
            cms_list_data_all,
            management_sheet_cms_uuids,
        ) = get_constructions_by_user(request, kanna_user, query_params, cms_list)
        params = CmsParam(**request.query_params.dict())
        params.project_status = ProjectStatus.IN_PROGRESS_CONSTRUCTION
        # クエリパラメータでフィルター
        cms_list_data_progress = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=list(management_sheet_cms_uuids),
        )
        params.project_status = ProjectStatus.COMPLETE_CONSTRUCTION
        # クエリパラメータでフィルター
        cms_list_data_complete = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=list(management_sheet_cms_uuids),
        )
        construction = {
            "all": len(cms_list_data_all),
            "status": [
                {
                    "type": ProjectStatus.IN_PROGRESS_CONSTRUCTION,
                    "count": len(cms_list_data_progress),
                },
                {
                    "type": ProjectStatus.COMPLETE_CONSTRUCTION,
                    "count": len(cms_list_data_complete),
                },
            ],
        }
        return construction
