from datetime import datetime

from django.db.models import Q, TextField
from django.db.models.functions import Cast

from api.constants import KannaUserRoleEnum
from cms.cms import cms_filter, get_cms_list
from cms.models import CmsUserKanna
from cms.services import get_cms_customize
from companies.models import CompanyType, ShopDealer
from diagnostics.models import Diagnostic
from kanna_api.query_params.cms import CmsParam
from management_sheets.models import ManagementSheet
from utilities.json_convert.convert import camel_to_snake_json


def get_management_sheets_by_user(kanna_user):
    company = kanna_user.company
    list_management_ids = []
    if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
        list_management_ids = Diagnostic.objects.values_list(
            "management_sheet__id", flat=True
        ).all()
    elif company:

        cms_user_current = list(
            CmsUserKanna.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(user_uuid=kanna_user.user_uuid)
        )
        if company.type == CompanyType.SHOP:
            # list id MS
            list_management_ids = Diagnostic.objects.values_list(
                "management_sheet__id", flat=True
            ).filter(Q(company_id=company.id), Q(cms_uuid__in=cms_user_current))
        elif company.type == CompanyType.DEALER:
            shop_ids = ShopDealer.objects.values_list("shop_id", flat=True).filter(
                dealer_id=company.id
            )
            # list ids MS
            list_management_ids = Diagnostic.objects.values_list(
                "management_sheet__id", flat=True
            ).filter(Q(company_id__in=shop_ids), Q(cms_uuid__in=cms_user_current))

        elif company.type == CompanyType.ACT:
            # list ids MS
            list_management_ids = Diagnostic.objects.values_list(
                "management_sheet__id", flat=True
            ).filter(cms_uuid__in=cms_user_current)
    return list_management_ids


def get_diagnostics_by_user(kanna_user):
    company = kanna_user.company
    list_diagnostic_ids = []
    if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
        list_diagnostic_ids = Diagnostic.objects.values_list("id", flat=True).all()
    elif company:
        cms_user_current = list(
            CmsUserKanna.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(user_uuid=kanna_user.user_uuid)
        )
        if company.type == CompanyType.SHOP:
            list_diagnostic_ids = Diagnostic.objects.values_list(
                "id", flat=True
            ).filter(Q(company_id=company.id), Q(cms_uuid__in=cms_user_current))
        elif company.type == CompanyType.DEALER:
            shop_ids = ShopDealer.objects.values_list("shop_id", flat=True).filter(
                dealer_id=company.id
            )
            list_diagnostic_ids = Diagnostic.objects.filter(
                Q(company_id__in=shop_ids), Q(cms_uuid__in=cms_user_current)
            )
        elif company.type == CompanyType.ACT:
            list_diagnostic_ids = Diagnostic.objects.values_list(
                "id", flat=True
            ).filter(cms_uuid__in=cms_user_current)

    return list_diagnostic_ids


def get_constructions_by_user(request, kanna_user, query_param, cms_list=None):
    """
    Get construction data for dashboard with optimized CMS list retrieval.
    
    Args:
        request: HTTP request object
        kanna_user: The Kanna user
        query_param: Query parameters for filtering
        cms_list: Optional pre-fetched CMS list to avoid duplicate API calls
        
    Returns:
        Tuple of (cms_list_data, cms_list_data_all, management_sheet_cms_uuids)
    """
    list_management_ids = get_management_sheets_by_user(kanna_user)
    params = CmsParam(**request.query_params.dict())

    if "start_date" in query_param and query_param["start_date"]:
        params.created_at_from = datetime.strptime(
            query_param["start_date"], "%Y-%m-%d"
        ).date()
    if "end_date" in query_param and query_param["end_date"]:
        params.created_at_to = datetime.strptime(
            query_param["end_date"], "%Y-%m-%d"
        ).date()

    # Use provided cms_list or fetch from API if not provided
    if cms_list is None:
        cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)
    
    cms_list_data = camel_to_snake_json(cms_list)
    for cms in cms_list_data:
        get_cms_customize(cms)
    # クエリパラメータでフィルター
    cms_list_data = cms_filter(
        request.user,
        params=params,
        cms_list_data=cms_list_data,
        on_user_cms_uuid_list=[],
        not_on_user_cms_uuid_list=[],
        not_on_group_cms_uuid_list=[],
        cms_uuid_list=[],
    )
    management_sheet_cms_uuids = (
        ManagementSheet.objects.annotate(str=Cast("cms_uuid", output_field=TextField()))
        .values_list("str", flat=True)
        .filter(id__in=list_management_ids)
    )
    # クエリパラメータでフィルター
    cms_list_data_all = cms_filter(
        request.user,
        params=params,
        cms_list_data=cms_list_data,
        on_user_cms_uuid_list=[],
        not_on_user_cms_uuid_list=[],
        not_on_group_cms_uuid_list=[],
        cms_uuid_list=list(management_sheet_cms_uuids),
    )
    return cms_list_data, cms_list_data_all, management_sheet_cms_uuids
