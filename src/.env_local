APP_ENV=local

# Django config
DEBUG=True
ALLOWED_HOSTS=*
ACCESS_TOKEN_LIFETIME=1400000
REFRESH_TOKEN_LIFETIME=140000

# Language and region
LANGUAGE_CODE=en-us
TIME_ZONE=Asia/Tokyo

# Postgres Database
DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
DATABASE_HOST=localhost
DATABASE_PORT=5491
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=agc_data

# Log Config
DJANGO_LOG_LEVEL=ERROR
# DJANGO_LOG_LEVEL=INFO

# Email server
EMAIL_SERVER_HOST=localhost
EMAIL_SERVER_USERNAME=
EMAIL_SERVER_PASSWORD=
EMAIL_SERVER_PORT=1025
EMAIL_USE_TLS=False
EMAIL_USE_SSL=False
DEFAULT_FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=

# Sentry
SENTRY_DSN=

# Notify eror slack url
WEBSITE_HOOK_ERROR_URL=''
PROJECT_NAME='AOC'
KANNA_URL=https://api.kanna4u.com
KANNA_CLIENT_ID=hybrid_technologies_dev_client_90c31daa
KANNA_CLIENT_SECRET=XOKnIkUnon6DaAWH
WEBSITE_URL=http://localhost:3000/
DAYS_TO_REMIND_CHANGE_PASSWORD=0
TIME_RUN_CRONJOB=8
TIME_TO_DELAY_SENT_EMAIL=3
API_URL=http://localhost:80
CORS_ORIGIN_WHITELIST=http://localhost:3000
INTERNAL_COMPANY_UUID=423608c1-ca89-4686-9e58-e93d0726f4b6
USER_KANNA_EMAIL_TO_CREATE_CMS=<EMAIL>
TIME_AUTO_GET_NEW_REFRESH_TOKEN="0 0 1 * *"
#Every 1 minutues (To test in QA))
TIME_AUTO_GET_NEW_REFRESH_TOKEN="* * * * *"
#day 1 of month
TIME_AUTO_GET_NEW_REFRESH_TOKEN="0 0 1 * *"
TIME_RUN_CRONJOB_UPLOAD_LOGS_TO_S3="0 * * * *"

# aws s3
FILE_UPLOAD_STORAGE=s3
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_LOGS_BUCKET_NAME=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_ENDPOINT_URL=

# aws sns
AWS_SNS_REGION_NAME=
AWS_SNS_ACCESS_KEY_ID=
AWS_SNS_SECRET_ACCESS_KEY=

MIRAGE_SECRET_KEY=VNIQ3I1R4C6G9M4B4B2J5E4R3N1V1J2L1
