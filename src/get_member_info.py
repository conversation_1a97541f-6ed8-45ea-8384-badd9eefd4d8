from kanna_api.models.user_kanna import UserKanna

user_kanna = UserKanna.objects.get(email="<EMAIL>")
print(f"User: {user_kanna} is active: {user_kanna.is_active}, role: {user_kanna.role}, two auth: {user_kanna.two_auth}")
print(user_kanna.last_login_at)
print(user_kanna.tel)

from utilities.sms_helper.sms_thread import SNS

import re

class NewSNS(SNS):
    def phone_parser(self, phone_number):
        if not phone_number:
            return None

        def format_e164(number, country_code):
            return f"+{country_code}{number}"

        def is_valid_jp_phone_number(number):
            return bool(re.match(r'^0\d{9,10}$', number))

        country_code = "81"  # country_code JP
        parsed_number = re.sub(r'\D', '', phone_number)

        if not is_valid_jp_phone_number(parsed_number):
            raise ValueError("Phone number is invalid")

        parsed_number = re.sub(r'^0', '', parsed_number)

        return format_e164(parsed_number, country_code)

sns = NewSNS()
print(sns.phone_parser(user_kanna.tel))