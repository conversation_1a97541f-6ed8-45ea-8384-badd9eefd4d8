from rest_framework import serializers
from rest_framework.serializers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    DateTimeField,
    UUIDField,
)

from properties.serializers import ImageSerializer


class CmsListBuildingSerializer(serializers.Serializer):
    uuid = UUIDField()
    title = CharField(allow_blank=True, default="")
    construction_no = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    is_in_house = BooleanField()
    started_on = DateField(allow_null=True, default=None)
    finished_on = DateField(allow_null=True, default=None)
    created_at = DateTimeField()
    updated_at = DateTimeField()
    note = CharField(allow_blank=True, default="")


class BuildingDetailSerializer(serializers.Serializer):
    uuid = UUIDField()
    management_id = CharField(allow_blank=True, default="")
    name = CharField(allow_blank=True, default="")
    code = CharField(allow_blank=True, default="")
    kind = Char<PERSON><PERSON>(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    postcode = CharField(allow_blank=True, default="")
    manager_full_name = CharField(allow_blank=True, default="")
    manager_last_name = CharField(allow_blank=True, default="")
    manager_first_name = CharField(allow_blank=True, default="")
    manager_full_name_kana = CharField(allow_blank=True, default="")
    manager_last_name_kana = CharField(allow_blank=True, default="")
    manager_first_name_kana = CharField(allow_blank=True, default="")
    manager_phone_number = CharField(allow_blank=True, default="")
    manager_email = CharField(allow_blank=True, default="")
    chairman_full_name = CharField(allow_blank=True, default="")
    chairman_last_name = CharField(allow_blank=True, default="")
    chairman_first_name = CharField(allow_blank=True, default="")
    chairman_full_name_kana = CharField(allow_blank=True, default="")
    chairman_last_name_kana = CharField(allow_blank=True, default="")
    chairman_first_name_kana = CharField(allow_blank=True, default="")
    chairman_phone_number = CharField(allow_blank=True, default="")
    chairman_email = CharField(allow_blank=True, default="")
    management_company_name = CharField(allow_blank=True, default="")
    staff_full_name = CharField(allow_blank=True, default="")
    staff_last_name = CharField(allow_blank=True, default="")
    staff_first_name = CharField(allow_blank=True, default="")
    staff_full_name_kana = CharField(allow_blank=True, default="")
    staff_last_name_kana = CharField(allow_blank=True, default="")
    staff_first_name_kana = CharField(allow_blank=True, default="")
    management_company_phone_number = CharField(allow_blank=True, default="")
    management_company_email = CharField(allow_blank=True, default="")
    management_company_fax_number = CharField(allow_blank=True, default="")
    management_system_ja = CharField(allow_blank=True, default="")
    working_style_ja = CharField(allow_blank=True, default="")
    management_company_note = CharField(allow_blank=True, default="")
    images = ImageSerializer(many=True)
    cms_list = CmsListBuildingSerializer(many=True, required=False)


class BuildingListSerializer(serializers.Serializer):
    uuid = UUIDField()
    postcode = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    name = CharField(allow_blank=True, default="")
    kind_ja = CharField(allow_blank=True, default="")
    kind = CharField(allow_blank=True, default="")
    kind_name = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    created_at = CharField()
