from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response

from django.core.exceptions import ObjectDoesNotExist

from cms.cms import cms_filter, get_cms_list
from cms.services import get_cms_customize, get_property_customize
from kanna_api.models.user_kanna import UserKanna
from kanna_api.property import get_properties, get_property, property_filter
from kanna_api.query_params.cms import CmsParam
from kanna_api.query_params.property import PropertyParam
from utilities.exceptions.api_exception import UserWhitelistEmailError
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import HasKannaPermission

from .serializers import BuildingDetailSerializer, BuildingListSerializer


class BuildingViewSet(viewsets.GenericViewSet):
    permission_classes = (HasKannaPermission,)
    queryset = None
    pagination_class = LimitOffsetPagination

    def get_serializer_class(self):
        if self.action == "list":
            return BuildingListSerializer

        return BuildingDetailSerializer

    def get_queryset(self):
        kanna_user = UserKanna.objects.get(email=self.request.user.email)
        response = get_properties(kanna_user)
        response_data = camel_to_snake_json(response)
        param = self.request.query_params.dict()
        response_data = property_filter(PropertyParam(**param), response_data)
        # @TODO remove this when filter is ready
        response_data = sorted(
            response_data, key=lambda k: k["created_at"], reverse=True
        )
        # @TODO uncomment this when filter is ready
        # sort_by_list = {
        #     "name",
        #     "kind",
        #     "prefecture",
        #     "created_at",
        # }
        # params = self.request.query_params.get("sort_by", None)
        # list_combine = []
        # params_direction = self.request.query_params.get("order_by_direction", "")
        # response_data = Ordering.order_filtering(
        #     response_data,
        #     params,
        #     params_direction,
        #     sort_by_list,
        #     list_combine,
        #     False,
        # )
        return response_data

    @swagger_auto_schema(manual_parameters=PropertyParam.yasg_params())
    def list(self, request, *args, **kwargs):
        try:
            response_data = self.get_queryset()
            page = self.paginate_queryset(response_data)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            serializer = self.get_serializer(response_data, many=True)

            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    def retrieve(self, request, *args, **kwargs):
        try:
            kanna_user = UserKanna.objects.get(email=request.user.email)
            response = get_property(kanna_user, kwargs["pk"])
            response_data = camel_to_snake_json(response)
            response_data
            params = CmsParam(**request.query_params.dict())
            params.property_uuid = kwargs["pk"]

            customer = response_data.get("property", {})
            get_property_customize(customer)

            cms_list = get_cms_list(kanna_user, cms_user_uuid=kanna_user.user_uuid)
            cms_list = camel_to_snake_json(cms_list)
            cms_list = cms_filter(
                kanna_user,
                params=params,
                cms_list_data=cms_list,
                on_user_cms_uuid_list=[],
                not_on_user_cms_uuid_list=[],
                not_on_group_cms_uuid_list=[],
                cms_uuid_list=[],
            )
            for cms in cms_list:
                get_cms_customize(cms)
                cms["address"] = customer.get("address", "")

            customer["cms_list"] = cms_list
            serializer = self.get_serializer(data=customer)
            serializer.is_valid(raise_exception=True)

            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError
