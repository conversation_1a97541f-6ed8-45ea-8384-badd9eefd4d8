from utilities.sms_helper.sms_thread import SNS

import re

class NewSNS:
    def phone_parser(self, phone_number):
        if not phone_number:
            return None

        def format_e164(number, country_code):
            return f"+{country_code}{number}"

        def is_valid_jp_phone_number(number):
            return bool(re.match(r'^0\d{9,10}$', number))

        country_code = "81"  # country_code JP
        parsed_number = re.sub(r'\D', '', phone_number)

        if not is_valid_jp_phone_number(parsed_number):
            raise ValueError("Phone number is invalid")

        parsed_number = re.sub(r'^0', '', parsed_number)

        return format_e164(parsed_number, country_code)

tel = "080-9367-1860"
sns = NewSNS()
print(sns.phone_parser(tel))