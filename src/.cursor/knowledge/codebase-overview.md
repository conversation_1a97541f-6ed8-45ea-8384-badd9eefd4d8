# Codebase Overview – Flight Booking System

This document provides a high-level overview of the codebase for the Flight Booking System, following Django Rest Framework (DRF) best practices.

---

## 1. API Endpoints (RESTful, DRF)

- **Flights** (`/api/v1/flights/`)
  - `GET`   – Search flights
  - `GET /{id}/` – Get flight details
  - `GET /{id}/availability/` – Check seat availability
  - `GET /{id}/pricing/` – Get pricing details

- **Bookings** (`/api/v1/bookings/`)
  - `GET`   – List user bookings
  - `POST`  – Create new booking
  - `GET /{id}/` – Get booking details
  - `PATCH /{id}/` – Update booking
  - `DELETE /{id}/` – Cancel booking
  - `POST /{id}/check-in/` – Online check-in

- **Passengers** (`/api/v1/passengers/`)
  - `GET`   – List passengers
  - `POST`  – Add passenger
  - `GET /{id}/` – Get passenger details
  - `PATCH /{id}/` – Update passenger
  - `DELETE /{id}/` – Remove passenger

- **Payments** (`/api/v1/payments/`)
  - `POST`  – Process payment
  - `GET /{id}/` – Get payment status
  - `POST /{id}/refund/` – Process refund

---

## 2. Management Commands

### 2.1 Data Maintenance Commands
- **find_missing_kaigai**: Identifies and creates missing PointsEvent records for international flight applications
  - Usage: `python manage.py find_missing_kaigai --mem_no "123,456,789"`
  - Purpose: Ensures data consistency between WorldApplications and PointsEvent systems
  - Features: Bulk processing, transaction safety, member filtering

### 2.2 Points Management Commands
- **update_points_summary_member_by_member_no**: Updates points summary for specific members
  - Usage: `python manage.py update_points_summary_member_by_member_no --date YYYY-MM-DD --mem_no "123,456,789"`
  - Purpose: Recalculates point summaries for specified members and dates

---

## 3. Main Modules

### 3.1 Models (`src/domestic_flight/models/`, `src/international_flight/models/`)
- **Flight**: Core model for flights (route, schedule, pricing)
- **Booking**: Booking management (status, passengers, payment)
- **Passenger**: Passenger information
- **Payment**: Payment processing and tracking
- **Airline**: Airline information and settings
- **Airport**: Airport information
- **Base**: `TimeStampedModel` in `src/common/models/base.py`

### 3.2 Points System Models (`application_v2/models/`)
- **PointsEvent**: Records of point transactions (purchases, promotions, usage)
- **PointsSummaryMember**: Monthly point summaries for members
- **WorldApplicationsPointEvent**: Junction table linking international applications to point events

### 3.3 International Flight Models (`kaigai/models/`)
- **WorldApplications**: International flight applications and bookings
- **Tickets**: Individual tickets within applications

### 3.4 Member Models (`member/models/`)
- **Member**: User member information and profiles

### 3.5 Serializers (`src/domestic_flight/serializers/`, `src/international_flight/serializers/`)
- **FlightSerializer**: Flight search and details
- **BookingSerializer**: Booking creation and management
- **PassengerSerializer**: Passenger information handling
- **PaymentSerializer**: Payment processing
- **AirlineSerializer**: Airline information

### 3.6 Views (`src/domestic_flight/views/`, `src/international_flight/views/`)
- **FlightViewSet**: Flight search and details
- **BookingViewSet**: Booking management
- **PassengerViewSet**: Passenger management
- **PaymentViewSet**: Payment processing
- **AirlineViewSet**: Airline information

### 3.7 Services (`src/domestic_flight/services/`, `src/international_flight/services/`)
- **FlightService**: Flight search and availability
- **BookingService**: Booking management
- **PaymentService**: Payment processing
- **AirlineService**: Airline integration
- **NotificationService**: Status updates

### 3.8 Config & Settings (`src/config/settings/`)
- **base.py**: Core settings
- **dev.py/prod.py/test.py**: Environment-specific settings
- **urls.py**: API routing
- **celery.py**: Async task configuration

---

## 4. Testing (`src/domestic_flight/tests/`, `src/international_flight/tests/`)
- **Unit tests** for models, serializers, services
- **Integration tests** for API endpoints
- **End-to-end tests** for booking flows
- **Performance tests** for critical paths
- **Factory Boy** for test data
- **Coverage**: Run with `python manage.py test` or `coverage run manage.py test`

---

## 5. Infrastructure & Tooling
- **Docker**: Multi-stage build
- **Docker Compose**: Local development environment
- **Makefile**: Development commands
- **Redis**: Caching and session management
- **Celery**: Async task processing
- **PostgreSQL**: Data storage
- **CDN**: Static file delivery
- **Monitoring**: APM and logging

---

## 6. Notable Design Choices
- **Service-oriented architecture**: Clear separation of concerns
- **Caching strategy**: Redis for performance
- **Async processing**: Celery for background tasks
- **API versioning**: All endpoints under `/api/v1/`
- **JWT authentication**: Secure API access
- **Multi-airline support**: Flexible airline integration
- **Payment processing**: Multiple gateway support
- **Points system**: Comprehensive point tracking and management
- **Data consistency**: Management commands for data maintenance

---

## 7. How to Extend
- Add new airlines by implementing airline adapter
- Add payment gateways by extending payment service
- Add new features by creating new apps
- Add background jobs using Celery
- Add monitoring and logging
- Add new API endpoints by creating new ViewSets
- Add data maintenance commands for new data consistency needs

---

For more details, see:
- `README.md` (setup, Makefile commands)
- `knowledge/structure/feature-summary.md` (feature list)
- `knowledge/structure/active-contexts/` (detailed context per feature)
