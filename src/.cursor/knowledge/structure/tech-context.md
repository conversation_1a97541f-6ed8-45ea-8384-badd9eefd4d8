# Tech Context – Flight Booking System

## 🧰 Core Technologies

| Layer       | Technology            | Description                                      |
|-------------|------------------------|--------------------------------------------------|
| Backend     | Django 2.0.4          | Core web framework for routing, ORM, and logic   |
| API Layer   | Django REST Framework 3.8.1 | Provides RESTful API endpoints                   |
| Frontend    | Django templates + Bootstrap 4 | Modern UI with Bootstrap framework |
| Database    | PostgreSQL            | Relational data storage with indexing support    |
| Cache       | Redis 2.10.6          | Caching layer for flight searches and status     |
| ORM         | Django ORM             | Handles model-level data access                  |
| Styling     | Bootstrap 4 1.1.1     | Modern CSS framework                             |
| Auth        | JWT 1.6.1             | JSON Web Token authentication                    |

## ⚙️ Development Environment

- **Python version**: 3.7.2
- **Package manager**: `pipenv`
- **Local server**: Django built-in dev server (`manage.py runserver`)
- **Environment variables**: Managed via `.env` file
- **Redis**: Required for caching and session management
- **PostgreSQL**: Required for data storage

## 🧪 Testing Stack

- `Django Test Framework` for test runner
- `FactoryBoy` + `Faker` for test data
- `httpretty 0.9.6` for API mocking
- `freezegun` for time-based testing
- `coverage` for code coverage reporting

## 🚀 Deployment Target

- **Platform**: AWS/GCP/Azure
- **Configuration**:
  - `DEBUG=False`
  - `ALLOWED_HOSTS` configured
  - Static files via CDN
  - Redis cluster for caching
  - PostgreSQL with replication
  - Load balancer configuration

## 🗂 Dependencies & Tools

| Tool/Lib       | Version    | Purpose                                  |
|----------------|------------|------------------------------------------|
| `djangorestframework` | 3.8.1 | API serialization & viewsets      |
| `psycopg2`     | *******    | PostgreSQL database adapter        |
| `redis`        | 2.10.6     | Redis client for caching          |
| `django-cors-headers` | 2.2.0 | CORS support                      |
| `django-redis` | 4.9.0      | Redis cache backend               |
| `django-filter` | 2.2.0     | Advanced filtering support        |
| `django-bootstrap4` | 1.1.1 | Bootstrap integration            |
| `gunicorn`     | 20.0.4     | Production WSGI server            |
| `requests`     | 2.31.0     | HTTP client for API calls         |
| `pyjwt`        | 1.6.1      | JWT authentication                |
| `cryptography` | 2.2.2      | Cryptographic operations          |
| `raven`        | 6.7.0      | Error tracking                    |
| `sendgrid`     | 5.3.0      | Email delivery                    |
| `boto3`        | *          | AWS services integration          |
| `selenium`     | 3.141.0    | Browser automation                |
| `beautifulsoup4` | 4.9.1    | HTML parsing                      |

## 🧩 Integrations

- **Airline APIs**: Integration with multiple airline systems
- **Payment Gateways**: Multiple payment provider integrations
- **Email Service**: SendGrid for transactional emails
- **SMS Gateway**: Flight status notifications
- **Monitoring**: Sentry (Raven) for error tracking
- **Logging**: Centralized log management
- **AWS Services**: Boto3 for AWS integration

## 📌 Configuration Notes

- `settings.py` is split into `base.py`, `dev.py`, and `prod.py`
- Use `.env` file for secrets and environment variables
- Static files served via CDN in production
- Redis used for:
  - Flight search caching
  - Session storage
  - Rate limiting
  - Task queue
- Database configuration includes:
  - Connection pooling
  - Read replicas
  - Backup strategy

## DRF Developer Guidelines

### Test Structure and Organization

1. Test Directory Structure:
   ```
   src/app_name/tests/
   ├── models/                    # Model tests
   │   ├── __init__.py
   │   └── test_model_name.py
   ├── serializers/              # Serializer tests
   │   ├── __init__.py
   │   └── test_serializer.py
   ├── services/                 # Service layer tests
   │   ├── __init__.py
   │   └── test_service.py
   └── views/                    # API endpoint tests
       ├── __init__.py
       └── feature_name/
           ├── __init__.py
           ├── test_feature.py
           └── test_feature_validation.py
   ```

2. Test File Separation:
   - Split by component type (models, serializers, services, views)
   - Group by feature and test type
   - Include integration tests for external services
   - Follow AAA pattern (Arrange-Act-Assert)

3. Test Coverage Requirements:
   - All API endpoints
   - External service integrations
   - Error handling
   - Edge cases
   - Performance critical paths

4. Test Data Management:
   - Use factories for test data
   - Mock external API calls
   - Clean up test data
   - Use realistic test scenarios

### Common Package Usage

1. Validators:
   - Field validation
   - Business rule validation
   - External service validation
   - Error message handling

2. Models:
   - Base models with audit fields
   - Domain-specific models
   - Data integrity constraints
   - Indexing strategy

3. Utils:
   - Shared utility functions
   - External service clients
   - Error handling helpers
   - Performance monitoring

### Code Organization

1. Package Structure:
   ```
   src/
   ├── common/                 # Shared functionality
   │   ├── models/            # Base models
   │   ├── validators/        # Validators
   │   ├── services/          # Shared services
   │   └── utils/            # Helper functions
   ├── domestic_flight/       # Domestic booking
   ├── international_flight/  # International booking
   ├── payment/              # Payment processing
   └── reservation/          # Reservation management
   ```

2. Import Guidelines:
   - Common modules first
   - Third-party imports
   - Local application imports
   - Absolute imports preferred

## Testing Tools & Practices

### Core Testing Stack
- **Django Test Framework**: Main testing framework
- **factory_boy**: Test data generation
- **faker**: Realistic fake data
- **coverage**: Coverage reporting
- **requests-mock**: API mocking
- **celery**: Async task testing

### Key Testing Principles
1. **Test Data Generation**
   - Use factories for test data
   - Mock external services
   - Realistic test scenarios

2. **Test Structure**
   - Use Django TestCase setUp/tearDown
   - Follow AAA pattern
   - Isolate tests

3. **API Testing**
   - Use DRF's APIClient
   - Test success/error cases
   - Verify response structure
   - Test rate limiting
   - Test caching

### Example Test Setup
```python
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

class TestFlightBooking(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.flight = FlightFactory()
        self.url = reverse("flight-booking", args=[self.flight.id])

    def test_create_booking(self):
        data = {
            "passengers": [PassengerFactory.build().dict()],
            "payment_method": "credit_card"
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, 201)
```

## Development Tools

- **Docker**: Containerization
- **Docker Compose**: Local development
- **Makefile**: Build automation
- **Git**: Version control
- **CI/CD**: Automated testing and deployment
- **Monitoring**: APM and logging
- **Documentation**: API docs and guides
