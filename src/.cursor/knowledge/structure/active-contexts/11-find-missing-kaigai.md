# Active Context: Find Missing Kaigai

## Feature Overview
**Feature ID**: 11  
**Feature Name**: Find Missing Kaigai  
**Status**: In Progress  
**Last Updated**: 2024-12-19  
**Priority**: High  

## What Is This Feature?
A Django management command that identifies and creates missing PointsEvent records for international flight applications (kaigai) that have arrived but don't have corresponding point events. This ensures data consistency between the WorldApplications and PointsEvent systems.

## Current Implementation Status

### ✅ Completed
- [x] Basic command structure with `--mem_no` option
- [x] SQL query to find missing point event applications
- [x] Bulk creation of missing PointsEvent records
- [x] Integration with WorldApplicationsPointEvent model
- [x] Proper handling of `event_date_time` with `auto_now_add=True`
- [x] Transaction safety with `@transaction.atomic`

### 🔄 In Progress
- [ ] Testing and validation of created records
- [ ] Performance optimization for large datasets
- [ ] Error handling improvements
- [ ] Logging enhancements

### 🔲 Not Started
- [ ] Integration with points summary update system
- [ ] Batch processing for large datasets
- [ ] Monitoring and alerting
- [ ] Documentation updates

## Technical Details

### Command Usage
```bash
python manage.py find_missing_kaigai --mem_no "123,456,789"
```

### Key Components
1. **find_missing_point_event_applications()**: Identifies applications missing point events
2. **WorldApplicationsPointEvent**: Junction table linking applications to point events
3. **PointsEvent**: Core point event model with KAIGAI_ADD_PURCHASE type
4. **Transaction safety**: All operations wrapped in atomic transactions

### Database Models Involved
- `WorldApplications`: International flight applications
- `PointsEvent`: Point events for various activities
- `WorldApplicationsPointEvent`: Junction table
- `Member`: User member information

### Key Constants
- `LIMIT_CHECK_DATE = "2024-08-01"`: Only process applications before this date
- `INTERNATIONAL_FLIGHT_POINT_DELAY_DAYS = 30`: Days after arrival to award points
- `STATUS_ARRIVED = 899`: Status code for arrived applications

## Business Logic
1. Find all WorldApplications with status 899 (arrived) before 2024-08-01
2. Check if corresponding PointsEvent exists for each application
3. Create missing PointsEvent records with:
   - Event type: KAIGAI_ADD_PURCHASE
   - Points: rewarded_points from application
   - Date: latest_arrival_date + 30 days
4. Create WorldApplicationsPointEvent junction records
5. Update points summary for affected members

## Error Handling
- Member not found: Log warning and skip
- No arrival date: Log warning and skip
- Database errors: Rollback transaction
- Invalid member numbers: Return error message

## Performance Considerations
- Uses bulk operations where possible
- Filters by member numbers to limit scope
- Single query for all applications
- Efficient member lookup with dictionary

## Dependencies
- Django ORM
- Transaction management
- Logging system
- Member and PointsEvent models
- WorldApplications model from kaigai app

## Next Steps
1. Test with real data
2. Optimize for large member lists
3. Add monitoring and alerting
4. Integrate with points summary update
5. Add comprehensive error handling

## Related Files
- `application_v2/management/commands/find_missing_kaigai.py`
- `application_v2/models.py` (PointsEvent, WorldApplicationsPointEvent)
- `kaigai/models/world_applications.py`
- `member/models.py` 