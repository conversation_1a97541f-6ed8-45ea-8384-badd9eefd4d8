# Active Context: <code> - <Feature Name>

## ✔️ Status
- **Current Status**: [In Progress / Completed]
- **Last Updated**: [YYYY-MM-DD]

## ✏️ Business Requirements
- [List specific business requirements for this feature.]

## TODO List
- ✅ Task 1: Technical Design & Data Flow
- ❌ Task 2: [task X]
- ❌ Task 3: [task Y]

## 📝 Active Decisions
- [List active decisions and considerations for the feature.]

## 🔍 Technical Solution / Design
- [Describe the technical approach, e.g., API endpoints, components, etc.]

### ⇅ Data Flow (Mermaid chart)
```mermaid
  [Mermaid chart]
```

### 🔏 Security Patterns
- [List security patterns for the feature.]

### ⌨️ Test Cases
- [List test cases for the feature.]
