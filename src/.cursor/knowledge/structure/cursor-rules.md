# Cursor Rules

## Directory and File Operations

### Before Creating Directories
1. **Always Check Existence**
   - Use `list_dir` tool to check if directory exists
   - Only create if directory doesn't exist
   - Log the result of the check

2. **Error Handling**
   - Handle "directory not found" gracefully
   - Provide clear error messages
   - Suggest next steps

### File Operations
1. **Check Before Modify**
   - Verify file existence before operations
   - Use appropriate tools for different operations
   - Handle errors appropriately

2. **File Creation**
   - Follow project structure
   - Use consistent naming
   - Add necessary documentation

## Testing Guidelines

### Test Structure
1. **Directory Organization**
   - Follow Django test structure
   - Group related tests
   - Use clear naming conventions

2. **Test Files**
   - Prefix with `test_`
   - Match source file names
   - Include comprehensive docstrings

### Test Best Practices
1. **Independence**
   - Each test should be self-contained
   - Use fixtures for setup
   - Clean up after tests

2. **Coverage**
   - Test all code paths
   - Include edge cases
   - Test error conditions

## Code Style

### Python Standards
1. **PEP 8**
   - Follow Python style guide
   - Use consistent indentation
   - Keep lines under 120 characters

2. **Documentation**
   - Add docstrings to all modules
   - Document complex logic
   - Keep comments up to date

### Django Best Practices
1. **App Structure**
   - Modular design
   - Clear separation of concerns
   - Follow Django conventions

2. **Code Organization**
   - Logical file structure
   - Related code together
   - Easy to navigate
