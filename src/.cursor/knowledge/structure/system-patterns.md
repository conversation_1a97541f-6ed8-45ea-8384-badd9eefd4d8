# System Patterns – Flight Booking System

## 🏛️ System Architecture

The application follows a **microservices-oriented architecture** with clear separation between different booking domains and airline integrations.

- **Backend**: Django REST Framework (DRF) with multiple apps
- **Frontend**: HTML templates with Tailwind CSS for styling
- **Database**: PostgreSQL with Redis for caching
- **External Services**: Airline APIs, Payment Gateways, Flight Status Services

Business logic is distributed across specialized services, with clear boundaries between different booking domains.

## 🧱 Technology Stack

| Layer      | Technology               | Description                                     |
|------------|--------------------------|-------------------------------------------------|
| API        | Django REST Framework    | Core backend framework with RESTful endpoints   |
| Database   | PostgreSQL               | Relational database for booking data            |
| Cache      | Redis                    | Caching layer for flight searches and status    |
| ORM        | Django ORM               | Object-relational mapper for data access        |
| UI         | HTML + Tailwind CSS      | Clean, responsive UI with utility-first CSS     |
| Auth       | JWT                      | JSON Web Token authentication                   |
| External   | Airline APIs             | Integration with multiple airline systems       |
| Payment    | Multiple Gateways        | Support for various payment providers           |

## 🔁 Data Flow

1. **Flight Search Flow**:
   - User inputs search criteria
   - System queries airline APIs
   - Results cached in Redis
   - Response returned to user

2. **Booking Flow**:
   - User selects flight
   - System validates availability
   - Passenger information collected
   - Payment processed
   - Booking confirmed with airline
   - Reservation created in database

3. **Status Update Flow**:
   - Regular polling of airline APIs
   - Updates cached in Redis
   - Notifications sent to affected users

## 🧠 Key Patterns

- **Service Layer Pattern**:
  - Core business logic in service classes
  - Clear separation of concerns
  - Reusable across different views

- **Repository Pattern**:
  - Data access abstraction
  - Consistent interface for database operations
  - Easy to switch data sources

- **Adapter Pattern**:
  - Standardized interfaces for airline APIs
  - Consistent handling of different airline responses
  - Easy to add new airline integrations

- **Observer Pattern**:
  - Flight status updates
  - Booking status changes
  - Payment status notifications

- **Factory Pattern**:
  - Creation of different booking types
  - Payment gateway initialization
  - Airline-specific handlers

## 🧰 Developer Notes

- Use environment variables for API keys and sensitive data
- Implement proper error handling for airline API failures
- Cache flight search results to reduce API calls
- Use transactions for booking operations
- Implement proper logging for debugging

## 🔭 Scalability Considerations

- Horizontal scaling of application servers
- Database sharding for large booking volumes
- Redis cluster for distributed caching
- Message queues for async operations
- CDN for static content delivery

## Test Organization

### Directory Structure
```
src/
├── domestic_flight/         # Domestic flight booking
│   ├── models/
│   ├── services/
│   ├── tests/
│   └── views/
├── international_flight/    # International flight booking
│   ├── models/
│   ├── services/
│   ├── tests/
│   └── views/
├── payment/                # Payment processing
│   ├── models/
│   ├── services/
│   ├── tests/
│   └── views/
└── common/                 # Shared components
    ├── models/
    ├── services/
    ├── tests/
    └── utils/
```

### Test File Organization Rules

1. **Directory Separation**
   - Each domain has its own test directory
   - Tests grouped by feature and operation type
   - Shared test utilities in common directory

2. **File Naming Convention**
   - Domain-specific: `test_domain_feature.py`
   - Operation-specific: `test_operation_feature.py`
   - Integration tests: `test_integration_feature.py`

3. **Test Types**
   - Unit tests for individual components
   - Integration tests for API interactions
   - End-to-end tests for booking flows
   - Performance tests for critical paths

### Common Test Patterns

1. **Flight Search Tests**
   ```python
   from django.test import TestCase
   
   class TestFlightSearch(TestCase):
       def test_search_parameters_validation(self):
           pass
       def test_airline_api_integration(self):
           pass
       def test_result_caching(self):
           pass
       def test_error_handling(self):
           pass
   ```

2. **Booking Tests**
   ```python
   from django.test import TestCase
   
   class TestBooking(TestCase):
       def test_booking_creation(self):
           pass
       def test_payment_processing(self):
           pass
       def test_airline_confirmation(self):
           pass
       def test_booking_cancellation(self):
           pass
   ```

3. **Status Update Tests**
   ```python
   from django.test import TestCase
   
   class TestStatusUpdates(TestCase):
       def test_flight_status_changes(self):
           pass
       def test_notification_delivery(self):
           pass
       def test_cache_invalidation(self):
           pass
   ```

4. **Integration Tests**
   ```python
   from django.test import TestCase
   
   class TestBookingFlow(TestCase):
       def test_complete_booking_process(self):
           pass
       def test_payment_failure_handling(self):
           pass
       def test_airline_api_failure(self):
           pass
   ```

## Testing Patterns

### Test Data Generation
- **Always use Factory Boy for test data**: Instead of creating objects directly, use factories to generate test data
- **Factory Boy + Faker**: Combine Factory Boy with Faker to generate realistic test data
- Benefits:
  - Consistent test data generation
  - Reduced test maintenance
  - More realistic test scenarios
  - Easier to modify test data structure
  - Avoid repetition in test code

Example:
```python
# Instead of this:
task = Task.objects.create(
    title="Test Task",
    description="Test Description",
    is_completed=False
)

# Use this:
task = TaskFactory()  # Creates with random realistic data
# Or with specific overrides:
task = TaskFactory(is_completed=True)
```

### Factory Best Practices
1. **Use build() vs create()**:
   - `TaskFactory.build()` - Creates object without saving to DB
   - `TaskFactory()` or `TaskFactory.create()` - Creates and saves to DB

2. **Faker Integration**:
   ```python
   class TaskFactory(DjangoModelFactory):
       title = factory.LazyAttribute(lambda _: fake.sentence())
       description = factory.LazyAttribute(lambda _: fake.paragraph())
   ```

3. **Sequence for Unique Fields**:
   ```python
   class UserFactory(DjangoModelFactory):
       email = factory.Sequence(lambda n: f'user{n}@example.com')
   ```

4. **Related Objects**:
   ```python
   class CommentFactory(DjangoModelFactory):
       task = factory.SubFactory(TaskFactory)
   ```

### Test Structure
1. Use Django TestCase setUp/tearDown for common setup
2. Follow AAA pattern (Arrange-Act-Assert)
3. Keep tests focused and isolated

## API Patterns
[... existing API patterns content ...]

## Common Package Structure

The `common` package contains shared functionality used across the project:

```
src/common/
├── models/           # Base model classes
│   ├── __init__.py
│   └── base.py      # TimeStampedModel, etc.
├── validators/       # Shared validators
│   ├── __init__.py
│   └── fields.py    # Field validators
└── utils/           # Utility functions
    ├── __init__.py
    └── helpers.py   # Helper functions
```

### Validators
1. Field Validators:
   - Place in `common.validators`
   - Focus on reusable validation logic
   - Keep validators simple and single-purpose
   - Include clear error messages
   - Example:
   ```python
   def validate_not_whitespace(value, field_name, allow_empty=False):
       """Validate string field is not empty/whitespace."""
       if allow_empty and not value:
           return value
       if not value or not value.strip():
           raise ValidationError(f'{field_name} cannot be empty.')
       return value.strip()
   ```

2. Usage Guidelines:
   - Import validators from common package
   - Use in serializers for field validation
   - Keep model validation minimal
   - Document parameters and return values

### Best Practices for Common Code
1. Keep It Simple:
   - Single responsibility principle
   - Clear documentation
   - Minimal dependencies
   - Easy to test

2. Naming Conventions:
   - Use descriptive names
   - Follow Python naming conventions
   - Include type hints where helpful
   - Document parameters

3. Error Handling:
   - Use appropriate exception types
   - Include helpful error messages
   - Handle edge cases
   - Document exceptions

4. Testing:
   - Write comprehensive tests
   - Test edge cases
   - Test with invalid inputs
   - Document test cases

## Directory Management

### Directory Creation
1. **Check Before Create**
   - Always check if directory exists before creating
   - Use `list_dir` tool to check existence
   - Create only if directory doesn't exist
   - Log the action (create new or already exists)

2. **Directory Structure**
   - Follow Django app structure
   - Keep related files together
   - Use clear naming conventions

## File Management

### File Operations
1. **Check Before Operation**
   - Verify file existence before any operation
   - Handle missing files gracefully
   - Provide clear error messages

2. **File Naming**
   - Use descriptive names
   - Follow project conventions
   - Include appropriate extensions

## Testing Structure

### Test Organization
1. **Test Directory Structure**
   ```
   app_name/
   ├── tests/
   │   ├── __init__.py
   │   ├── models/
   │   │   └── test_*.py
   │   ├── views/
   │   │   └── test_*.py
   │   ├── serializers/
   │   │   └── test_*.py
   │   └── services/
   │       └── test_*.py
   ```

2. **Test File Naming**
   - Prefix with `test_`
   - Match source file name
   - Descriptive of what's being tested

### Test Best Practices
1. **Test Independence**
   - Each test should be independent
   - Use fixtures for common setup
   - Clean up after tests

2. **Test Coverage**
   - Aim for comprehensive coverage
   - Test edge cases
   - Test error conditions

## Code Organization

### Project Structure
1. **Django Apps**
   - Modular and focused
   - Clear responsibilities
   - Minimal dependencies

2. **Code Layout**
   - Consistent structure
   - Logical grouping
   - Easy to navigate

## Error Handling

### Error Patterns
1. **Graceful Degradation**
   - Handle errors appropriately
   - Provide useful messages
   - Log important information

2. **User Feedback**
   - Clear error messages
   - Actionable information
   - Appropriate status codes

## Django Project Structure

### Directory Organization
```
src/
  ├── app_name/
  │   ├── filters/           # All filter classes
  │   │   ├── __init__.py
  │   │   └── task.py       # Task-related filters
  │   ├── views/            # ViewSets and Views
  │   ├── serializers/      # Serializers
  │   ├── models/          # Models
  │   └── tests/           # Tests matching the structure above
      └── filters/         # Tests for filters
```

### Code Organization Principles

1. **Separation of Concerns**
   - Each type of component (filters, views, serializers) should be in its own directory
   - Avoid mixing different types of logic in the same file
   - Keep files focused and single-purpose

2. **Filter Organization**
   - Place all filter classes in `filters/` directory
   - Name filter files after their corresponding models (e.g., `task.py` for `TaskFilter`)
   - Import filters in views using relative imports from filters package
   - Keep filter logic separate from view logic for better maintainability
   - Consider creating base filters for common filtering patterns

3. **Testing Structure**
   - Mirror the main code structure in tests
   - Create separate test files for filters
   - Group related test cases in classes
   - Use fixtures effectively to reduce code duplication

4. **Import Guidelines**
   - Use relative imports within the app
   - Keep imports organized and grouped (stdlib, django, third-party, local)
   - Avoid circular imports

5. **Naming Conventions**
   - Filter classes: `ModelNameFilter`
   - Filter files: lowercase, matching model name
   - Test files: `test_modelname_filter.py`

### Best Practices

1. **Filter Implementation**
   - Define filters in dedicated filter classes
   - Use appropriate filter fields based on data type
   - Document filter fields and their purpose
   - Consider performance implications of filters

2. **View Integration**
   - Import filters from the filters package
   - Set `filterset_class` in ViewSet configuration
   - Document available filters in ViewSet docstrings

3. **Testing**
   - Test each filter field independently
   - Test combinations of filters
   - Test edge cases and invalid inputs
   - Use appropriate assertions for filter results

4. **Maintenance**
   - Keep filter logic simple and focused
   - Update filters when model fields change
   - Consider backwards compatibility
   - Document any changes in filtering behavior

## View Organization Rule (2024-06-xx)

**View Organization Guidelines:**
- All views serving API endpoints (DRF ViewSet, APIView, generic API views) must be placed in dedicated files/modules, e.g., `views/task.py`, `views/tag.py`.
- All views serving web forms (Django class-based views such as ListView, CreateView, UpdateView, DeleteView, etc.) must be placed in a subdirectory `views/forms/`, e.g., `views/forms/task.py`.
- Do not mix API views and form views in the same file/module.
- When importing into `urls.py`, always import from the correct separated location.
- This rule ensures a clear, maintainable, and scalable codebase, and enforces separation of concerns.

**Example:**
```
src/tasks/views/
├── task.py         # API views for Task
├── tag.py          # API views for Tag
└── forms/
    └── task.py     # Form views for Task (ListView, CreateView, ...)
```

Apply this rule to all apps in the project.
