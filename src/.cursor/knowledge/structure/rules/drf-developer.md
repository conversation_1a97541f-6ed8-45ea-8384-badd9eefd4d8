### Unit Test
- **Always use Factory Boy + Faker for test data generation**
  - Avoid direct model creation in tests
  - Use `TaskFactory.build()` for non-persisted objects
  - Use `TaskFactory()` for DB-saved objects
  - Leverage Faker for realistic test data
  - Keep test data generation DRY and maintainable

- Write tests using the **AAA pattern** (`Arrange-Act-Assert`)
  - Arrange: Set up test data using factories
  - Act: Perform the action being tested
  - Assert: Verify the results

- Use **Django TestCase setUp/tearDown** for common setup
  - Use `setUp()` method for common test setup
  - Keep setup focused and reusable
  - Use `APIClient` for API tests

- Test both **success and error cases**
  - Test valid inputs and expected outcomes
  - Test edge cases and error conditions
  - Verify error messages and status codes

- Maintain **high coverage**, but focus on testing business logic
  - Aim for 90%+ coverage on business logic
  - Test API endpoints thoroughly
  - Test model methods and properties
  - Test serializer validation

Example:
```python
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

class TestTaskAPI(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.task = TaskFactory()  # Use factory for test data
        self.url = reverse("task-detail", args=[self.task.id])

    def test_update_task(self):
        # Use factory for test data
        new_data = {
            "title": TaskFactory.build().title,
            "description": TaskFactory.build().description
        }
        response = self.client.patch(self.url, new_data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["title"], new_data["title"])
```
