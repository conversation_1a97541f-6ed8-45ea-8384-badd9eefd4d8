from django.db import models
from django.utils import timezone

from api.constants import NOTIFICATION_DOCUMENT_TYPES
from approve_flow.states import SubmitFromShop
from approve_flow_settings.models import ApproveFlowSetting
from authentication.models import User
from companies.models import Company
from construction_methods.models import (
    ConstructionMethod,
    ConstructionStepType,
)
from management_sheets.models import ManagementSheet
from utilities.helpers.notifications import save_notifications
from utilities.model.custom_model import CustomModel
from .choices import (
    DiagnosticConditionTypes,
    DiagnosticStatus,
    DiagnosticTypes,
)


class Diagnostic(CustomModel):
    # Basic information
    cms_uuid = models.UUIDField(null=True)
    type = models.CharField(max_length=16, choices=DiagnosticTypes.choices)
    status = models.CharField(
        max_length=32,
        choices=DiagnosticStatus.choices,
        default=DiagnosticStatus.TEMP.value,
    )
    diagnosis_date = models.DateField(null=True, blank=True)
    note_backup = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # Construction information
    construction_zipcode = models.CharField(max_length=16, blank=True, null=True)
    construction_prefecture = models.CharField(max_length=64, null=True, blank=True)
    construction_address = models.CharField(max_length=64, null=True, blank=True)
    construction_address_extend = models.CharField(max_length=64, null=True, blank=True)
    construction_owner = models.CharField(max_length=64, blank=True, null=True)
    phone_number = models.CharField(max_length=64, blank=True, null=True)
    # Building information
    building_name = models.CharField(max_length=64, null=True, blank=True)
    building_name_furigana = models.CharField(max_length=64, null=True, blank=True)
    building_completion_date = models.DateField(null=True, blank=True)
    building_age = models.IntegerField(null=True, blank=True)
    building_renovation_history = models.BooleanField(default=True)
    building_renovation_times = models.IntegerField(null=True, blank=True)
    building_structure = models.CharField(max_length=64, null=True, blank=True)
    building_area = models.IntegerField(blank=True, default=0)
    building_painted_area_backup = models.IntegerField(blank=True, default=0)
    # Confirmation content
    confirm_date = models.DateField(null=True, blank=True)
    expected_approve_date = models.DateField(null=True, blank=True)
    # application form
    application_date = models.DateField(null=True, blank=True)
    construction_method_backup_id = models.CharField(max_length=10, null=True, blank=True)
    # Relationship
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING)
    company_phone = models.CharField(max_length=16, null=True, blank=True)
    company_fax = models.CharField(max_length=16, null=True, blank=True)
    person = models.ForeignKey(User, on_delete=models.CASCADE)
    # Kanna infomation
    kanna_image_category_uuid = models.UUIDField(null=True, blank=True)
    management_sheet = models.ForeignKey(
        ManagementSheet,
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        related_name="diagnostic_management_sheet",
    )
    # Approve flow setting
    approve_flow_setting = models.ForeignKey(
        ApproveFlowSetting,
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        related_name="diagnostic_approved_flow_setting",
    )
    pdf_url = models.URLField(blank=True, null=True)

    def __init__(self, *args, **kwargs) -> None:
        self.state = kwargs.get("state", SubmitFromShop)
        super().__init__(*args, **kwargs)

    class Meta:
        db_table = "diagnostic"
        ordering = ["-id"]

    def __str__(self):
        return f"SDS no. {self.pk}"

    def handle_approve(self):
        self.state.handle_approve()

    def handle_submit(self):
        self.state.handle_submit()

    def handle_reject(self):
        self.state.handle_reject()

    def handle_return(self):
        self.state.handle_return()

    def get_total_photos(self):
        return DiagnosticConditionPhoto.objects.filter(
            diagnostic_condition__diagnostic_id=self.id
        ).count()

    def send_notifications(
        self, verb, sender, recipients, from_type="", from_level="LEVEL_1"
    ):
        if recipients is None:
            return True
        action_object = self
        expected_approve_date = ""
        if self.expected_approve_date is not None:
            try:
                expected_approve_date = self.expected_approve_date.strftime("%Y-%m-%d")
            except Exception as e:
                print(e)
                expected_approve_date = self.expected_approve_date

        data = {
            "id": self.id,
            "from": from_type,
            "from_level": from_level,
            "action": verb,
            "document_type": NOTIFICATION_DOCUMENT_TYPES["SDS"],
            "construction_uuid": str(self.cms_uuid),
            "building_name": self.building_name if self.building_name else "",
            "request_account_number": "",
            "approved_date": expected_approve_date,
        }

        save_notifications(
            recipients=recipients,
            sender=sender,
            verb=verb,
            action_object=action_object,
            data=data,
        )


class DiagnosticCondition(CustomModel):
    type = models.CharField(max_length=32, choices=DiagnosticConditionTypes.choices)
    title = models.CharField(max_length=32)
    diagnostic = models.ForeignKey(
        Diagnostic, on_delete=models.CASCADE, related_name="diagnostic_conditions"
    )
    kanna_image_category_uuid = models.UUIDField(null=True)
    is_selected = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        db_table = "diagnostic_condition"
        # constraints = [
        #     models.UniqueConstraint(
        #         fields=["type", "title", "id"],
        #         name="unique type and condition in diagnostic",
        #     )
        # ]


class DiagnosticConditionItem(CustomModel):
    title = models.CharField(max_length=128, null=True)
    body = models.JSONField(blank=True, default=dict)
    kanna_image_category_uuid = models.UUIDField(null=True)
    diagnostic_condition = models.ForeignKey(
        DiagnosticCondition, on_delete=models.CASCADE, related_name="items", null=True
    )

    class Meta:
        db_table = "diagnostic_condition_item"
        ordering = ["id"]


class DiagnosticConditionPhoto(CustomModel):
    photo_uuid = models.UUIDField(null=True, blank=True)
    diagnostic_condition = models.ForeignKey(
        DiagnosticCondition, on_delete=models.CASCADE, related_name="photos", null=True
    )
    diagnostic_condition_item = models.ForeignKey(
        DiagnosticConditionItem,
        on_delete=models.CASCADE,
        related_name="photos",
        null=True,
    )

    class Meta:
        db_table = "diagnostic_condition_photo"


class DiagnosticConstructionMethod(models.Model):
    diagnostic = models.ForeignKey(
        Diagnostic, on_delete=models.CASCADE, null=True, related_name="diagnostic_construction_methods"
    )
    painting_area = models.DecimalField(max_digits=20, decimal_places=1, blank=True, null=True)
    note = models.CharField(max_length=255, null=True, blank=True)
    construction_method = models.ForeignKey(
        ConstructionMethod,
        on_delete=models.CASCADE,
        null=True
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "diagnostic_construction_method"
        ordering = ["id"]


class DiagnosticConstructionMethodEstimation(models.Model):
    construction_method_type = models.CharField(
        max_length=255, choices=ConstructionStepType.choices
    )
    diagnostic_backup_id = models.CharField(max_length=10, null=True, blank=True)
    # Number of paint cans
    number_of_paint_cans = models.FloatField(null=True, blank=True)
    number_of_paint_to_use = models.FloatField(null=True, blank=True)

    diagnostic_construction_method = models.ForeignKey(
        DiagnosticConstructionMethod,
        on_delete=models.CASCADE,
        null=True,
        related_name="estimations",
    )
    class Meta:
        db_table = "diagnostic_construction_method_estimation"
        ordering = ["id"]
