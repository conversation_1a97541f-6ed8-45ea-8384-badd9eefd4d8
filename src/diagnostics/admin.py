from django.contrib import admin

from .models import (
    Diagnostic,
    DiagnosticCondition,
    DiagnosticConditionItem,
    DiagnosticConditionPhoto,
    DiagnosticConstructionMethod,
    DiagnosticConstructionMethodEstimation,
)


class DiagnosticAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "type",
        "status",
        "diagnosis_date",
    )
    fieldsets = (
        (
            "Basic information",
            {
                "fields": (
                    "cms_uuid",
                    "type",
                    "status",
                    "diagnosis_date",
                    "kanna_image_category_uuid",
                )
            },
        ),
        (
            "Construction information",
            {
                "fields": (
                    "construction_zipcode",
                    "construction_prefecture",
                    "construction_address",
                    "construction_address_extend",
                    "construction_owner",
                    "phone_number",
                ),
            },
        ),
        (
            "Building information",
            {
                "fields": (
                    "building_name",
                    "building_completion_date",
                    "building_age",
                    "building_renovation_history",
                    "building_renovation_times",
                    "building_structure",
                    "building_area",
                    "building_painted_area",
                ),
            },
        ),
        (
            "Confirmation content",
            {
                "fields": (
                    "confirm_date",
                    "expected_approve_date",
                )
            },
        ),
        (
            "Application form",
            {
                "fields": (
                    "application_date",
                    "construction_method",
                ),
            },
        ),
        (
            "Approve Flow",
            {
                "fields": ("approve_flow_setting",),
            },
        ),
        (
            "Relation",
            {
                "fields": ("company", "person", "management_sheet"),
            },
        ),
    )


class DiagnosticConditionAdmin(admin.ModelAdmin):
    list_display = ("type", "title", "diagnostic")


class DiagnosticConditionPhotoAdmin(admin.ModelAdmin):
    list_display = ("photo_uuid", "diagnostic_condition")


class DiagnosticConditionItemAdmin(admin.ModelAdmin):
    list_display = ("title", "body", "diagnostic_condition")


class DiagnosticEstimationAdmin(admin.ModelAdmin):
    list_display = (
        "construction_method_type",
        "number_of_paint_cans",
        "number_of_paint_to_use",
        "diagnostic_construction_method",
    )


class DiagnosticConstructionMethodAdmin(admin.ModelAdmin):
    list_display = (
        "painting_area",
        "construction_method",
        "diagnostic",
        "note"
    )

admin.site.register(Diagnostic, DiagnosticAdmin)
admin.site.register(DiagnosticCondition, DiagnosticConditionAdmin)
admin.site.register(DiagnosticConditionPhoto, DiagnosticConditionPhotoAdmin)
admin.site.register(DiagnosticConditionItem, DiagnosticConditionItemAdmin)
admin.site.register(DiagnosticConstructionMethod, DiagnosticConstructionMethodAdmin)
admin.site.register(DiagnosticConstructionMethodEstimation, DiagnosticEstimationAdmin)
