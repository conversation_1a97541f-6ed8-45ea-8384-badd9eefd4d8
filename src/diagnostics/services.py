from api.constants import (
    EXISTING_PAINT_FILM_CONDITION_FOLDER,
    KANNA_DIAGNOSTIC_FOLDER_NAME,
    OTHER_CONDITION_FOLDER,
    SUBSTRATE_CONDITION_FOLDER,
)
from kanna_api.cms_image import create_image_category, get_image_categories
from kanna_api.models.user_kanna import UserKanna

from .models import DiagnosticConditionTypes


def gen_diagnostic_conditions_dict():
    diagnostic_condition_dict = {
        diagnostic_condition_type.lower(): []
        for diagnostic_condition_type, _ in DiagnosticConditionTypes.choices
    }
    return diagnostic_condition_dict


def get_uuid_folder_for_sds(user_kanna, cms_uuid):
    list_folders = get_image_categories(user_kanna, cms_uuid)
    for folder in list_folders:
        if folder["name"] == KANNA_DIAGNOSTIC_FOLDER_NAME:
            return folder["uuid"]
    return None


def create_sub_folder(user, cms_uuid, parent_image_category_uuid, folder_name):
    user_kanna = UserKanna.objects.get(email=user.email)
    response = create_image_category(
        user_kanna,
        cms_uuid,
        {
            "name": folder_name,
            "parentImageCategoryUuid": parent_image_category_uuid,
        },
    )
    return response["imageCategory"]["uuid"]


def create_sds_images_folder_tree(user, cms_uuid, image_category_name):
    user_kanna = UserKanna.objects.get(email=user.email)
    sds_image_category_uuid = get_uuid_folder_for_sds(user_kanna, cms_uuid)
    sds_image_folder = create_image_category(
        user_kanna,
        cms_uuid,
        {
            "name": image_category_name,
            "parentImageCategoryUuid": sds_image_category_uuid,
        },
    )
    sds_image_folder_uuid = sds_image_folder["imageCategory"]["uuid"]
    # create sub folder
    uuid_dict = {}
    list_sub_folder_name = [
        SUBSTRATE_CONDITION_FOLDER,
        EXISTING_PAINT_FILM_CONDITION_FOLDER,
        OTHER_CONDITION_FOLDER,
    ]
    for sub_folder_name in list_sub_folder_name:
        uuid = create_sub_folder(
            user_kanna, cms_uuid, sds_image_folder_uuid, sub_folder_name
        )
        uuid_dict[sub_folder_name] = uuid

    return {
        "sds_folder_uuid": sds_image_folder_uuid,
        **uuid_dict,
    }
