from __future__ import annotations

import logging
from typing import Any, Dict, List

from rest_framework import serializers

from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db.models import Q, TextField
from django.db.models.functions import Cast

from api.constants import (
    EXISTING_PAINT_FILM_CONDITION_FOLDER,
    KANNA_USER_ROLE,
    MAX_IMAGES,
    OTHER_CONDITION_FOLDER,
    SUBSTRATE_CONDITION_FOLDER,
    KannaUserRoleEnum,
)
from approve_flow.models import (
    ApproveFlowSettingInstance,
    ApproveFlowStep,
    ApproveFlowStepStatusChoices,
    OwnerElectronicMark,
    UserElectronicMark,
    UserRoleType,
)
from approve_flow.services import get_min_max_level_in_company
from approve_flow_settings.models import ApproveFlowSetting
from approve_flow_settings.serializers import ApproveFlowSettingSerializer
from authentication.models import User
from cms.services import fetch_cms_data_map, get_cms_kanna
from cms_images.services import fetch_image_concurrently
from companies.models import Company, CompanyType
from companies.serializers import CompanySerializer
from construction_methods.models import (
    ConstructionMethod,
    ConstructionStepType,
)
from construction_methods.serializers import (
    ConstructionMethodSerializer,
    ListConstructionMethodSerializer,
)
from diagnostics.choices import DiagnosticStatus
from kanna_api.cms_image import get_image
from kanna_api.models.user_kanna import UserKanna
from management_sheets.models import ManagementSheet, ManagementSheetStatus
from users.models import UserConstruction
from users.serializers import (
    UserKannaLiteSerializer,
    UserLiteSerializer,
    UserSerializer,
)

from .models import (
    Diagnostic,
    DiagnosticCondition,
    DiagnosticConditionItem,
    DiagnosticConditionPhoto,
    DiagnosticConditionTypes,
    DiagnosticConstructionMethod,
    DiagnosticConstructionMethodEstimation,
)

logger = logging.getLogger(__name__)


def get_cms_data_for_diagnostics(
    diagnostics: List[Diagnostic], kanna_user
) -> Dict[str, Any]:
    """
    Fetch CMS data for multiple diagnostics in parallel.
    Args:
        diagnostics: List of diagnostic objects
        kanna_user: Kanna user for API authentication
    Returns:
        Dict mapping cms_uuid to cms_data, empty dict for failed requests
    """
    # Extract unique cms_uuids
    cms_uuids = list(set([diagnostic.cms_uuid for diagnostic in diagnostics]))
    return fetch_cms_data_map(cms_uuids, kanna_user)


class DiagnosticEstimationsSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(allow_null=True)

    class Meta:
        model = DiagnosticConstructionMethodEstimation
        fields = (
            "id",
            "construction_method_type",
            "number_of_paint_cans",
            "number_of_paint_to_use",
        )


class DetailDiagnosticConstructionMethodSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    estimations = DiagnosticEstimationsSerializer(many=True)
    construction_method = ListConstructionMethodSerializer(read_only=True)

    class Meta:
        model = DiagnosticConstructionMethod
        fields = (
            "id",
            "painting_area",
            "construction_method",
            "note",
            "estimations",
        )


class DiagnosticConstructionMethodSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(allow_null=True)
    estimations = DiagnosticEstimationsSerializer(many=True)
    construction_method = serializers.IntegerField(allow_null=True)
    painting_area = serializers.CharField(allow_blank=True, allow_null=True)

    class Meta:
        model = DiagnosticConstructionMethod
        fields = (
            "id",
            "painting_area",
            "construction_method",
            "note",
            "estimations",
        )

    def validate_painting_area(self, value):
        if not value:
            return None
        try:
            value = float(value)
            if value <= 0:
                raise serializers.ValidationError(
                    "Please enter a number greater than 0 for the paint area."
                )
            return round(value, 1)
        except ValueError:
            raise serializers.ValidationError("Please enter a number")


class CreateDiagnosticConstructionMethodsSerializer(serializers.Serializer):
    diagnostic_construction_methods = DiagnosticConstructionMethodSerializer(many=True)

    def update(self, instance, validated_data):
        diagnostic_construction_methods = validated_data.get("diagnostic_construction_methods", [])
        estimations_to_create = []
        estimations_to_update = []

        # Delete diagnostic construction methods, so all estimations and actual construction is deleted by cascade
        diagnostic_construction_methods_delete = DiagnosticConstructionMethod.objects.filter(diagnostic=instance)
        diagnostic_construction_methods_delete.delete()

        # Update or create diagnostic_construction_method
        for diagnostic_construction_method in diagnostic_construction_methods:
            construction_method_instance = ConstructionMethod.objects.filter(
                id=diagnostic_construction_method.get("construction_method", None)).first()
            obj, created = DiagnosticConstructionMethod.objects.update_or_create(
                id=diagnostic_construction_method.get("id", None),
                defaults={
                    "painting_area": diagnostic_construction_method.get("painting_area"),
                    "construction_method": construction_method_instance,
                    "note": diagnostic_construction_method.get("note"),
                    "diagnostic": instance
                }
            )

            # Add estimation in list create when does not exist in db and
            # Add estimation in list update when get estimation successfully
            estimations = diagnostic_construction_method.get('estimations')
            for estimation in estimations:
                record = DiagnosticConstructionMethodEstimation(**estimation, diagnostic_construction_method=obj)
                try:
                    current = DiagnosticConstructionMethodEstimation.objects.get(id=estimation["id"])
                    record.id = current.id
                    estimations_to_update.append(record)
                except DiagnosticConstructionMethodEstimation.DoesNotExist:
                    estimations_to_create.append(record)

        # Bulk update and create estimations
        DiagnosticConstructionMethodEstimation.objects.bulk_update(
            estimations_to_update,
            fields=[
                "construction_method_type",
                "number_of_paint_cans",
                "number_of_paint_to_use",
                "diagnostic_construction_method",
            ],
            batch_size=1000,
        )
        DiagnosticConstructionMethodEstimation.objects.bulk_create(
            estimations_to_create, ignore_conflicts=True, batch_size=1000
        )
        queryset = (
            DiagnosticConstructionMethod.objects.filter(diagnostic=instance)
            .select_related("construction_method")
            .prefetch_related("estimations")
        )
        return DetailDiagnosticConstructionMethodSerializer(queryset, many=True).data

class DiagnosticConditionPhotoSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DiagnosticConditionPhoto
        fields = ("id", "photo_uuid", "image_url")

    # @TODO: Optimize calling api from KANNA.
    # Now for each row, we call each KANNA api to get image
    def get_image_url(self, obj):
        try:
            # Get diagnostic and diagnostic_condition
            diagnostic_condition, diagnostic = self._get_diagnostic_and_condition(obj)
            if not diagnostic_condition or not diagnostic:
                return None

            # Attempt to get the image URL from the cached context
            cached_image_url = None
            if images_by_uuid := self.context.get("images_by_uuid"):
                cached_image_url = self._get_cached_image_url(
                    images_by_uuid=images_by_uuid,
                    image_uuid=obj.photo_uuid
                )

            # Fetch the image from Kanna if there is no cached image
            return cached_image_url or self._fetch_image_url_from_kanna(
                cms_uuid=diagnostic.cms_uuid,
                image_category_uuid=diagnostic_condition.kanna_image_category_uuid,
                image_uuid=obj.photo_uuid
            )

        except Exception:
            return ""

    def _get_diagnostic_and_condition(self, obj):
        """
        Extract diagnostic_condition and diagnostic from obj.
        Returns a tuple (diagnostic_condition, diagnostic) or (None, None) if not found.
        """
        diagnostic_condition = obj.diagnostic_condition
        if diagnostic_condition:
            diagnostic = diagnostic_condition.diagnostic

        else:
            diagnostic_condition_item = obj.diagnostic_condition_item
            if diagnostic_condition_item:
                diagnostic_condition = diagnostic_condition_item.diagnostic_condition
                diagnostic = diagnostic_condition.diagnostic if diagnostic_condition else None
            else:
                diagnostic = None

        return diagnostic_condition, diagnostic

    @staticmethod
    def _get_cached_image_url(images_by_uuid, image_uuid):
        if image_data := images_by_uuid.get(image_uuid):
            try:
                return image_data["image"]["imageUrl"]
            except Exception as e:
                return None

        return None

    def _fetch_image_url_from_kanna(self, cms_uuid, image_category_uuid, image_uuid):
        """
        Fetch the image URL from the Kanna service.
        """
        user_kanna = UserKanna.objects.get(email=self.context["user"].email)
        kanna_image = get_image(
            user=user_kanna,
            cms_uuid=cms_uuid,
            image_category_uuid=image_category_uuid,
            image_uuid=image_uuid
        )
        return kanna_image["image"]["imageUrl"]


class DiagnosticConditionItemSerializer(serializers.ModelSerializer):
    photos = DiagnosticConditionPhotoSerializer(many=True, read_only=True)
    images_uuid = serializers.ListField(child=serializers.UUIDField(), required=False)

    class Meta:
        model = DiagnosticConditionItem
        fields = (
            "id",
            "title",
            "body",
            "kanna_image_category_uuid",
            "photos",
            "images_uuid",
        )

    def update(self, instance, validated_data):
        if "photos" in validated_data:
            validated_data.pop("photos")

        return super().update(instance, validated_data)


class DiagnosticConditionSerialzer(serializers.ModelSerializer):
    photos = DiagnosticConditionPhotoSerializer(many=True, read_only=True)
    items = DiagnosticConditionItemSerializer(many=True)
    images_uuid = serializers.ListField(child=serializers.UUIDField(), required=False)

    class Meta:
        model = DiagnosticCondition
        fields = (
            "id",
            "type",
            "title",
            "items",
            "kanna_image_category_uuid",
            "images_uuid",
            "photos",
            "is_selected",
        )
        extra_kwargs = {
            "id": {"read_only": True},
            "type": {"required": True},
            "title": {"required": True},
            "items": {"required": True},
            "is_selected": {"required": False},
        }
        depth = 1

    def create_diagnostic_condition_item_photos(
        self, diagnostic_condition_item, images_uuid: list
    ):
        image_exists = (
            DiagnosticConditionPhoto.objects.filter(
                Q(diagnostic_condition_item=diagnostic_condition_item),
            )
            .annotate(str_id=Cast("photo_uuid", TextField()))
            .values_list("str_id", flat=True)
        )
        image_exists_list = list(image_exists)
        images_delete = list(set(image_exists_list) - set(images_uuid))
        images_add = list(set(images_uuid) - set(image_exists_list))
        images_processing = images_add + images_delete
        for image in images_processing:
            if str(image) in images_delete:
                DiagnosticConditionPhoto.objects.filter(
                    Q(diagnostic_condition=None),
                    Q(diagnostic_condition_item=diagnostic_condition_item),
                    Q(photo_uuid=image),
                ).delete()
            elif str(image) in images_add:
                DiagnosticConditionPhoto.objects.create(
                    photo_uuid=image,
                    diagnostic_condition=None,
                    diagnostic_condition_item=diagnostic_condition_item,
                )

    def update(self, instance, validated_data):
        if "photos" in validated_data:
            validated_data.pop("photos")

        items = validated_data.pop("items")
        for item in items:
            diagnostic_condition_item_instance = DiagnosticConditionItem.objects.get(
                id=item["id"]
            )
            images_uuid_list = item.pop("images_uuid", [])
            self.create_diagnostic_condition_item_photos(
                diagnostic_condition_item_instance, images_uuid_list
            )

            DiagnosticConditionItemSerializer().update(
                diagnostic_condition_item_instance, item
            )
        # return super().update(instance, validated_data)


class ListDiagnosticSerializer(serializers.ModelSerializer):
    person = UserSerializer()
    cms_title = serializers.SerializerMethodField(read_only=True)
    project_status = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = (
            "id",
            "type",
            "cms_uuid",
            "diagnosis_date",
            "status",
            "construction_zipcode",
            "construction_prefecture",
            "construction_address",
            "construction_address_extend",
            "cms_title",
            "project_status",
            "construction_owner",
            "building_name",
            "building_name_furigana",
            "phone_number",
            "person",
        )
        extra_kwargs = {}

    def get_cms_data(self, obj):
        cms_data_map = self.context.get("cms_data_map", {})
        return cms_data_map.get(obj.cms_uuid)

    def get_cms_title(self, obj):
        cms_data = self.get_cms_data(obj)
        if cms_data and isinstance(cms_data, dict):
            return cms_data.get("title", "")
        cms_title = ""
        if "request" in self.context:
            cms = get_cms_kanna(obj.cms_uuid, self.context["request"].user.kanna_user)
            cms_title = cms.get("title")
        return cms_title

    def get_project_status(self, obj):
        cms_data = self.get_cms_data(obj)
        if cms_data and isinstance(cms_data, dict):
            return cms_data.get("project_status", "")
        project_status = ""
        if "request" in self.context:
            cms = get_cms_kanna(obj.cms_uuid, self.context["request"].user.kanna_user)
            project_status = cms.get("project_status")
        return project_status


class GetCommentAndLevelSerializer(serializers.ModelSerializer):
    user = UserLiteSerializer()

    class Meta:
        model = ApproveFlowStep
        fields = ("level", "comment", "user")


class ElectronicMarkSerializer(serializers.ModelSerializer):
    user = UserKannaLiteSerializer()

    class Meta:
        model = UserElectronicMark
        fields = ("user", "diagnostic", "image_base64")


class GetDiagnosticDetailSerialzer(ListDiagnosticSerializer):
    diagnostic_conditions = serializers.SerializerMethodField()
    company = serializers.SerializerMethodField(read_only=True)
    diagnostic_construction_methods = DetailDiagnosticConstructionMethodSerializer(many=True, read_only=True)
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    actions = serializers.SerializerMethodField(read_only=True)
    comments = serializers.SerializerMethodField(read_only=True)
    electronic_mark = serializers.SerializerMethodField(read_only=True)
    owner_electronic_mark = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = ListDiagnosticSerializer.Meta.fields + (
            "cms_uuid",
            "confirm_date",
            "status",
            "expected_approve_date",
            "application_date",
            "building_completion_date",
            "building_age",
            "building_renovation_history",
            "building_renovation_times",
            "building_structure",
            "building_area",
            "confirm_date",
            "diagnostic_conditions",
            "company",
            "diagnostic_construction_methods",
            "approve_flow_setting",
            "actions",
            "comments",
            "electronic_mark",
            "owner_electronic_mark",
            "updated_at",
        )
        extra_kwargs = {}

    # Get list allow actions what current user can do
    def get_actions(self, obj):
        user = self.context["user"]
        company = user.kanna_user.company
        level = user.kanna_user.level
        list_actions = []
        if user.kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            if obj.status in (DiagnosticStatus.WAITING_APPROVAL,):
                list_actions.extend(["approve", "reject", "return", "signed_by_admin"])
            if (obj.status in (
                    DiagnosticStatus.APPROVED,
                    DiagnosticStatus.UPDATE_AFTER_APPROVE,
            ) and
                    obj.management_sheet.status in (ManagementSheetStatus.TEMP,
                                                    ManagementSheetStatus.RETURN)):
                list_actions.append("update_after_approve")
        elif company:

            max_level = None
            if company.type != CompanyType.SHOP:
                _, max_level = get_min_max_level_in_company(company)
            if (
                hasattr(obj, "diagnostic_approve_flow_setting_instance")
                and obj.diagnostic_approve_flow_setting_instance is not None
            ):
                approve_flow_setting_instance = (
                    obj.diagnostic_approve_flow_setting_instance
                )
                # Handle approve by level or not
                if (
                    not approve_flow_setting_instance.is_approve_level_dealer
                    and company.type == CompanyType.DEALER
                ) or (
                    not approve_flow_setting_instance.is_approve_level_act
                    and company.type == CompanyType.ACT
                ):
                    last_step = ApproveFlowStep.objects.filter(
                        diagnostic=obj,
                        approve_flow_setting_instance=approve_flow_setting_instance,
                        company=company,
                    ).first()
                else:
                    last_step = ApproveFlowStep.objects.filter(
                        status=ApproveFlowStepStatusChoices.OPEN,  # Avoid Step with duplicate status
                        diagnostic=obj,
                        approve_flow_setting_instance=approve_flow_setting_instance,
                        company=company,
                        level=level,
                    ).first()

            else:
                last_step = None

            if last_step:
                if last_step.status == ApproveFlowStepStatusChoices.OPEN:
                    list_actions.extend(["approve", "reject", "return"])
                if (
                    last_step.status == ApproveFlowStepStatusChoices.OPEN
                    and approve_flow_setting_instance.is_signed_by_dealer
                    and (
                        (
                            max_level == level
                            and approve_flow_setting_instance.is_approve_level_dealer
                        )
                        or not approve_flow_setting_instance.is_approve_level_dealer
                    )
                    and company.type == CompanyType.DEALER
                ):
                    list_actions.append("signed_by_dealer")
                if (
                    last_step.status == ApproveFlowStepStatusChoices.OPEN
                    and approve_flow_setting_instance.is_signed_by_act
                    and company.type == CompanyType.ACT
                    and (
                        (
                            max_level == level
                            and approve_flow_setting_instance.is_approve_level_act
                        )
                        or not approve_flow_setting_instance.is_approve_level_act
                    )
                ):
                    list_actions.append("signed_by_act")
                if (
                    company
                    and company.type == CompanyType.ACT
                    and obj.status
                    in (
                        DiagnosticStatus.APPROVED,
                        DiagnosticStatus.UPDATE_AFTER_APPROVE,
                    )
                    and obj.management_sheet.status
                    in (ManagementSheetStatus.TEMP, ManagementSheetStatus.RETURN)
                ):
                    list_actions.append("update_after_approve")

            else:
                if (
                    company
                    and company.type == CompanyType.SHOP
                    and obj.status in (DiagnosticStatus.TEMP, DiagnosticStatus.RETURN)
                    # and obj.approve_flow_setting is not None
                ):
                    list_actions.append("approve")

                if (
                    company
                    and company.type == CompanyType.ACT
                    and obj.status
                    in (
                        DiagnosticStatus.APPROVED,
                        DiagnosticStatus.UPDATE_AFTER_APPROVE,
                    )
                    and obj.management_sheet.status
                    in (ManagementSheetStatus.TEMP, ManagementSheetStatus.RETURN)
                ):
                    list_actions.append("update_after_approve")

        return list_actions

    def get_diagnostic_conditions(self, obj):
        diagnostic_condition_query = DiagnosticCondition.objects.filter(
            diagnostic=obj
        ).order_by("id")

        images_by_uuid = self.context.get("images_by_uuid")
        if not images_by_uuid:
            image_uuids = set()
            for diagnostic_condition in diagnostic_condition_query:
                diagnostic_condition_photo_uuids = (
                    diagnostic_condition
                    .photos.all()
                    .values_list("photo_uuid", flat=True)
                )
                diagnostic_condition_item_photo_uuids = (
                    diagnostic_condition
                    .items.prefetch_related("photos")
                    .values_list("photos__photo_uuid", flat=True)
                )
                image_uuids.update(diagnostic_condition_photo_uuids)
                image_uuids.update(diagnostic_condition_item_photo_uuids)
            image_uuids.discard(None)

            images_by_uuid = fetch_image_concurrently(
                user=self.context['user'].kanna_user,
                cms_uuid=obj.cms_uuid,
                image_uuids=image_uuids,
            )

        serializer = DiagnosticConditionSerialzer(
            diagnostic_condition_query,
            many=True,
            context={
                "user": self.context["user"],
                "images_by_uuid": images_by_uuid
            },
        )
        return serializer.data

    def get_company(self, obj):
        company_query = Company.objects.get(pk=obj.company.id)
        serializer = CompanySerializer(company_query)
        return serializer.data

    def get_comments(self, obj):
        shop_comment, dealer_comment, act_comment, admin_comment = (None,) * 4
        if (
            hasattr(obj, "diagnostic_approve_flow_setting_instance")
            and obj.diagnostic_approve_flow_setting_instance
        ):
            approve_flow_instance = obj.diagnostic_approve_flow_setting_instance
            shop_comment = (
                ApproveFlowStep.objects.filter(
                    company=approve_flow_instance.shop, diagnostic=obj
                )
                .only("comment")
                .first()
            )
            if shop_comment:
                # Get dealer comment
                dealer_comment = (
                    ApproveFlowStep.objects.filter(
                        company=approve_flow_instance.dealer,
                        diagnostic=obj,
                        pk__gt=shop_comment.id,
                    )
                    .only("comment", "level")
                    .all()
                )
                dealer_comment_serializer = GetCommentAndLevelSerializer(
                    dealer_comment, many=True
                ).data
                # Get act comment
                act_comment = (
                    ApproveFlowStep.objects.filter(
                        company=approve_flow_instance.act,
                        diagnostic=obj,
                        pk__gt=shop_comment.id,
                        is_admin_approved=False
                    )
                    .only("comment", "level", "user")
                    .all()
                )
                act_comment_serializer = GetCommentAndLevelSerializer(
                    act_comment, many=True
                ).data

                # Get admin comment
                admin_comment = ApproveFlowStep.objects.filter(
                    diagnostic=obj,
                    pk__gt=shop_comment.id,
                    is_admin_approved=True
                ).only("comment", "level", "user")
                admin_comment_serializer = GetCommentAndLevelSerializer(
                    admin_comment, many=True
                ).data

        comments = {
            "shop": shop_comment.comment if shop_comment else None,
            "dealer": dealer_comment_serializer if dealer_comment else None,
            "act": act_comment_serializer if act_comment else None,
            "admin": admin_comment_serializer if admin_comment else None,
        }
        return comments

    def get_electronic_mark(self, obj):

        user_list_electronic_mark = UserElectronicMark.objects.filter(diagnostic=obj)
        serializer = ElectronicMarkSerializer(user_list_electronic_mark, many=True)

        return serializer.data

    def get_owner_electronic_mark(self, obj):
        owner_electronic_mark = OwnerElectronicMark.objects.filter(
            diagnostic=obj
        ).first()
        if owner_electronic_mark:
            return owner_electronic_mark.image_base64
        return None


class DiagnosticSerializer(serializers.ModelSerializer):
    diagnostic_conditions = serializers.ListField(
        child=DiagnosticConditionSerialzer(), write_only=True
    )
    cms_uuid = serializers.UUIDField()
    person = serializers.DictField(read_only=True)
    company = serializers.DictField(read_only=True)
    person_id = serializers.IntegerField()
    company_id = serializers.IntegerField()
    approve_flow_setting_id = serializers.IntegerField(required=False, allow_null=True)
    latest_updated_at = serializers.DateTimeField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = "__all__"
        extra_fields = ["person_id", "company_id", "approve_flow_setting_id"]
        extra_kwargs = {
            "status": {"read_only": True},
        }

    def get_field_names(self, declared_fields, info):
        expanded_fields = super(DiagnosticSerializer, self).get_field_names(
            declared_fields, info
        )
        if getattr(self.Meta, "extra_fields", None):
            return expanded_fields + self.Meta.extra_fields
        else:
            return expanded_fields

    def validate(self, attrs):
        total_images, request_image_uuids = self._process_diagnostic_condition_photos(attrs)
        images_by_uuid = self._check_image_availability(attrs, request_image_uuids)
        self._check_image_limit(total_images)

        self.context.update({
            "images_by_uuid": images_by_uuid
        })

        try:
            Company.objects.get(id=attrs["company_id"])
            User.objects.get(id=attrs["person_id"])
            if approve_flow_setting_id := attrs.get("approve_flow_setting_id", None):
                ApproveFlowSetting.objects.get(id=approve_flow_setting_id)
            return super().validate(attrs)
        except ObjectDoesNotExist:
            raise serializers.ValidationError(
                {"related_fields": "Company, Person or ApproveFlowSetting not found"}
            )

    def _process_diagnostic_condition_photos(self, attrs):
        total = 0
        images_uuids = set()
        diagnostic_condition_folder_need_check = [
            "屋根既存塗膜",
            "吹付タイル",
            "スタッコ",
            "弾性タイル",
            "リシン",
            "塗装",
            "窯業系サイディング板",
        ]

        for diagnostic_condition in attrs.get("diagnostic_conditions", []):
            if "images_uuid" in diagnostic_condition:
                total += len(diagnostic_condition["images_uuid"])
                images_uuids.update(diagnostic_condition["images_uuid"])

            for item in diagnostic_condition.get("items", []):
                if "images_uuid" in item:
                    images_uuids.update(item["images_uuid"])
                    if diagnostic_condition["title"] in diagnostic_condition_folder_need_check:
                        total += len(item["images_uuid"])

        return total, images_uuids

    def _check_image_availability(self, attrs, request_image_uuids):
        if not request_image_uuids:
            return {}

        kanna_user = UserKanna.objects.get(email=self.context["user"].email)
        available_images_by_uuid = fetch_image_concurrently(
            user=kanna_user,
            cms_uuid=attrs["cms_uuid"],
            image_uuids=request_image_uuids
        )

        unavailable_img_uuids = self._get_unavailable_img_uuids(request_image_uuids, available_images_by_uuid)

        if unavailable_img_uuids:
            raise serializers.ValidationError(
                {
                    "images_uuid": "選択した画像が存在していません。",
                }
            )

        return available_images_by_uuid

    def _get_unavailable_img_uuids(self, image_uuids, available_images_by_uuid):
        unavailable_img_uuids = set(image_uuids) - set(available_images_by_uuid.keys())
        return unavailable_img_uuids

    def _check_image_limit(self, total):
        if total > MAX_IMAGES:
            raise serializers.ValidationError(
                {
                    "images_uuid": "画像の使用数枚数が、{}枚を超えています。".format(
                        MAX_IMAGES
                    )
                }
            )

    def get_parent_folder_by_type(self, type):
        if type == DiagnosticConditionTypes.SUBSTRATE.value:
            return SUBSTRATE_CONDITION_FOLDER
        if type == DiagnosticConditionTypes.EXISTING_PAINT_FILM.value:
            return EXISTING_PAINT_FILM_CONDITION_FOLDER
        if type == DiagnosticConditionTypes.OTHER.value:
            return OTHER_CONDITION_FOLDER

    def create_diagnostic_condition_items(
        self,
        items,
        user,
        cms_uuid,
        parent_image_category_uuid,
        diagnostic_condition_instance,
    ):
        for item in items:
            # item_folder_uuid = None
            # if item.get("has_photos"):
            # item_folder_uuid = create_sub_folder(
            #     user=user,
            #     cms_uuid=cms_uuid,
            #     parent_image_category_uuid=parent_image_category_uuid,
            #     folder_name=item["title"],
            # )
            # diagnostic_condition_item_instance = DiagnosticConditionItem(
            #     title=item["title"],
            #     body=item["body"],
            #     kanna_image_category_uuid=item_folder_uuid,
            #     diagnostic_condition=diagnostic_condition_instance,
            # )

            diagnostic_condition_item_instance = DiagnosticConditionItem(
                title=item["title"],
                body=item["body"],
                diagnostic_condition=diagnostic_condition_instance,
            )
            diagnostic_condition_item_instance.save()

    def create_management_sheet_temporary(self, diagnostic):
        management_sheet = ManagementSheet.objects.filter(
            cms_uuid=diagnostic.cms_uuid
        ).first()

        if management_sheet is None:
            management_sheet = ManagementSheet.objects.create(
                cms_uuid=diagnostic.cms_uuid,
                type="{}",
                construction_zipcode=diagnostic.construction_zipcode,
                prefecture=diagnostic.construction_prefecture,
                city=diagnostic.construction_address,
                full_address=diagnostic.construction_address_extend,
                pic_id=diagnostic.person.kanna_user.pk,
                building_name=diagnostic.building_name,
                client_name=diagnostic.construction_owner,
            )
        diagnostic.management_sheet = management_sheet
        diagnostic.save()

    def create_diagnostic_conditions_photos(self, diagnostic_condition, images_uuid):
        image_exists = (
            DiagnosticConditionPhoto.objects.filter(
                Q(diagnostic_condition__id=diagnostic_condition.id),
                Q(diagnostic_condition_item=None),
            )
            .annotate(str_id=Cast("photo_uuid", TextField()))
            .values_list("str_id", flat=True)
        )

        image_exists_list = list(image_exists)
        images_delete = list(set(image_exists_list) - set(images_uuid))
        images_add = list(set(images_uuid) - set(image_exists_list))
        images_processing = images_add + images_delete

        for image in images_processing:
            if str(image) in images_delete:
                DiagnosticConditionPhoto.objects.filter(
                    Q(diagnostic_condition=diagnostic_condition),
                    Q(diagnostic_condition_item=None),
                    Q(photo_uuid=image),
                ).delete()
            elif str(image) in images_add:
                DiagnosticConditionPhoto.objects.create(
                    photo_uuid=image,
                    diagnostic_condition=diagnostic_condition,
                )

    def create(self, validated_data):
        diagnostic_conditions = validated_data.pop("diagnostic_conditions")
        # Create Diagnostic
        diagnostic = Diagnostic.objects.create(**validated_data)

        # Create image folder for diagnostic in kanna

        # Create Image Category in Kanna and assign to Diagnostic
        # sds_image_folder = create_sds_images_folder_tree(
        #     self.context["user"], validated_data["cms_uuid"], diagnostic.__str__()
        # )

        # diagnostic.kanna_image_category_uuid = sds_image_folder["sds_folder_uuid"]
        diagnostic.kanna_image_category_uuid = None
        diagnostic.save()

        # Create Diagnostic Conditions
        for diagnostic_condition in diagnostic_conditions:
            # Create Image Sub Category in Kanna and assign to Diagnostic Condition
            # folder_uuid = create_sub_folder(
            #     user=self.context["user"],
            #     cms_uuid=diagnostic.cms_uuid,
            #     parent_image_category_uuid=sds_image_folder[
            #         self.get_parent_folder_by_type(diagnostic_condition["type"])
            #     ],
            #     folder_name=diagnostic_condition["title"],
            # )

            # @TODO: Recheck and reimplement it when update method get image from Kanna
            folder_uuid = diagnostic_condition.get("kanna_image_category_uuid", None)

            # Old follow create SDS
            # diagnostic_condition_instance = DiagnosticCondition(
            #     type=diagnostic_condition["type"],
            #     title=diagnostic_condition["title"],
            #     kanna_image_category_uuid=folder_uuid,
            #     diagnostic=diagnostic,
            # )

            diagnostic_condition_instance = DiagnosticCondition(
                type=diagnostic_condition["type"],
                title=diagnostic_condition["title"],
                is_selected=diagnostic_condition.get("is_selected", False),
                diagnostic=diagnostic,
            )
            diagnostic_condition_instance.save()

            # Create Diagnostic Items
            self.create_diagnostic_condition_items(
                items=diagnostic_condition.get("items", []),
                user=self.context["user"],
                cms_uuid=diagnostic.cms_uuid,
                parent_image_category_uuid=folder_uuid,
                diagnostic_condition_instance=diagnostic_condition_instance,
            )

        diagnostic_construction_method = DiagnosticConstructionMethod.objects.create(
            diagnostic=diagnostic
        )
        # Create Diagnostic - Construction Method Estimation
        estimations = []
        for type_data in ConstructionStepType:
            estimations.append(
                DiagnosticConstructionMethodEstimation(
                    construction_method_type=type_data,
                    diagnostic_construction_method=diagnostic_construction_method,
                )
            )
        DiagnosticConstructionMethodEstimation.objects.bulk_create(estimations)

        # create MS temporary
        self.create_management_sheet_temporary(diagnostic)

        return GetDiagnosticDetailSerialzer(
            diagnostic, context={"user": self.context["user"]}
        ).data

    def update(self, instance, validated_data):
        def create_new_approve_flow_setting_instance(approve_flow_setting_id):
            if approve_flow_setting_id:
                approve_flow_setting = ApproveFlowSetting.objects.get(
                    pk=approve_flow_setting_id
                )

                sds_approve_flow_setting_instance = (
                    ApproveFlowSettingInstance.objects.filter(
                        diagnostic=instance
                    ).first()
                )

                if sds_approve_flow_setting_instance is None:
                    approve_flow_setting_instance = ApproveFlowSettingInstance(
                        name=approve_flow_setting.name,
                        type=approve_flow_setting.type,
                        shop=approve_flow_setting.shop,
                        dealer=approve_flow_setting.dealer,
                        act=approve_flow_setting.act,
                        is_approve_level_dealer=approve_flow_setting.is_approve_level_dealer,
                        is_approve_level_act=approve_flow_setting.is_approve_level_act,
                        is_signed_by_shop=approve_flow_setting.is_signed_by_shop,
                        is_signed_by_dealer=approve_flow_setting.is_signed_by_dealer,
                        is_signed_by_act=approve_flow_setting.is_signed_by_act,
                        ref_setting=approve_flow_setting,
                        diagnostic=instance,
                    )
                    approve_flow_setting_instance.save()
                else:

                    sds_approve_flow_setting_instance = (
                        ApproveFlowSettingInstance.objects.get(diagnostic=instance)
                    )

                    sds_approve_flow_setting_instance.name = approve_flow_setting.name
                    sds_approve_flow_setting_instance.type = approve_flow_setting.type
                    sds_approve_flow_setting_instance.shop = approve_flow_setting.shop
                    sds_approve_flow_setting_instance.dealer = (
                        approve_flow_setting.dealer
                    )
                    sds_approve_flow_setting_instance.act = approve_flow_setting.act
                    sds_approve_flow_setting_instance.is_approve_level_dealer = (
                        approve_flow_setting.is_approve_level_dealer
                    )
                    sds_approve_flow_setting_instance.is_approve_level_act = (
                        approve_flow_setting.is_approve_level_act
                    )
                    sds_approve_flow_setting_instance.is_signed_by_shop = (
                        approve_flow_setting.is_signed_by_shop
                    )
                    sds_approve_flow_setting_instance.is_signed_by_dealer = (
                        approve_flow_setting.is_signed_by_dealer
                    )
                    sds_approve_flow_setting_instance.is_signed_by_act = (
                        approve_flow_setting.is_signed_by_act
                    )
                    sds_approve_flow_setting_instance.ref_setting = approve_flow_setting
                    sds_approve_flow_setting_instance.save()

        # Remove status out of Diagnostic from payload
        validated_data.pop("status", None)
        # Update Diagnostic
        diagnostic_conditions = validated_data.pop("diagnostic_conditions")

        # Update owner name and building name for Diagnostic
        Diagnostic.objects.filter(cms_uuid=instance.cms_uuid).update(
            building_name=validated_data["building_name"],
            construction_owner=validated_data["construction_owner"],
        )

        # Update owner name and building name for ManagementSheet
        ManagementSheet.objects.filter(cms_uuid=instance.cms_uuid).update(
            building_name=validated_data["building_name"],
            client_name=validated_data["construction_owner"],
        )

        # Create Instance for ApproveFlowSetting
        approve_flow_setting_id = validated_data.get("approve_flow_setting_id", None)


        # If instance has approve_flow_setting
        if instance.approve_flow_setting:
            # If current approve_flow_setting difference with new approve_flow_setting
            if (
                approve_flow_setting_id
                and approve_flow_setting_id != instance.approve_flow_setting.id
            ):
                ApproveFlowSettingInstance.objects.filter(diagnostic=instance).update(
                    diagnostic=None
                )
                create_new_approve_flow_setting_instance(approve_flow_setting_id)
        else:
            create_new_approve_flow_setting_instance(approve_flow_setting_id)

        super().update(instance, validated_data)

        # Update Diagnostic Condition
        for diagnostic_condition in diagnostic_conditions:
            diagnostic_condition_id = diagnostic_condition.pop("id")
            diagnostic_condition_item = DiagnosticCondition.objects.get(
                id=diagnostic_condition_id
            )
            images_uuid_list = diagnostic_condition.pop("images_uuid", [])
            diagnostic_condition_img_category = diagnostic_condition.pop(
                "kanna_image_category_uuid", None
            )
            diagnostic_condition_item.is_selected = diagnostic_condition.get(
                "is_selected", False
            )
            diagnostic_condition_item.kanna_image_category_uuid = (
                diagnostic_condition_img_category
            )
            diagnostic_condition_item.save()
            self.create_diagnostic_conditions_photos(
                diagnostic_condition_item, images_uuid_list
            )
            DiagnosticConditionSerialzer().update(
                diagnostic_condition_item, diagnostic_condition
            )

        return GetDiagnosticDetailSerialzer(
            instance,
            context={
                "user": self.context["user"],
                "images_by_uuid": self.context.get("images_by_uuid")
            }
        ).data


class UploadDiagnosticConditionPhotoSerializer(serializers.Serializer):
    # @TODO: Upload multiple file for swagger
    files = serializers.ImageField()


class DiagnosticConditionPhotoResultSerializer(serializers.Serializer):
    result = serializers.BooleanField(default=True)


class DiagnosticRequestApproveSerializer(serializers.Serializer):
    approve_flow_setting_id = serializers.IntegerField()
    expected_approve_date = serializers.DateField()


class DiagnosticApproveFLowSerializer(serializers.Serializer):
    comment = serializers.CharField(required=False, allow_blank=True)
    latest_updated_at = serializers.DateTimeField(required=False, read_only=True)

    def validate(self, attrs):
        diagnostic = self.context["diagnostic"]
        action = self.context["action"]
        if action not in ["approve", "reject", "return"]:
            raise ValidationError({"detail": "Action is invalid"})
        DiagnosticRequestApproveSerializer(
            data={
                "approve_flow_setting_id": diagnostic.approve_flow_setting_id,
                "expected_approve_date": diagnostic.expected_approve_date,
            }
        ).is_valid(raise_exception=True)
        return super().validate(attrs)


# Approve history
class DiagnostiCommonInfoApproveHistorySerializer(serializers.ModelSerializer):
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    document_type = serializers.SerializerMethodField(read_only=True)
    application_date = serializers.SerializerMethodField(read_only=True)
    application_name = serializers.SerializerMethodField(read_only=True)
    owner_name = serializers.SerializerMethodField(read_only=True)
    construction_name = serializers.SerializerMethodField(read_only=True)
    owner_id = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = (
            "approve_flow_setting",
            "person",
            "status",
            "document_type",
            "application_date",
            "application_name",
            "construction_name",
            "cms_uuid",
            "owner_name",
            "owner_id",
        )

    def get_document_type(self, obj):
        return obj.type

    def get_application_date(self, obj):
        return obj.application_date

    def get_application_name(self, obj):

        user_mark = UserElectronicMark.objects.filter(
            diagnostic=obj, user_role_type=UserRoleType.SHOP
        ).first()
        application_name = ""
        if user_mark:
            application_name = user_mark.user.kanna_user.name
        else:
            # if SDS return
            if obj.status == DiagnosticStatus.RETURN:
                approve_flow_step = ApproveFlowStep.objects.filter(
                    diagnostic=obj,
                    status=ApproveFlowStepStatusChoices.APPROVED,
                    company__type=CompanyType.SHOP,
                ).last()
                application_name = approve_flow_step.user.kanna_user.name

        return application_name

    def get_owner_name(self, obj):
        owner_cms = UserConstruction.objects.filter(
            construction_uuid=obj.cms_uuid
        ).first()
        owner_name = ""
        if owner_cms:
            owner = User.objects.filter(id=owner_cms.owner_id).first()
            owner_name = owner.last_name + owner.first_name

        return owner_name

    def get_owner_id(self, obj):
        owner_cms = UserConstruction.objects.filter(
            construction_uuid=obj.cms_uuid
        ).first()
        owner_id = ""
        if owner_cms:
            owner_id = owner_cms.owner_id

        return owner_id

    def get_construction_name(self, obj):
        construction_name = ""
        if "request" in self.context:
            cms = get_cms_kanna(obj.cms_uuid, self.context["request"].user.kanna_user)
            construction_name = cms.get("title")
        return construction_name


class DiagnosticApproveHistorySerializer(serializers.ModelSerializer):
    # approve_flow_setting_instance = ApproveFlowSettingSerializer(read_only=True)
    # company = CompanySerializer(read_only=True)
    user = UserKannaLiteSerializer(read_only=True)
    role_type = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ApproveFlowStep
        exclude = (
            "approve_flow_setting_instance",
            "company",
        )

    def get_role_type(self, obj):
        if obj.user:
            if obj.user.kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                return KANNA_USER_ROLE["admin"]
        return obj.company.type


class DiagnosticRequestFlowApproveSerializer(serializers.ModelSerializer):
    user = UserLiteSerializer(read_only=True)

    class Meta:
        model = ApproveFlowStep
        fields = (
            "created_at",
            "user",
        )


class DiagnostiInfoApproveByConstructionSerializer(serializers.ModelSerializer):
    approve_flow_setting = ApproveFlowSettingSerializer(read_only=True)
    construction_method = ConstructionMethodSerializer(read_only=True)
    user_request_approve = serializers.SerializerMethodField(read_only=True)
    approve_date = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Diagnostic
        fields = (
            "id",
            "user_request_approve",
            "status",
            "approve_flow_setting",
            "construction_method",
            "approve_date",
            "type",
        )
        extra_kwargs = {}

    def get_user_request_approve(self, obj):
        user_request = UserElectronicMark.objects.filter(
            diagnostic=obj, user_role_type=UserRoleType.SHOP
        ).first()
        approve_flow_steps = ""

        if user_request:

            approve_flow_steps = ApproveFlowStep.objects.filter(
                diagnostic=obj,
                status=ApproveFlowStepStatusChoices.APPROVED,
                user=user_request.user,
            ).first()

        # if SDS return
        if obj.status == DiagnosticStatus.RETURN:
            approve_flow_steps = ApproveFlowStep.objects.filter(
                diagnostic=obj,
                status=ApproveFlowStepStatusChoices.APPROVED,
                company__type=CompanyType.SHOP,
            ).last()

        serializer = DiagnosticRequestFlowApproveSerializer(approve_flow_steps)

        return serializer.data

    def get_approve_date(self, obj):

        approve_date = (
            ApproveFlowStep.objects.values_list("updated_at", flat=True)
            .filter(
                Q(diagnostic__id=obj.id),
                ~Q(status=ApproveFlowStepStatusChoices.OPEN),
            )
            .first()
        )

        return approve_date


class HandleACTUpdateAfterApproveSerializer(serializers.Serializer):
    diagnostic = DiagnosticSerializer(write_only=True)
    comment = serializers.CharField(required=False, allow_blank=True)


# Save temp
class DiagnosticTemporaryUpdateSerializer(serializers.ModelSerializer):
    diagnostic_conditions = serializers.ListField(
        child=DiagnosticConditionSerialzer(), write_only=True, required=False, allow_null=True
    )
    cms_uuid = serializers.UUIDField()
    person = serializers.DictField(read_only=True)
    company = serializers.DictField(read_only=True)
    person_id = serializers.IntegerField()
    company_id = serializers.IntegerField()
    approve_flow_setting_id = serializers.IntegerField(required=False, allow_null=True)
    latest_updated_at = serializers.DateTimeField(required=False, read_only=True)

    estimations = DiagnosticEstimationsSerializer(required=False, many=True)

    class Meta:
        model = Diagnostic
        fields = "__all__"
        extra_fields = ["person_id", "company_id", "approve_flow_setting_id"]
        extra_kwargs = {
            "status": {"read_only": True},
        }

    def update(self, instance, validated_data):
        # Remove status, diagnostic_conditions, estimations out of Diagnostic from payload
        validated_data.pop("status", None)
        diagnostic_conditions = validated_data.pop("diagnostic_conditions", [])
        diagnostic_construction_methods = validated_data.pop("diagnostic_construction_methods", [])

        approve_flow_setting_id = validated_data.get("approve_flow_setting_id", None)

        # Update or create approve flow setting instance
        if approve_flow_setting_id:
            try:
                approve_flow_setting = ApproveFlowSetting.objects.get(
                    id=approve_flow_setting_id
                )
                
                ApproveFlowSettingInstance.objects.update_or_create(
                    diagnostic=instance,
                    defaults={
                        "name": approve_flow_setting.name,
                        "type": approve_flow_setting.type,
                        "shop": approve_flow_setting.shop,
                        "dealer": approve_flow_setting.dealer,
                        "act": approve_flow_setting.act,
                        "is_approve_level_dealer": approve_flow_setting.is_approve_level_dealer,
                        "is_approve_level_act": approve_flow_setting.is_approve_level_act,
                        "is_signed_by_shop": approve_flow_setting.is_signed_by_shop,
                        "is_signed_by_dealer": approve_flow_setting.is_signed_by_dealer,
                        "is_signed_by_act": approve_flow_setting.is_signed_by_act,
                        "ref_setting": approve_flow_setting,
                    },
                )
            except ApproveFlowSetting.DoesNotExist:
                logger.info("ApproveFlowSetting not found")

        # Estimate diagnostic construction method
        estimations_to_create = []
        estimations_to_update = []

        # Delete diagnostic construction methods, so all estimations and actual construction is deleted by cascade
        diagnostic_construction_methods_delete = DiagnosticConstructionMethod.objects.filter(diagnostic=instance)
        diagnostic_construction_methods_delete.delete()

        # Update or create diagnostic_construction_method
        for diagnostic_construction_method in diagnostic_construction_methods:
            construction_method_instance = ConstructionMethod.objects.filter(
                id=diagnostic_construction_method.get("construction_method", None)).first()
            obj, created = DiagnosticConstructionMethod.objects.update_or_create(
                id=diagnostic_construction_method.get("id", None),
                defaults={
                    "painting_area": diagnostic_construction_method.get("painting_area"),
                    "construction_method": construction_method_instance,
                    "note": diagnostic_construction_method.get("note"),
                    "diagnostic": instance
                }
            )

            # Add estimation in list create when does not exist in db and
            # Add estimation in list update when get estimation successfully
            estimations = diagnostic_construction_method.get('estimations')
            for estimation in estimations:
                record = DiagnosticConstructionMethodEstimation(**estimation, diagnostic_construction_method=obj)
                try:
                    current = DiagnosticConstructionMethodEstimation.objects.get(id=estimation["id"])
                    record.id = current.id
                    estimations_to_update.append(record)
                except DiagnosticConstructionMethodEstimation.DoesNotExist:
                    estimations_to_create.append(record)

        # Bulk update and create estimations
        DiagnosticConstructionMethodEstimation.objects.bulk_update(
            estimations_to_update,
            fields=[
                "construction_method_type",
                "number_of_paint_cans",
                "number_of_paint_to_use",
                "diagnostic_construction_method",
            ],
            batch_size=1000,
        )
        DiagnosticConstructionMethodEstimation.objects.bulk_create(
            estimations_to_create, ignore_conflicts=True, batch_size=1000
        )

        # Update Diagnostic Condition
        for diagnostic_condition in diagnostic_conditions:
            diagnostic_condition_id = diagnostic_condition.pop("id")
            diagnostic_condition_item = DiagnosticCondition.objects.get(
                id=diagnostic_condition_id
            )

            diagnostic_condition_item.is_selected = diagnostic_condition.get(
                "is_selected", False
            )
            diagnostic_condition_item.kanna_image_category_uuid = diagnostic_condition.pop(
                "kanna_image_category_uuid", None
            )

            images_uuid_set = set(diagnostic_condition.pop("images_uuid", []))

            image_exists_set = set(
                DiagnosticConditionPhoto.objects.filter(
                    diagnostic_condition=diagnostic_condition_item,
                    diagnostic_condition_item=None,
                )
                .annotate(str_id=Cast("photo_uuid", TextField()))
                .values_list("str_id", flat=True)
            )

            images_delete = image_exists_set - images_uuid_set
            images_add = images_uuid_set - image_exists_set

            # bulk delete
            DiagnosticConditionPhoto.objects.filter(
                diagnostic_condition=diagnostic_condition_item,
                diagnostic_condition_item=None,
                photo_uuid__in=images_delete,
            ).delete()

            # bulk create
            diagnostic_condition_photo_create = []
            for image in images_add:
                instance_diagnostic_condition = DiagnosticConditionPhoto(
                    photo_uuid=image,
                    diagnostic_condition=diagnostic_condition_item,
                )
                diagnostic_condition_photo_create.append(instance_diagnostic_condition)
            DiagnosticConditionPhoto.objects.bulk_create(
                diagnostic_condition_photo_create
            )

            diagnostic_condition_item.save()
            DiagnosticConditionSerialzer().update(
                diagnostic_condition_item, diagnostic_condition
            )
        super().update(instance, validated_data)
        return GetDiagnosticDetailSerialzer(
            instance,
            context={
                "user": self.context["user"],
            }
        ).data


class NumberConstructionMethodSerializer(serializers.Serializer):
    num_cm_add = serializers.IntegerField()
