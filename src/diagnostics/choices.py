from django.db import models
from django.utils.translation import gettext_lazy as _


class DiagnosticTypes(models.TextChoices):
    ROOF = "ROOF", _("Roof Diagnostic")
    OUTER_WALL = "OUTER_WALL", _("Outer Wall Diagnostic")


class DiagnosticStatus(models.TextChoices):
    TEMP = "TEMP", _("Temporary")
    WAITING_APPROVAL = "WAITING_APPROVAL", _("Waiting for approval")
    APPROVED = "APPROVED", _("Approval")
    REJECT = "REJECT", _("Reject")
    RETURN = "RETURN", _("Return")
    UPDATE_AFTER_APPROVE = "UPDATE_AFTER_APPROVE", _("Update after approve")


class BuildingCompletionYearTypeChoices(models.TextChoices):
    SHOWA = "SHOWA", _("Showa")
    HEISEI = "HEISEI", _("Heisei")


class MonthChoice(models.IntegerChoices):
    JAN = 1, _("January")
    FEB = 2, _("February")
    MAR = 3, _("March")
    APR = 4, _("April")
    MAY = 5, _("May")
    JUN = 6, _("June")
    JUL = 7, _("July")
    AUG = 8, _("August")
    SEP = 9, _("September")
    OCT = 10, _("October")
    NOV = 11, _("November")
    DEC = 12, _("December")


class DiagnosticConditionTypes(models.TextChoices):
    SUBSTRATE = "SUBSTRATE", _("Substrate Condition")
    EXISTING_PAINT_FILM = "EXISTING_PAINT_FILM", _("Existing Paint Film Condition")
    OTHER = "OTHER", _("Other Fault Conditions")
