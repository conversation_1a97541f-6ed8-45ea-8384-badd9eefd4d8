from datetime import datetime
from logging import getLogger

from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import AccessToken

from django.db import transaction
from django.db.models import Q
from django.db.models.functions import Concat

from api.constants import (
    KANNA_USER_ROLE,
    MAX_CONSTRUCTION_METHOD,
    MAX_IMAGE_HEIGHT,
    MAX_IMAGE_WIDTH,
    KannaUserRoleEnum,
)
from approve_flow.models import (
    ApproveFlowStep,
    ApproveFlowStepStatusChoices,
    OwnerElectronicMark,
    UserElectronicMark,
    UserRoleType,
)
from approve_flow.services import render_to_pdf_and_send_to_kanna_diagnostic
from approve_flow.states import (
    ApproveFromACT,
    ApproveFromAdmin,
    Approve<PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON>romAC<PERSON>,
    <PERSON><PERSON><PERSON>rom<PERSON>d<PERSON>,
    Reject<PERSON>rom<PERSON>ealer,
    ReturnFromACT,
    ReturnFromAdmin,
    ReturnfromDealer,
    SubmitFromShop,
)
from authentication.models import User
from cms.services import get_cms_kanna, update_user_assign_cms
from cms_document.models import DocumentDownloadInformation
from companies.models import Company, CompanyType
from dashboard.utils import get_diagnostics_by_user
from kanna_api.cms_image import create_image, delete_image, get_images
from kanna_api.models.user_kanna import UserKanna
from users.serializers import OwnerElectronicMarkSerializers
from utilities.helpers.notifications import (
    send_update_after_approve_from_act_notifications,
)
from utilities.image.resize import resize_image
from utilities.json_convert.convert import camel_to_snake_json
from utilities.order_filtering.ordering import Ordering
from utilities.permissions.custom_permission import HasKannaPermission
from utilities.seal.seal import Seal

from .models import (
    Diagnostic,
    DiagnosticConditionItem,
    DiagnosticConditionPhoto,
    DiagnosticConstructionMethod,
    DiagnosticStatus,
)
from .serializers import (
    CreateDiagnosticConstructionMethodsSerializer,
    DiagnosticApproveFLowSerializer,
    DiagnosticApproveHistorySerializer,
    DiagnosticConditionPhotoResultSerializer,
    DiagnosticConditionSerialzer,
    DiagnostiCommonInfoApproveHistorySerializer,
    DiagnosticSerializer,
    DiagnosticTemporaryUpdateSerializer,
    GetDiagnosticDetailSerialzer,
    HandleACTUpdateAfterApproveSerializer,
    ListDiagnosticSerializer,
    NumberConstructionMethodSerializer,
    UploadDiagnosticConditionPhotoSerializer,
    get_cms_data_for_diagnostics,
)

logger = getLogger(__name__)


class DiagnosticViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    http_method_names = ["get", "post", "put", "delete"]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return GetDiagnosticDetailSerialzer
        if self.action in ["create", "update"]:
            return DiagnosticSerializer
        if self.action == "estimate":
            return CreateDiagnosticConstructionMethodsSerializer
        if self.action == "handle_approve_flow":
            return DiagnosticApproveFLowSerializer
        if self.action == "get_approve_history":
            return DiagnosticApproveHistorySerializer
        if self.action == "handle_update_after_approve":
            return HandleACTUpdateAfterApproveSerializer
        if self.action == "signed_owner":
            return OwnerElectronicMarkSerializers
        if self.action == "save_temp":
            return DiagnosticTemporaryUpdateSerializer
        if self.action == "add_construction_method":
            return NumberConstructionMethodSerializer

        return ListDiagnosticSerializer

    def get_queryset(self):
        user = self.request.user
        kanna_user = user.kanna_user
        if kanna_user:
            update_user_assign_cms(kanna_user)
        list_diagnostic_ids = get_diagnostics_by_user(kanna_user)
        queryset = Diagnostic.objects.filter(Q(id__in=(list_diagnostic_ids))).all()
        query_param = self.request.query_params
        # search by shop id
        if "shop_id" in query_param:
            queryset = queryset.filter(Q(company_id=query_param["shop_id"]))

        if "type" in query_param:
            queryset = queryset.filter(Q(type__icontains=query_param["type"]))
        if "status" in query_param:
            queryset = queryset.filter(Q(status__icontains=query_param["status"]))
        if "building_name" in query_param:
            queryset = queryset.filter(
                Q(building_name__icontains=query_param["building_name"])
            )
        if "start_date" in query_param:
            queryset = queryset.filter(Q(diagnosis_date__gte=query_param["start_date"]))
        if "end_date" in query_param:
            queryset = queryset.filter(Q(diagnosis_date__lte=query_param["end_date"]))
        if "user_id" in query_param:
            user_kanna = UserKanna.objects.filter(pk=query_param["user_id"]).first()
            user = User.objects.filter(email=user_kanna.email).first()
            queryset = queryset.filter(Q(person=user.id))
        if "user_name" in query_param:
            users = User.objects.all()
            user_ids = []
            keyword = query_param["user_name"].lower()
            for user in users:
                full_name = user.get_full_name()
                full_name_reverse = f"{user.first_name} {user.last_name}"
                first_name = user.first_name.lower() if user.first_name else ""
                last_name = user.last_name.lower() if user.last_name else ""
                if (
                    keyword in first_name
                    or keyword in last_name
                    or keyword in full_name
                    or keyword in full_name_reverse.lower()
                    or keyword.replace(" ", "") in full_name.lower().replace(" ", "")
                    or keyword.replace(" ", "")
                    in full_name_reverse.lower().replace(" ", "")
                ):
                    user_ids.append(user.id)
            queryset = queryset.filter(Q(person__in=user_ids))
        if "construction_method_id" in query_param:
            queryset = queryset.filter(
                Q(
                    diagnostic_construction_methods__construction_method_id=query_param[
                        "construction_method_id"
                    ]
                )
            )

        construction_uuid = self.request.query_params.get("construction_uuid", None)
        if construction_uuid is not None:
            queryset = queryset.filter(Q(cms_uuid=construction_uuid))

        title = self.request.query_params.get("title", None)
        if title is not None:
            logger.info(f"Get all cms data for {title}")
            self.request.cms_data_map = get_cms_data_for_diagnostics(
                diagnostics=queryset, kanna_user=kanna_user
            )
            cms_uuids = queryset.values_list("cms_uuid", flat=True).distinct()
            id_cms = []
            for cms_uuid in cms_uuids:
                cms = self.request.cms_data_map.get(cms_uuid)
                if not cms:
                    continue
                if cms and title.lower() in cms["title"].lower():
                    id_cms.append(cms_uuid)
            queryset = queryset.filter(Q(cms_uuid__in=id_cms))

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        sort_by_list = {
            "building_name",
            "building_name_furigana",
            "construction_address",
            "construction_address_extend",
            "construction_owner",
            "construction_prefecture",
            "construction_zipcode",
            "diagnosis_date",
            "full_name",
            "type",
            "status",
        }
        params = self.request.query_params.get("sort_by", None)
        if params is not None:
            if params in sort_by_list:
                if params == "full_name":
                    data = list(queryset)
                    param_direction = self.request.query_params.get(
                        "order_by_direction", "DESC"
                    )
                    reverse_sort = param_direction == "DESC"
                    data.sort(key=lambda x: x.person.get_full_name(), reverse=reverse_sort)
                    page = self.paginate_queryset(data)
                    cms_data_map = get_cms_data_for_diagnostics(
                        diagnostics=page, kanna_user=request.user.kanna_user
                    )
                    serializer = self.get_serializer(
                        page,
                        many=True,
                        context={"request": request, "cms_data_map": cms_data_map},
                    )
                    return self.get_paginated_response(serializer.data)

                param_direction = self.request.query_params.get(
                    "order_by_direction", "DESC"
                )
                if param_direction == "DESC":
                    queryset = queryset.order_by(f"-{params}")
                else:
                    queryset = queryset.order_by(params)
                page = self.paginate_queryset(queryset)
                cms_data_map = get_cms_data_for_diagnostics(
                    diagnostics=page, kanna_user=request.user.kanna_user
                )
                serializer = self.get_serializer(
                    page,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )
                return self.get_paginated_response(serializer.data)
            elif params == "cms_title":
                param_direction = self.request.query_params.get(
                    "order_by_direction", "DESC"
                )
                cms_data_map = getattr(self.request, "cms_data_map", None)
                if cms_data_map is None:
                    logger.info("Get all cms data")
                    cms_data_map = get_cms_data_for_diagnostics(
                        diagnostics=queryset, kanna_user=request.user.kanna_user
                    )
                serializer = self.get_serializer(
                    queryset,
                    many=True,
                    context={"request": request, "cms_data_map": cms_data_map},
                )
                result = Ordering.order_filtering(
                    serializer.data,
                    params,
                    param_direction,
                    {"cms_title"},
                    [],
                    False,
                )
                page = self.paginate_queryset(result)
                return self.get_paginated_response(page)

        page = self.paginate_queryset(queryset)
        cms_data_map = get_cms_data_for_diagnostics(
            diagnostics=page, kanna_user=request.user.kanna_user
        )
        serializer = self.get_serializer(
            page,
            many=True,
            context={"request": request, "cms_data_map": cms_data_map},
        )
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        instace = self.get_object()
        serializer = self.get_serializer(
            instace, context={"user": request.user, "request": request}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        data = request.data
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        # Count number of construction method base on cms_uuid
        diagnostics = Diagnostic.objects.filter(cms_uuid=data["cms_uuid"]).exclude(status=DiagnosticStatus.REJECT)
        diagnostic_cm = DiagnosticConstructionMethod.objects.filter(diagnostic__in=diagnostics).count()
        if diagnostic_cm >= MAX_CONSTRUCTION_METHOD:
            raise ValidationError(
                # this construction has maximum construction method
                {"construction": "選択した工事の工法数は4つになっているため、新たな診断書を作成することができません。"}
            )
        data = serializer.create(data)
        return Response(data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        data = request.data

        # Handle the logic of updating a record after it has been deleted by someone else.
        diagnostic = Diagnostic._base_manager.filter(id=kwargs["pk"]).first()
        if diagnostic and diagnostic.is_deleted:
            return Response(
                data={"detail": "すでにデータは削除されています", "is_deleted": diagnostic.is_deleted},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Handle the logic of updating a record after it has been updated by someone else.
        latest_updated_at_str = data.get('latest_updated_at')
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != diagnostic.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        diagnostics = Diagnostic.objects.filter(cms_uuid=diagnostic.cms_uuid).exclude(status=DiagnosticStatus.REJECT)
        count_cm = DiagnosticConstructionMethod.objects.filter(diagnostic__in=diagnostics).count()
        if count_cm > MAX_CONSTRUCTION_METHOD:
            return Response(
                data={
                    "constructions ": "選択した工事の工法数は4つになっているため、更新することができません。"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        instance = self.get_object()
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(instance, data)
        return Response(response_data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/estimate",  # noqa
    )
    def estimate(self, request, *args, **kwargs):
        diagnostic_instance = self.get_object()
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(instance=diagnostic_instance, validated_data=data)
        return Response(response_data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        user = request.user
        kanna_user = user.kanna_user
        if kanna_user.company_id != instance.company_id:
            return Response(
                data={"diagnostic": "cant not deleted"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if instance.status != DiagnosticStatus.TEMP:
            return Response(
                data={
                    "diagnostic": "The diagnotic has been approved so it can't be deleted"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # check if this diagnostic is last in MS then delete MS
        if instance.management_sheet is not None:
            if instance.management_sheet.total_diagnostics() == 1:
                instance.management_sheet.delete()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/approve-history",  # noqa
    )
    def get_approve_history(self, request, *args, **kwargs):
        instance = self.get_object()
        steps = ApproveFlowStep.objects.filter(diagnostic=instance)
        steps_serializer = self.get_serializer(steps, many=True)
        common_serializer = DiagnostiCommonInfoApproveHistorySerializer(
            instance, context={"request": request}
        )
        return Response(
            data={
                "data": {
                    "common": common_serializer.data,
                    "history": steps_serializer.data,
                }
            },
            status=status.HTTP_200_OK,
        )

    @action(
        detail=False,
        methods=["get"],
        url_path="(?P<pk>\d+)/pdf-url",  # noqa
    )
    def get_pdf_url(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            document_info_instance = DocumentDownloadInformation.objects.get(
                diagnostic=instance
            )
            urls = document_info_instance.get_url_by_admin_account(
                request.user, document_type="diagnostic"
            )
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )
        except DocumentDownloadInformation.DoesNotExist:
            urls = {
                "url": "",
                "download_url": "",
            }
            return Response(
                {"data": urls},
                status=status.HTTP_200_OK,
            )

    # assign signed.
    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/signed-document",  # noqa
    )
    def signed(self, request, *args, **kwargs):

        instace = self.get_object()

        access_token_obj = AccessToken(
            request.META.get("HTTP_AUTHORIZATION", "").replace("Bearer ", "")
        )
        user_online_certificate = User.objects.filter(
            id=access_token_obj["user_id"]
        ).first()
        seal = Seal()

        user_kanna = UserKanna.objects.filter(
            email=user_online_certificate.email
        ).first()

        pngImageB64String = "data:image/png;base64,"

        user_signed = user_online_certificate.last_name

        if len(user_signed) > 6:
            user_signed = user_signed[0:6]

        image = seal.draw_seal(user_signed)
        image_base64 = pngImageB64String + image.decode("utf-8")

        # insert signed for sds
        user_role_type = ''
        if user_kanna.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
            user_role_type = UserRoleType.ADMIN
        elif user_kanna.role_id == KANNA_USER_ROLE["ACT"]:
            user_role_type = UserRoleType.ACT
        elif user_kanna.role_id == KANNA_USER_ROLE["DEALER"]:
            user_role_type = UserRoleType.DEALER
        elif user_kanna.role_id == KANNA_USER_ROLE["MAKEUP_SHOP"]:
            user_role_type = UserRoleType.SHOP

        UserElectronicMark.objects.get_or_create(
            user=request.user,
            diagnostic=instace,
            image_base64=image_base64,
            user_role_type=user_role_type,
        )

        return Response(
            data={
                "message": "Create electronic mark success",
            },
            status=200,
        )

    # assign signed.
    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/signed-document-owner",  # noqa
    )
    def signed_owner(self, request, *args, **kwargs):

        instace = self.get_object()
        data = request.data
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        instace.confirm_date = datetime.now()
        instace.save()
        owner_electric_mark = OwnerElectronicMark.objects.filter(diagnostic=instace)

        if owner_electric_mark:
            owner_electric_mark.update(image_base64=data["image_base64"])
        else:
            OwnerElectronicMark.objects.create(
                diagnostic=instace, image_base64=data["image_base64"]
            )
        return Response(
            data={
                "message": "Create electronic mark success",
            },
            status=200,
        )

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/update-after-approve",  # noqa
    )
    def handle_update_after_approve(self, request, *args, **kwargs):
        instance = self.get_object()
        data = request.data
        # Validate body
        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        # Get data
        user = request.user
        kanna_user = user.kanna_user
        company = kanna_user.company
        if kanna_user.role_id in [KannaUserRoleEnum.ADMIN.value, KannaUserRoleEnum.ACT.value]:
            comment = data["comment"]
            # Update body diagnostic
            response_data = DiagnosticSerializer(context={"user": request.user}).update(
                instance, data["diagnostic"]
            )
            # Create new step act update after approve
            approve_flow_setting_instance = (
                instance.diagnostic_approve_flow_setting_instance
            )
            new_approve_flow_step = ApproveFlowStep(
                approve_flow_setting_instance=approve_flow_setting_instance,
                diagnostic=instance,
                user=user,
                status=ApproveFlowStepStatusChoices.UPDATE_AFTER_APPROVE,
                company=company or Company.objects.order_by('?').first(),
                comment=comment,
                is_admin_approved=kanna_user.role_id == KannaUserRoleEnum.ADMIN.value
            )
            new_approve_flow_step.save()
            # Update Status
            instance.status = DiagnosticStatus.UPDATE_AFTER_APPROVE
            instance.save()

            # Upload PDF
            render_to_pdf_and_send_to_kanna_diagnostic(
                diagnostic=instance,
                kanna_user=kanna_user,
                cms_uuid=instance.cms_uuid,
            )

            # send notifications
            send_update_after_approve_from_act_notifications(
                target_object=instance,
                approve_flow_setting_instance=approve_flow_setting_instance,
                kanna_user=kanna_user,
            )
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            raise PermissionDenied({"detail": "Invalid action"})

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/(?P<action>\w+)",  # noqa
    )
    @transaction.atomic()
    def handle_approve_flow(self, request, action, *args, **kwargs):
        # Validate request
        diagnostic_instance = self.get_object()
        data = request.data
        serializer = self.get_serializer(
            data=data, context={"diagnostic": diagnostic_instance, "action": action}
        )
        serializer.is_valid(raise_exception=True)

        # Handle the logic of updating a record after it has been updated by someone else.
        latest_updated_at_str = data.get("latest_updated_at")
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != diagnostic_instance.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Get data
        user = request.user
        kanna_user = user.kanna_user
        company = kanna_user.company
        comment = data["comment"]
        if hasattr(diagnostic_instance, "diagnostic_approve_flow_setting_instance"):
            approve_flow_setting_instance = (
                diagnostic_instance.diagnostic_approve_flow_setting_instance
            )
        else:
            return Response(
                {"detail": "Something went wrong in approve flow"}, status=400
            )

        # If Diagnostic has stt is APPROVED, RETURN or REJECT
        # return 400 error
        if diagnostic_instance.status in [
            DiagnosticStatus.REJECT,
            DiagnosticStatus.APPROVED,
            DiagnosticStatus.UPDATE_AFTER_APPROVE,
        ]:
            return Response(
                # Message mean: This diagnostic already approved, returned or rejected
                {"detail": ["この診断書は既に承認されました。", "この診断書は既に返却されました。"]},
                status=400,
            )

        # Handle action approve
        if action == "approve":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                approve_state = ApproveFromAdmin
            elif company.type == CompanyType.SHOP:
                approve_state = SubmitFromShop
            elif company.type == CompanyType.DEALER:
                approve_state = ApproveFromDealer
            elif company.type == CompanyType.ACT:
                approve_state = ApproveFromACT
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logger.info(f"Approved {approve_state} from user {kanna_user} in company {company}")

            state = approve_state(
                kanna_user=kanna_user,
                diagnostic_instance=diagnostic_instance,
                approve_flow_setting_instance=approve_flow_setting_instance,
                comment=comment,
            )
            diagnostic_instance.state = state
            diagnostic_instance.handle_approve()

            return Response({"success": True}, status=200)

        # Handle action reject
        if action == "reject":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                state = RejectFromAdmin(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.DEALER:
                state = RejectFromDealer(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.ACT:
                state = RejectFromACT(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            diagnostic_instance.state = state
            diagnostic_instance.handle_reject()
            return Response({"success": True}, status=200)

        # Handle action return
        if action == "return":
            if kanna_user.role_id == KANNA_USER_ROLE["ADMIN_SYSTEM"]:
                state = ReturnFromAdmin(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.DEALER:
                state = ReturnfromDealer(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            elif company.type == CompanyType.ACT:
                state = ReturnFromACT(
                    kanna_user=kanna_user,
                    diagnostic_instance=diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    comment=comment,
                )
            else:
                return Response(
                    {"detail": "Incorrect company type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            diagnostic_instance.state = state
            diagnostic_instance.handle_return()
            return Response({"success": True}, status=200)

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/save-temp",
    )
    def save_temp(self, request, *args, **kwargs):
        data = request.data
        diagnostic = Diagnostic._base_manager.filter(id=kwargs["pk"]).first()
        if diagnostic:
            if diagnostic.is_deleted:
                return Response(
                    data={"detail": "すでにデータは削除されています", "is_deleted": diagnostic.is_deleted},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                data={"detail": "データは存在していません。"},
                status=status.HTTP_404_NOT_FOUND,
            )
        # Handle the logic of updating a record after it has been deleted by someone else or not exits.
        latest_updated_at_str = data.get('latest_updated_at')
        if latest_updated_at_str:
            try:
                latest_updated_at = datetime.fromisoformat(latest_updated_at_str)
                if latest_updated_at != diagnostic.updated_at:
                    return Response(
                        data={"detail": "すでにデータは更新されています", "is_updated": True},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception:
                return Response(
                    data={"detail": "Invalid datetime format for latest_updated_at"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        serializer = self.get_serializer(data=data, context={"user": request.user})
        serializer.is_valid(raise_exception=True)
        response_data = serializer.update(diagnostic, data)

        return Response(response_data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        url_path="(?P<pk>\d+)/add-construction-method",
    )
    def add_construction_method(self, request, *args, **kwargs):
        diagnostic = Diagnostic.objects.filter(id=kwargs["pk"]).first()
        diagnostics = Diagnostic.objects.filter(cms_uuid=diagnostic.cms_uuid).exclude(Q(id=diagnostic.id) |
                                                                                      Q(status=DiagnosticStatus.REJECT))
        num_cm_curr = DiagnosticConstructionMethod.objects.filter(diagnostic__in=diagnostics).count()

        num_cm_add = request.data.get("num_cm_add")
        if num_cm_curr + num_cm_add > MAX_CONSTRUCTION_METHOD:
            return Response(
                data={
                    "message": "工法はすでに4つ揃っています。"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            data={
                "success": True,
            },
            status=status.HTTP_200_OK,
        )


class DiagnosticConditionsPhotoAPIView(generics.CreateAPIView):
    permission_classes = [HasKannaPermission]
    parser_classes = (MultiPartParser,)
    serializer_class = UploadDiagnosticConditionPhotoSerializer

    @action(detail=False, methods=["post"], parser_classes=(MultiPartParser,))
    @swagger_auto_schema(
        request_body=UploadDiagnosticConditionPhotoSerializer(),
        responses={200: DiagnosticConditionPhotoResultSerializer},
    )
    def post(self, request, pk, image_categories_uuid, *args, **kwargs):
        kanna_user = UserKanna.objects.get(email=request.user.email)
        diagnostic = Diagnostic.objects.get(id=pk)

        # Get Diagnostic condition and Diagnostic condition item
        diagnostic_condition = diagnostic.diagnostic_conditions.filter(
            kanna_image_category_uuid=image_categories_uuid
        ).first()
        diagnostic_condition_item = DiagnosticConditionItem.objects.filter(
            kanna_image_category_uuid=image_categories_uuid
        ).first()

        # Create image in Kanna and Photo record in DB
        for file in request.FILES.getlist("files", default=[]):
            img_data = resize_image(file.file, MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT)
            files = {"file": (file.name, img_data, file.content_type)}
            kanna_image = create_image(
                kanna_user,
                diagnostic.cms_uuid,
                image_categories_uuid,
                files,
            )
            DiagnosticConditionPhoto.objects.create(
                photo_uuid=kanna_image["image"]["uuid"],
                diagnostic_condition=diagnostic_condition,
                diagnostic_condition_item=diagnostic_condition_item,
            )

        images_list = get_images(
            kanna_user,
            cms_uuid=diagnostic.cms_uuid,
            image_category_uuid=image_categories_uuid,
        )
        images_list_data = camel_to_snake_json(images_list)
        return Response({"data": images_list_data}, status=status.HTTP_200_OK)


class GetDetailConditionAPIView(generics.GenericAPIView):
    permission_classes = [HasKannaPermission]
    serializer_class = DiagnosticConditionSerialzer

    def get(self, request, pk, condition_id, *args, **kwargs):
        diagnostic = Diagnostic.objects.get(id=pk)
        diagnostic_condition = diagnostic.diagnostic_conditions.get(id=condition_id)
        serializer = self.get_serializer(
            diagnostic_condition, context={"user": request.user}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)


class DeleteDiagnosticConditionPhotoAPIView(generics.DestroyAPIView):
    queryset = DiagnosticConditionPhoto.objects.all()
    permission_classes = [HasKannaPermission]
    parser_classes = (MultiPartParser,)
    serializer_class = DiagnosticConditionPhotoResultSerializer

    def delete(self, request, pk, image_categories_uuid, photo_uuid, *args, **kwargs):
        kanna_user = UserKanna.objects.get(email=request.user.email)
        diagnostic = Diagnostic.objects.get(id=pk)
        diagnostic_condition_photo = DiagnosticConditionPhoto.objects.filter(
            photo_uuid=photo_uuid
        ).first()
        if diagnostic_condition_photo:
            kanna_response = delete_image(
                kanna_user,
                diagnostic.cms_uuid,
                image_categories_uuid,
                photo_uuid,
            )
            kanna_response.status_code == 204 and diagnostic_condition_photo.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)
