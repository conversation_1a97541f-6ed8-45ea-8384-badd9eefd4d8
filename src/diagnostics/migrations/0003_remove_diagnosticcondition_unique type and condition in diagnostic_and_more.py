# Generated by Django 4.0.6 on 2022-09-16 10:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('construction_methods', '0003_alter_constructionmethod_complete'),
        ('companies', '0004_alter_company_options'),
        ('diagnostics', '0002_alter_diagnostic_options'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='diagnosticcondition',
            name='unique type and condition in diagnostic',
        ),
        migrations.RemoveField(
            model_name='diagnostic',
            name='building_completion_date',
        ),
        migrations.RemoveField(
            model_name='diagnostic',
            name='end_date',
        ),
        migrations.RemoveField(
            model_name='diagnostic',
            name='name',
        ),
        migrations.RemoveField(
            model_name='diagnostic',
            name='shop',
        ),
        migrations.RemoveField(
            model_name='diagnostic',
            name='start_date',
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='application_date',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='building_age',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='building_completion_month',
            field=models.IntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], default=1),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='building_completion_year',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='building_completion_year_type',
            field=models.CharField(choices=[('SHOWA', 'Showa'), ('HEISEI', 'Heisei')], default='HEISEI', max_length=16),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='building_name_furigana',
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='cms_id',
            field=models.UUIDField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='company',
            field=models.ForeignKey(default=0, on_delete=django.db.models.deletion.DO_NOTHING, to='companies.company'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='confirm_date',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='construction_method',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='construction_methods.constructionmethod'),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='diagnosis_date',
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='expected_approve_date',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='expected_comment',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='diagnostic',
            name='phone_number',
            field=models.CharField(max_length=64, null=True),
        ),
        migrations.AddField(
            model_name='diagnosticcondition',
            name='photo',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='diagnostic',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('WAITING_APPROVAL', 'Waiting for approval'), ('APPROVED', 'Approval'), ('RETURN', 'Return')], default='PENDING', max_length=16),
        ),
        migrations.AlterField(
            model_name='diagnosticcondition',
            name='diagnostic',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diagnostic_conditions', to='diagnostics.diagnostic'),
        ),
        migrations.AlterField(
            model_name='diagnosticcondition',
            name='ground_condition',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddConstraint(
            model_name='diagnosticcondition',
            constraint=models.UniqueConstraint(fields=('type', 'sub_type', 'id'), name='unique type and condition in diagnostic'),
        ),
    ]
