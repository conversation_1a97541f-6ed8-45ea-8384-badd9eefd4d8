# Generated by Django 4.1 on 2022-09-07 08:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("companies", "0001_initial"),
        ("authentication", "0007_user_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="Diagnostic",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("ROOF", "Roof Diagnostic"),
                            ("OUTER_WALL", "Outer Wall Diagnostic"),
                        ],
                        max_length=16,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("APPROVED", "Approval"), ("PENDING", "Pending")],
                        default="PENDING",
                        max_length=16,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("construction_zipcode", models.CharField(max_length=16)),
                (
                    "construction_prefecture",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                (
                    "construction_address",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                (
                    "construction_address_extend",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                ("construction_owner", models.CharField(max_length=64)),
                (
                    "building_name",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                ("building_completion_date", models.DateField(blank=True, null=True)),
                ("building_renovation_history", models.BooleanField(default=True)),
                (
                    "buinding_structure",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                ("building_area", models.IntegerField(blank=True, default=0)),
                ("building_painted_area", models.IntegerField(blank=True, default=0)),
                (
                    "person",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="companies.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DiagnosticCondition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("SUBSTRATE", "Substrate Condition"),
                            ("EXISTING_PAINT_FILM", "Existing Paint Film Condition"),
                            ("OTHER", "Other Fault Conditions"),
                        ],
                        max_length=32,
                    ),
                ),
                ("sub_type", models.CharField(max_length=32)),
                ("ground_condition", models.JSONField(max_length=32)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "diagnostic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="diagnostics.diagnostic",
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="diagnosticcondition",
            constraint=models.UniqueConstraint(
                fields=("type", "sub_type"),
                name="unique type and condition in diagnostic",
            ),
        ),
    ]
