# Generated by Django 4.1 on 2022-09-20 10:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        (
            "diagnostics",
            "0003_remove_diagnosticcondition_unique type and condition in diagnostic_and_more",
        ),
    ]

    operations = [
        migrations.RemoveField(
            model_name="diagnosticcondition",
            name="photo",
        ),
        migrations.AddField(
            model_name="diagnostic",
            name="kanna_image_category_uuid",
            field=models.UUIDField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="diagnosticcondition",
            name="kanna_image_category_uuid",
            field=models.UUIDField(null=True),
        ),
        migrations.CreateModel(
            name="DiagnosticConditionPhoto",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("photo_uuid", models.UUIDField(blank=True, null=True)),
                (
                    "diagnostic_condition",
                    models.Foreign<PERSON>ey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="photos",
                        to="diagnostics.diagnosticcondition",
                    ),
                ),
            ],
        ),
    ]
