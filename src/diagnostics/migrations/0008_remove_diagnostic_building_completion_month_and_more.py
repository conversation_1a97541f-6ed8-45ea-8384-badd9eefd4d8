# Generated by Django 4.1 on 2022-09-29 10:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("diagnostics", "0007_diagnosticconditionitem_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="diagnostic",
            name="building_completion_month",
        ),
        migrations.RemoveField(
            model_name="diagnostic",
            name="building_completion_year",
        ),
        migrations.RemoveField(
            model_name="diagnostic",
            name="building_completion_year_type",
        ),
        migrations.AddField(
            model_name="diagnostic",
            name="building_completion_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="DiagnosticConstructionMethodEstimation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "construction_method_type",
                    models.CharField(
                        choices=[
                            ("PAINT_BOTTOM_1", "Paint Bottom 1"),
                            ("PAINT_BOTTOM_2", "Paint Bottom 2"),
                            ("PAINT_MIDDLE", "Paint Middle"),
                            ("PAINT_OVER_1", "Paint Over 1"),
                            ("PAINT_OVER_2", "Paint Over 2"),
                        ],
                        max_length=255,
                    ),
                ),
                ("number_of_paint_cans", models.IntegerField(blank=True, null=True)),
                ("number_of_paint_to_use", models.IntegerField(blank=True, null=True)),
                (
                    "diagnostic",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="estimations",
                        to="diagnostics.diagnostic",
                    ),
                ),
            ],
            options={
                "db_table": "diagnostic_construction_method_estimation",
            },
        ),
    ]
