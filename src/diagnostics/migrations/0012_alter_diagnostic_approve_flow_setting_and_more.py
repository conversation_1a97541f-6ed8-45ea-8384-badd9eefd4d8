# Generated by Django 4.1 on 2022-10-11 09:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("approve_flow_settings", "0003_approveflowsetting_deleted_at_and_more"),
        ("diagnostics", "0011_remove_diagnostic_expected_comment"),
    ]

    operations = [
        migrations.AlterField(
            model_name="diagnostic",
            name="approve_flow_setting",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="diagnostic_approved_flow_setting",
                to="approve_flow_settings.approveflowsetting",
            ),
        ),
        migrations.AlterField(
            model_name="diagnostic",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="diagnosticcondition",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="diagnosticconditionitem",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="diagnosticconditionphoto",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="diagnosticconstructionmethodestimation",
            name="diagnostic",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="estimations",
                to="diagnostics.diagnostic",
            ),
        ),
    ]
