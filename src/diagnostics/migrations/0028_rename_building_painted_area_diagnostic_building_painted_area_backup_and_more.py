# Generated by Django 4.1.3 on 2025-02-10 08:24

import django.utils.timezone
from django.db import migrations, models


def create_diagnostic_construction_methods(apps, schema_editor):
    # Get model
    Diagnostic = apps.get_model('diagnostics', 'Diagnostic')
    DiagnosticConstructionMethod = apps.get_model('diagnostics', 'DiagnosticConstructionMethod')
    DiagnosticConstructionMethodEstimation = apps.get_model('diagnostics',
                                                            'DiagnosticConstructionMethodEstimation')

    diagnostics = Diagnostic.objects.all()
    diagnostic_construction_method_create = []
    diagnostic_construction_method_estimation_update = []

    for diagnostic in diagnostics:
        # Backup construction method id
        diagnostic.construction_method_backup_id = diagnostic.construction_method_id
        diagnostic.save()

        diagnostic_construction_method = DiagnosticConstructionMethod(
            diagnostic=diagnostic,
            construction_method=diagnostic.construction_method,
            painting_area=diagnostic.building_painted_area,
            note=diagnostic.note,
        )
        diagnostic_construction_method_create.append(diagnostic_construction_method)
        diagnostic_construction_method_estimations = DiagnosticConstructionMethodEstimation.objects.filter(
            diagnostic=diagnostic
        )
        if diagnostic_construction_method_estimations:
            for estimation in diagnostic_construction_method_estimations:
                estimation.diagnostic_construction_method = diagnostic_construction_method

                # Backup construction method id
                estimation.diagnostic_backup_id = estimation.diagnostic_id
                diagnostic_construction_method_estimation_update.append(estimation)

    DiagnosticConstructionMethod.objects.bulk_create(diagnostic_construction_method_create)
    if diagnostic_construction_method_estimation_update:
        DiagnosticConstructionMethodEstimation.objects.bulk_update(diagnostic_construction_method_estimation_update,
                                                                   fields=['diagnostic_construction_method',
                                                                           'diagnostic_backup_id'])


class Migration(migrations.Migration):
    dependencies = [
        ('construction_methods', '0030_alter_constructionmethod_complete'),
        ('diagnostics', '0027_diagnosticcondition_is_selected'),
    ]

    operations = [
        migrations.AddField(
            model_name='diagnostic',
            name='construction_method_backup_id',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='diagnosticconstructionmethodestimation',
            name='diagnostic_backup_id',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.CreateModel(
            name='DiagnosticConstructionMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('painting_area', models.IntegerField(blank=True, null=True)),
                ('note', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('construction_method', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE,
                                                          to='construction_methods.constructionmethod')),
                ('diagnostic', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE,
                                                 related_name='diagnostic_construction_methods',
                                                 to='diagnostics.diagnostic')),
            ],
            options={
                'db_table': 'diagnostic_construction_method',
                'ordering': ['id'],
            },
        ),
        migrations.AddField(
            model_name='diagnosticconstructionmethodestimation',
            name='diagnostic_construction_method',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='estimations',
                                    to='diagnostics.diagnosticconstructionmethod'),
        ),
        migrations.RunPython(create_diagnostic_construction_methods),
        migrations.RemoveField(
            model_name='diagnostic',
            name='construction_method',
        ),
        migrations.RemoveField(
            model_name='diagnosticconstructionmethodestimation',
            name='diagnostic',
        ),
        migrations.RenameField(
            model_name='diagnostic',
            old_name='building_painted_area',
            new_name='building_painted_area_backup',
        ),
        migrations.RenameField(
            model_name='diagnostic',
            old_name='note',
            new_name='note_backup',
        ),
        migrations.AlterField(
            model_name='diagnosticconstructionmethod',
            name='painting_area',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=20, null=True),
        ),
    ]
