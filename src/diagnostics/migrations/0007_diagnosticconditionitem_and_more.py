# Generated by Django 4.1 on 2022-09-28 07:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("diagnostics", "0006_alter_diagnostic_table_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DiagnosticConditionItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(null=True)),
                ("title", models.Char<PERSON>ield(max_length=128, null=True)),
                ("body", models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ("kanna_image_category_uuid", models.UUIDField(null=True)),
            ],
            options={
                "db_table": "diagnostic_condition_item",
            },
        ),
        migrations.RemoveConstraint(
            model_name="diagnosticcondition",
            name="unique type and condition in diagnostic",
        ),
        migrations.RenameField(
            model_name="diagnostic",
            old_name="cms_id",
            new_name="cms_uuid",
        ),
        migrations.RenameField(
            model_name="diagnosticcondition",
            old_name="sub_type",
            new_name="title",
        ),
        migrations.RemoveField(
            model_name="diagnosticcondition",
            name="ground_condition",
        ),
        migrations.AddField(
            model_name="diagnostic",
            name="company_fax",
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name="diagnostic",
            name="company_phone",
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name="diagnosticconditionitem",
            name="diagnostic_condition",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="diagnostics.diagnosticcondition",
            ),
        ),
        migrations.AddField(
            model_name="diagnosticconditionphoto",
            name="diagnostic_condition_item",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="photos",
                to="diagnostics.diagnosticconditionitem",
            ),
        ),
    ]
