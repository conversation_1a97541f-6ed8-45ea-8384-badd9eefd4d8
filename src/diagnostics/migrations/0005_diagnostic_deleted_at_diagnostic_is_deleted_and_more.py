# Generated by Django 4.1 on 2022-09-21 06:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("diagnostics", "0004_remove_diagnosticcondition_photo_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="diagnostic",
            name="deleted_at",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="diagnostic",
            name="is_deleted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="diagnosticcondition",
            name="deleted_at",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="diagnosticcondition",
            name="is_deleted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="diagnosticconditionphoto",
            name="deleted_at",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="diagnosticconditionphoto",
            name="is_deleted",
            field=models.<PERSON><PERSON><PERSON><PERSON>ield(default=False),
        ),
        migrations.AlterField(
            model_name="diagnostic",
            name="status",
            field=models.CharField(
                choices=[
                    ("TEMP", "Temporary"),
                    ("WAITING_APPROVAL", "Waiting for approval"),
                    ("APPROVED", "Approval"),
                    ("REJECT", "Reject"),
                    ("RETURN", "Return"),
                ],
                default="TEMP",
                max_length=16,
            ),
        ),
    ]
