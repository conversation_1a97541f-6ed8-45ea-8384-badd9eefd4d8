from rest_framework.routers import SimpleRouter

from django.urls import include, path
from django.urls import re_path as url

from .views import (
    DeleteDiagnosticConditionPhotoAPIView,
    DiagnosticConditionsPhotoAPIView,
    DiagnosticViewSet,
    GetDetailConditionAPIView,
)

router = SimpleRouter()
router.register(r"", DiagnosticViewSet, "diagnostic")


urlpatterns = [
    url(r"^", include(router.urls)),
    path(
        "<int:pk>/conditions/<condition_id>/",
        GetDetailConditionAPIView.as_view(),
        name="diagnostic-condition-detail",
    ),
    # SDS Image: Upload image to KANNA
    path(
        "<int:pk>/image_categories/<uuid:image_categories_uuid>/image/",
        DiagnosticConditionsPhotoAPIView.as_view(),
        name="upload-image",
    ),
    path(
        "<int:pk>/image_categories/<uuid:image_categories_uuid>/image/<uuid:photo_uuid>/",
        DeleteDiagnosticConditionPhotoAPIView.as_view(),
        name="delete-image",
    ),
]
