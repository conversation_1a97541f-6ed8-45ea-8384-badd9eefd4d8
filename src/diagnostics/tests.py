import json

from rest_framework.test import APITestCase

from django.urls import reverse

from authentication.factories import UserFactory
from companies.factories import CompanyFactory
from prefectures.factories import PrefecturesFactory

from .factories import DiagnosticFactory
from .models import DiagnosticStatus, DiagnosticTypes


# @TODO: Unit test for Create and Update Diagnostic
class DiagnosticAPITests(APITestCase):
    def test_get_diagnostic_list(self):
        # Create User
        user = UserFactory(email="<EMAIL>", password="123456")
        # Create Company
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id, name="Company Shop")
        # Create 3 Diagnostics
        DiagnosticFactory(
            person=user, company=company, status=DiagnosticStatus.APPROVED.value
        )
        DiagnosticFactory(person=user, company=company, type=DiagnosticTypes.ROOF.value)
        DiagnosticFactory(person=user, company=company)
        # Get list
        url = reverse("diagnostic-list", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(len(data["results"]), 3)

        # Search by status
        response = self.client.get(url + "?status=" + DiagnosticStatus.APPROVED.value)
        data = response.json()
        self.assertEqual(len(data["results"]), 1)

        response = self.client.get(url + "?status=" + DiagnosticStatus.TEMP.value)
        data = response.json()
        self.assertEqual(len(data["results"]), 2)

        # Search by type
        response = self.client.get(url + "?type=" + DiagnosticTypes.ROOF.value)
        data = response.json()
        self.assertEqual(len(data["results"]), 1)

        # Search by PIC
        response = self.client.get(url + f"?user_id={user.pk}")
        data = response.json()
        self.assertEqual(len(data["results"]), 3)

    def test_create_incorect_diagnostic_with_conditions(self):
        url = reverse("diagnostic-list", kwargs={"version": "v1"})
        body = {}
        response = self.client.post(url, body)
        self.assertEqual(response.status_code, 400)

    # TODO : Update unit test for this case
    def test_create_corect_diagnostic_with_conditions(self):
        # Create User
        user = UserFactory(email="<EMAIL>", password="123456")
        # Create Company
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id, name="Company Shop")
        url = reverse("diagnostic-list", kwargs={"version": "v1"})
        body = {
            "type": DiagnosticTypes.ROOF,
            "construction_zipcode": "12345678",
            "construction_owner": "NALS",
            "diagnostic_conditions": [
                {
                    "type": "SUBSTRATE",
                    "sub_type": "new_slate_tiles",
                    "ground_condition": {
                        "is_broken": True,
                        "is_falling": False,
                        "repaint_history": 1,
                        "coating_file_type": "Dummy",
                    },
                    "photo": [],
                },
                {
                    "type": "EXISTING_PAINT_FILM",
                    "sub_type": "metal_parts",
                    "ground_condition": {
                        "is_paint_pelling": True,
                        "other_deterioration": True,
                        "is_cracked_paint_layer": True,
                    },
                },
                {
                    "type": "OTHER",
                    "sub_type": "silicone_material",
                    "ground_condition": {},
                },
            ],
            "person_id": user.id,
            "company_id": company.id,
        }
        response = self.client.post(
            url, data=json.dumps(body), content_type="application/json"
        )
        data = response.json()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(data["diagnostic_conditions"])
