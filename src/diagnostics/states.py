from abc import ABC


class DiagnosticState(ABC):
    def handle_waiting_for_approval(self):
        pass

    def handle_reject(self):
        pass

    def handle_return(self):
        pass


class WaitingApprove(DiagnosticState):
    def handle_waiting_for_approval(self):
        return super().handle_waiting_for_approval()


class Reject(DiagnosticState):
    def handle_reject(self):
        return super().handle_reject()


class Return(DiagnosticState):
    def handle_return(self):
        return super().handle_return()
