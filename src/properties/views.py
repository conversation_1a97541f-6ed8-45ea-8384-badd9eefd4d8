from rest_framework import viewsets
from rest_framework.response import Response

from django.core.exceptions import ObjectDoesNotExist

from cms.services import get_property_customize
from kanna_api.models.user_kanna import UserKanna
from kanna_api.property import get_properties, get_property
from utilities.exceptions.api_exception import UserWhitelistEmailError
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import HasK<PERSON><PERSON>ermission

from .serializers import PropertyPageSerializer, PropertySerializer


class PropertyViewSet(viewsets.GenericViewSet):
    permission_classes = (HasKannaPermission,)
    queryset = None

    def get_serializer_class(self):
        if self.action == "list":
            return PropertyPageSerializer

        return PropertySerializer

    # @swagger_auto_schema(manual_parameters=PropertyParam.yasg_params())
    def list(self, request, *args, **kwargs):
        """
        物件一覧
        """
        # kanna_apiの物件一覧取得
        try:

            kanna_user = UserKanna.objects.get(email=request.user.email)

            response = get_properties(kanna_user)
            response_data = camel_to_snake_json(response)

            # カスタマイズ項目取得
            for property_data in response_data:
                get_property_customize(property_data)

            # クエリパラメータでフィルター
            serializer = self.get_serializer(
                data={"count": len(response_data), "results": response_data}
            )
            serializer.is_valid(raise_exception=True)

            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    def retrieve(self, request, pk: str, *args, **kwargs):
        """
        物件詳細
        """
        try:
            # kanna_apiの物件詳細取得
            kanna_user = UserKanna.objects.get(email=request.user.email)
            response = get_property(kanna_user, pk)
            response_data = camel_to_snake_json(response)
            property_data = response_data.get("property")

            # カスタマイズ項目取得
            get_property_customize(property_data)

            serializer = self.get_serializer(data=property_data)
            serializer.is_valid(raise_exception=True)

            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError
