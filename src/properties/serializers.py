from rest_framework import serializers
from rest_framework.serializers import <PERSON><PERSON><PERSON><PERSON>, Integer<PERSON>ield, UUIDField


class ImageSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = Char<PERSON>ield()
    image_url = CharField()
    image_thumb_url = CharField()


class CmsPropertySerializer(serializers.Serializer):
    uuid = UUIDField()
    management_id = CharField(allow_blank=True, default="")
    name = Char<PERSON><PERSON>(allow_blank=True, default="")
    postcode = Char<PERSON>ield(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    phone_number = CharField(required=False, allow_blank=True, default=None)


class PropertySerializer(serializers.Serializer):
    uuid = UUIDField()
    management_id = Char<PERSON>ield(allow_blank=True, default="")
    name = Cha<PERSON><PERSON><PERSON>(allow_blank=True, default="")
    room_number = Cha<PERSON><PERSON><PERSON>(allow_blank=True, default="")
    postcode = CharField(allow_blank=True, default="")
    address = Char<PERSON>ield(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    floor_plan_ja = CharField(allow_blank=True, default="")
    exclusive_total_floor_area = IntegerField(allow_null=True, default=None)
    land_area = IntegerField(allow_null=True, default=None)
    building_age = CharField(allow_blank=True, default="")
    floor = IntegerField(allow_null=True, default=None)
    floor_ground_type_ja = CharField(allow_blank=True, default="")
    building_structure_type_ja = CharField(allow_blank=True, default="")
    total_units = IntegerField(allow_null=True, default=None)
    stories_above_ground = IntegerField(allow_null=True, default=None)
    stories_underground = IntegerField(allow_null=True, default=None)
    has_elevator_ja = CharField(allow_blank=True, default="")
    has_autolock_ja = CharField(allow_blank=True, default="")
    images = ImageSerializer(many=True)
    note = CharField(allow_blank=True, default="")
    manager_full_name = CharField(allow_blank=True, default="")
    manager_last_name = CharField(allow_blank=True, default="")
    manager_first_name = CharField(allow_blank=True, default="")
    manager_full_name_kana = CharField(allow_blank=True, default="")
    manager_last_name_kana = CharField(allow_blank=True, default="")
    manager_first_name_kana = CharField(allow_blank=True, default="")
    manager_phone_number = CharField(allow_blank=True, default="")
    manager_email = CharField(allow_blank=True, default="")
    chairman_full_name = CharField(allow_blank=True, default="")
    chairman_last_name = CharField(allow_blank=True, default="")
    chairman_first_name = CharField(allow_blank=True, default="")
    chairman_full_name_kana = CharField(allow_blank=True, default="")
    chairman_last_name_kana = CharField(allow_blank=True, default="")
    chairman_first_name_kana = CharField(allow_blank=True, default="")
    chairman_phone_number = CharField(allow_blank=True, default="")
    chairman_email = CharField(allow_blank=True, default="")
    management_company_name = CharField(allow_blank=True, default="")
    staff_full_name = CharField(allow_blank=True, default="")
    staff_last_name = CharField(allow_blank=True, default="")
    staff_first_name = CharField(allow_blank=True, default="")
    staff_full_name_kana = CharField(allow_blank=True, default="")
    staff_last_name_kana = CharField(allow_blank=True, default="")
    staff_first_name_kana = CharField(allow_blank=True, default="")
    management_company_phone_number = CharField(allow_blank=True, default="")
    management_company_email = CharField(allow_blank=True, default="")
    management_company_fax_number = CharField(allow_blank=True, default="")
    management_system_ja = CharField(allow_blank=True, default="")
    working_style_ja = CharField(allow_blank=True, default="")
    management_company_note = CharField(allow_blank=True, default="")
    code = CharField(allow_blank=True, required=False)
    kind_ja = CharField(allow_blank=True, default="")


class PropertyListSerializer(serializers.Serializer):
    uuid = UUIDField()
    code = CharField(allow_blank=True, default="")
    postcode = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    name = CharField(allow_blank=True, default="")
    room_number = CharField(allow_blank=True, default="")
    kind_ja = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    created_at = CharField()


class PropertyPageSerializer(serializers.Serializer):
    count = IntegerField()
    results = PropertyListSerializer(many=True)
