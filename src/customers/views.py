from rest_framework import viewsets
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response

from django.core.exceptions import ObjectDoesNotExist

from cms.cms import cms_filter, get_cms_list
from cms.services import get_cms_customize
from customers.serializers import ClientListSerializer, ClientSerializer
from kanna_api.choices.business_form_type import BusinessFormType
from kanna_api.client import clients_filter, get_client, get_clients
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.client import ClientParam
from kanna_api.query_params.cms import CmsParam
from utilities.exceptions.api_exception import UserWhitelistEmailError
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import HasKannaPermission


class ClientViewSet(viewsets.GenericViewSet):
    permission_classes = [HasKannaPermission]
    http_method_names = ["get"]
    pagination_class = LimitOffsetPagination

    def get_serializer_class(self):
        if self.action == "retrieve":
            return ClientSerializer
        elif self.action == "list":
            return ClientListSerializer

    def get_queryset(self):
        kanna_user = UserKanna.objects.get(email=self.request.user.email)
        response = get_clients(kanna_user)
        response_data = camel_to_snake_json(response)
        param = self.request.query_params.dict()
        response_data = clients_filter(ClientParam(**param), response_data)
        # @TODO remove this when filter is ready
        response_data = sorted(
            response_data, key=lambda k: k["created_at"], reverse=True
        )
        # @TODO uncomment this when filter is ready
        # sort_by_list = {
        #     "business_form_type",
        #     "prefecture",
        #     "email",
        #     "created_at",
        #     "phone_number",
        #     "name",
        # }
        # params = self.request.query_params.get("sort_by", None)
        # list_combine = ["company_name", "full_name"]
        # params_direction = self.request.query_params.get("order_by_direction", "")
        # response_data = Ordering.order_filtering(
        #     response_data,
        #     params,
        #     params_direction,
        #     sort_by_list,
        #     list_combine,
        #     True,
        # )
        return response_data

    def list(self, request, *args, **kwargs):
        try:
            response_data = self.get_queryset()
            for client in response_data:

                if "business_form_type" not in client:
                    client["business_form_type"] = ""
                    client["business_form_type_ja"] = ""
                    client["name"] = ""
                    client["name_kana"] = ""
                elif (
                    client["business_form_type"]
                    == BusinessFormType.SOLE_PROPRIETORSHIP.name
                ):
                    client["name"] = client.get("full_name", "")
                    client["name_kana"] = client.get("full_name_kana", "")
                elif client["business_form_type"] == BusinessFormType.CORPORATE.name:
                    client["name"] = client.get("company_name", "")
                    client["name_kana"] = client.get("company_name_kana", "")

            page = self.paginate_queryset(response_data)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            serializer = self.get_serializer(response_data, many=True)
            return Response(serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    def retrieve(self, request, pk: str, *args, **kwargs):
        try:
            kanna_user = UserKanna.objects.get(email=request.user.email)
            params = CmsParam(**request.query_params.dict())
            params.client_uuid = pk

            client = get_client(kanna_user, pk).get("client")
            client = camel_to_snake_json(client)

            if "business_form_type" not in client:
                client["business_form_type"] = ""
                client["business_form_type_ja"] = ""
                client["name"] = ""
                client["name_kana"] = ""
            elif (
                client["business_form_type"]
                == BusinessFormType.SOLE_PROPRIETORSHIP.name
            ):
                client["name"] = client.get("full_name", "")
                client["name_kana"] = client.get("full_name_kana", "")
            elif client["business_form_type"] == BusinessFormType.CORPORATE.name:
                client["name"] = client.get("company_name", "")
                client["name_kana"] = client.get("company_name_kana", "")

            cms_list = get_cms_list(kanna_user, cms_user_uuid=kanna_user.user_uuid)
            cms_list = camel_to_snake_json(cms_list)
            cms_list = cms_filter(
                kanna_user,
                params=params,
                cms_list_data=cms_list,
                on_user_cms_uuid_list=[],
                not_on_user_cms_uuid_list=[],
                not_on_group_cms_uuid_list=[],
                cms_uuid_list=[],
            )

            for cms in cms_list:
                get_cms_customize(cms)
                cms["building_name"] = cms.get("property", {}).get("name", "")
                cms["address"] = cms.get("property", {}).get("address", "")
                cms["postcode"] = cms.get("property", {}).get("postcode", "")
                cms["prefecture"] = cms.get("property", {}).get("prefecture", "")
                cms["city"] = cms.get("property", {}).get("city", "")
                cms["street_number"] = cms.get("property", {}).get("street_number", "")
                cms["room_number"] = cms.get("property", {}).get("room_number", "")

            customer_detail = client

            customer_detail["cms_list"] = cms_list
            serializer = self.get_serializer(data=customer_detail)
            serializer.is_valid(raise_exception=True)
            return Response(serializer.data)

        except ObjectDoesNotExist:
            raise UserWhitelistEmailError
