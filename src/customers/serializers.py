from rest_framework import serializers, status
from rest_framework.serializers import (
    Cha<PERSON><PERSON><PERSON>,
    UUIDField,
)

class CmsSerializer(serializers.Serializer):
    uuid = UUIDField()
    title = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    post_code = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    room_number = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    construction_no = CharField(allow_blank=True, default="")
    building_name = CharField(allow_blank=True, default="")


class ClientSerializer(serializers.Serializer):
    uuid = UUIDField()
    management_id = CharField(allow_blank=True, default="")
    business_form_type = CharField(allow_blank=True, default="")
    business_form_type_ja = Char<PERSON>ield(allow_blank=True, default="")
    name = Cha<PERSON><PERSON><PERSON>(allow_blank=True, default="")
    name_kana = Char<PERSON><PERSON>(allow_blank=True, default="")
    post_code = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    property_name = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    room_number = CharField(allow_blank=True, default="")
    phone_number = CharField(allow_blank=True, default="")
    phone_number2 = CharField(allow_blank=True, default="")
    fax_number = CharField(allow_blank=True, default="")
    email = CharField(allow_blank=True, default="")
    note = CharField(allow_blank=True, default="")
    created_at = CharField(allow_blank=True, default="")
    updated_at = CharField(allow_blank=True, default="")
    cms_list = CmsSerializer(many=True)


class ClientListSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField(allow_blank=True, default="")
    name_kana = CharField(allow_blank=True, default="")
    business_form_type = CharField(allow_blank=True, default="")
    business_form_type_ja = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    post_code = CharField(allow_blank=True, default="")    
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")
    room_number = CharField(allow_blank=True, default="")
    phone_number = CharField(allow_blank=True, default="")
    phone_number2 = CharField(allow_blank=True, default="")
    email = CharField(allow_blank=True, default="")
    created_at = CharField()
