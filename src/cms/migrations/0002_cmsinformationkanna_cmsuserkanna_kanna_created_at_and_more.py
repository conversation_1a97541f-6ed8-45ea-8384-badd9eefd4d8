# Generated by Django 4.1.3 on 2024-09-23 07:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("cms", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CmsInformationKanna",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("uuid", models.UUI<PERSON>ield(editable=False, unique=True)),
                ("management_id", models.TextField(blank=True, default="")),
                ("title", models.TextField(blank=True, null=True)),
                ("project_status", models.TextField(blank=True, null=True)),
                ("project_status_ja", models.TextField(blank=True, null=True)),
                ("project_status_name", models.TextField(blank=True, null=True)),
                ("project_status_uuid", models.UUIDField(blank=True, null=True)),
                (
                    "project_status_is_completed",
                    models.<PERSON><PERSON>an<PERSON>ield(blank=True, null=True),
                ),
                ("project_template_uuid", models.U<PERSON><PERSON>ield(blank=True, null=True)),
                ("project_template_name", models.TextField(blank=True, null=True)),
                ("parent_project_uuid", models.UUIDField(blank=True, null=True)),
                ("is_in_house", models.BooleanField(blank=True, null=True)),
                ("registered_user", models.JSONField(blank=True, null=True)),
                ("description", models.TextField(blank=True, default="")),
                ("started_on", models.DateField(default=None, null=True)),
                ("finished_on", models.DateField(default=None, null=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("finished_at", models.DateTimeField(blank=True, null=True)),
                ("note", models.TextField(blank=True, default="")),
                ("detail_url", models.TextField(blank=True, null=True)),
                ("chat_id", models.TextField(blank=True, default="")),
                ("chat_url", models.TextField(blank=True, default="")),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("property", models.JSONField(blank=True, null=True)),
                ("client", models.JSONField(blank=True, null=True)),
                ("industry_categories", models.JSONField(blank=True, null=True)),
                ("customized_sections", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "cms_information_kanna",
            },
        ),
        migrations.AddField(
            model_name="cmsuserkanna",
            name="kanna_created_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="cmsuserkanna",
            name="kanna_updated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
