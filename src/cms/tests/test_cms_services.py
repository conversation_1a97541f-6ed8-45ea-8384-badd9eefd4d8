import uuid
from unittest.mock import patch

from faker import Faker

from django.test import TestCase

from cms.models import CmsUserKanna
from cms.services import (
    fetch_cms_data_map,
    get_cms_list_detail,
    get_cms_list_parallel,
    get_cms_user_current,
    update_user_assign_cms,
)
from kanna_api.models.user_kanna import UserKanna


class TestCmsServices(TestCase):
    def setUp(self):
        """Set up test data."""
        self.faker = Faker()
        self.kanna_user = UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=self.faker.email(),
            name=self.faker.name(),
        )

    @patch("cms.services.get_cms_list")
    @patch("cms.services.update_cms_notification_email_setting")
    @patch("cms.services.get_cms_user_current")
    def test_update_user_assign_cms_returns_cms_list(
        self, mock_get_cms_user_current, mock_update_notification, mock_get_cms_list
    ):
        """Test that update_user_assign_cms returns the cms_list from KANNA API."""
        # Mock CMS list from KANNA
        mock_cms_list = [
            {"uuid": self.faker.uuid4(), "title": self.faker.name()},
            {"uuid": self.faker.uuid4(), "title": self.faker.name()},
        ]
        mock_get_cms_list.return_value = mock_cms_list

        # Mock existing CMS users in database
        mock_get_cms_user_current.return_value = []

        with patch.object(CmsUserKanna.objects, "bulk_create") as mock_bulk_create:
            with patch.object(CmsUserKanna.objects, "filter") as mock_filter:
                mock_filter.return_value.delete.return_value = (0, {})
                result = update_user_assign_cms(self.kanna_user)

                # Assert the function returns the cms_list
                self.assertEqual(result, mock_cms_list)

                # Verify get_cms_list was called with correct parameters
                mock_get_cms_list.assert_called_once_with(
                    user=self.kanna_user, cms_user_uuid=self.kanna_user.user_uuid
                )

                # Verify get_cms_user_current was called
                mock_get_cms_user_current.assert_called_once_with(self.kanna_user)

    @patch("cms.services.get_cms_list")
    @patch("cms.services.update_cms_notification_email_setting")
    @patch("cms.services.get_cms_user_current")
    def test_update_user_assign_cms_with_existing_cms_users(
        self, mock_get_cms_user_current, mock_update_notification, mock_get_cms_list
    ):
        """Test update_user_assign_cms when user already has some CMS assignments."""
        # Mock CMS list from KANNA
        mock_cms_list = [
            {"uuid": self.faker.uuid4(), "title": self.faker.name()},
            {"uuid": self.faker.uuid4(), "title": self.faker.name()},
        ]
        mock_get_cms_list.return_value = mock_cms_list

        # Mock existing CMS users in database
        mock_get_cms_user_current.return_value = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5"
        ]

        # Mock bulk operations
        with patch.object(CmsUserKanna.objects, "bulk_create") as mock_bulk_create:
            with patch.object(CmsUserKanna.objects, "filter") as mock_filter:
                mock_filter.return_value.delete.return_value = (0, {})

                # Call the function
                result = update_user_assign_cms(self.kanna_user)

                # Assert the function returns the cms_list
                self.assertEqual(result, mock_cms_list)

                # Verify bulk_create was called for new CMS
                mock_bulk_create.assert_called_once()

    @patch("cms.services.get_cms_list")
    def test_update_user_assign_cms_handles_api_error(self, mock_get_cms_list):
        """Test that update_user_assign_cms properly handles API errors."""
        # Mock API error
        mock_get_cms_list.side_effect = Exception("KANNA API Error")

        # Call the function and expect it to raise an exception
        with self.assertRaises(Exception) as context:
            update_user_assign_cms(self.kanna_user)

        # Verify the error message
        self.assertIn("KANNA API Error", str(context.exception))

    @patch("cms.services.get_cms_list")
    @patch("cms.services.update_cms_notification_email_setting")
    @patch("cms.services.get_cms_user_current")
    def test_update_user_assign_cms_creates_new_cms_assignments(
        self, mock_get_cms_user_current, mock_update_notification, mock_get_cms_list
    ):
        """Test that new CMS assignments are created correctly."""
        # Mock CMS list from KANNA
        mock_cms_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
            {"uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de", "title": "Construction 2"},
        ]
        mock_get_cms_list.return_value = mock_cms_list

        # Mock no existing CMS users
        mock_get_cms_user_current.return_value = []

        # Mock bulk operations
        with patch.object(CmsUserKanna.objects, "bulk_create") as mock_bulk_create:
            with patch.object(CmsUserKanna.objects, "filter") as mock_filter:
                mock_filter.return_value.delete.return_value = (0, {})
                update_user_assign_cms(self.kanna_user)

                # Verify bulk_create was called with correct data
                mock_bulk_create.assert_called_once()
                created_cms_users = mock_bulk_create.call_args[0][0]

                # Verify two CMS users were created
                self.assertEqual(len(created_cms_users), 2)

                # Verify the CMS users have correct data
                for cms_user in created_cms_users:
                    self.assertEqual(cms_user.user_uuid, self.kanna_user.user_uuid)
                    self.assertIn(
                        cms_user.cms_uuid,
                        [
                            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                            "d8734161-e718-40b5-b1a1-3e66aa9561de",
                        ],
                    )

    @patch("cms.services.get_cms_list")
    @patch("cms.services.update_cms_notification_email_setting")
    @patch("cms.services.get_cms_user_current")
    def test_update_user_assign_cms_removes_old_cms_assignments(
        self, mock_get_cms_user_current, mock_update_notification, mock_get_cms_list
    ):
        """Test that old CMS assignments are removed correctly."""
        # Mock CMS list from KANNA (only one CMS)
        mock_cms_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
        ]
        mock_get_cms_list.return_value = mock_cms_list

        # Mock existing CMS users (two CMS, one should be removed)
        # The user currently has 2 CMS assignments, but KANNA only returns 1
        # So the second one should be removed
        mock_get_cms_user_current.return_value = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",  # This one stays
            "d8734161-e718-40b5-b1a1-3e66aa9561de",  # This one should be removed
        ]

        with patch.object(CmsUserKanna.objects, "bulk_create") as mock_bulk_create:
            with patch.object(CmsUserKanna.objects, "filter") as mock_filter:
                mock_filter.return_value.delete.return_value = (1, {})
                result = update_user_assign_cms(self.kanna_user)

                # Verify delete was called to remove old CMS
                mock_filter.assert_called()

                # Verify bulk_create was NOT called (no new CMS to create)
                mock_bulk_create.assert_not_called()

                # Verify the function returns the cms_list
                self.assertEqual(result, mock_cms_list)

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_success_small_list(self, mock_get_cms_list_detail):
        """Test fetch_cms_data_map with small list (uses get_cms_list_detail)."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
            "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        ]

        mock_cms_data_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
            {"uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de", "title": "Construction 2"},
            {"uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "title": "Construction 3"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        expected_result = {
            uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5"): {
                "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "title": "Construction 1",
            },
            uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de"): {
                "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de",
                "title": "Construction 2",
            },
            uuid.UUID("a1b2c3d4-e5f6-7890-abcd-ef1234567890"): {
                "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                "title": "Construction 3",
            },
        }
        self.assertEqual(result, expected_result)

        mock_get_cms_list_detail.assert_called_once_with(cms_uuids, self.kanna_user)

    @patch("cms.services.get_cms_list_parallel")
    def test_fetch_cms_data_map_success_large_list(self, mock_get_cms_list_parallel):
        """Test fetch_cms_data_map with large list (uses get_cms_list_parallel)."""
        cms_uuids = [self.faker.uuid4() for i in range(15)]

        mock_cms_data_list = [
            {"uuid": str(cms_uuids[i]), "title": f"Construction {i}"} for i in range(15)
        ]
        mock_get_cms_list_parallel.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        # Should return a dict with UUID objects as keys
        self.assertEqual(len(result), 15)
        for i, uuid_key in enumerate(cms_uuids):
            cms_uuid = uuid.UUID(uuid_key)
            self.assertIn(cms_uuid, result)
            self.assertEqual(result[cms_uuid]["title"], f"Construction {i}")

        mock_get_cms_list_parallel.assert_called_once_with(user=self.kanna_user)

    def test_fetch_cms_data_map_empty_input(self):
        """Test fetch_cms_data_map with empty input."""
        result = fetch_cms_data_map([], self.kanna_user)

        self.assertEqual(result, {})

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_with_failed_requests(self, mock_get_cms_list_detail):
        """Test fetch_cms_data_map handles failed requests gracefully."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
            "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        ]

        mock_cms_data_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
            {},  # Failed request
            {"uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "title": "Construction 3"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        expected_result = {
            uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5"): {
                "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "title": "Construction 1",
            },
            uuid.UUID("a1b2c3d4-e5f6-7890-abcd-ef1234567890"): {
                "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                "title": "Construction 3",
            },
        }
        self.assertEqual(result, expected_result)

        self.assertNotIn(uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de"), result)

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_all_failed_requests(self, mock_get_cms_list_detail):
        """Test fetch_cms_data_map when all requests fail."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
        ]

        mock_cms_data_list = [{}, {}]  # All empty dicts
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        self.assertEqual(result, {})

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_partial_data(self, mock_get_cms_list_detail):
        """Test fetch_cms_data_map with partial data (some CMS not found)."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
            "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        ]

        mock_cms_data_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
            {"uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de", "title": "Construction 2"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        expected_result = {
            uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5"): {
                "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "title": "Construction 1",
            },
            uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de"): {
                "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de",
                "title": "Construction 2",
            },
        }
        self.assertEqual(result, expected_result)

        self.assertNotIn(uuid.UUID("a1b2c3d4-e5f6-7890-abcd-ef1234567890"), result)

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_with_complex_cms_data(self, mock_get_cms_list_detail):
        """Test fetch_cms_data_map with complex CMS data structures."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
        ]

        mock_cms_data_list = [
            {
                "uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
                "title": "Construction 1",
                "description": "Test construction",
                "status": "active",
                "created_at": "2023-01-01T00:00:00Z",
                "customized_sections": [
                    {
                        "name": "案件情報",
                        "customized_items": [
                            {"label": "工事コード", "values": ["CODE001"]},
                            {"label": "業態", "values": ["住宅"]},
                        ],
                    }
                ],
            },
            {
                "uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de",
                "title": "Construction 2",
                "description": "Another construction",
                "status": "pending",
                "created_at": "2023-01-02T00:00:00Z",
                "property": {
                    "uuid": "prop-123",
                    "address": "Test Address",
                    "customized_items": [{"label": "物件コード", "values": ["PROP001"]}],
                },
            },
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        self.assertEqual(len(result), 2)
        self.assertIn(uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5"), result)
        self.assertIn(uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de"), result)

        first_cms = result[uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5")]
        self.assertEqual(first_cms["title"], "Construction 1")
        self.assertEqual(first_cms["status"], "active")
        self.assertIn("customized_sections", first_cms)

        second_cms = result[uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de")]
        self.assertEqual(second_cms["title"], "Construction 2")
        self.assertEqual(second_cms["status"], "pending")
        self.assertIn("property", second_cms)

    @patch("cms.services.get_cms_list_detail")
    def test_fetch_cms_data_map_filters_by_requested_uuids(
        self, mock_get_cms_list_detail
    ):
        """Test that fetch_cms_data_map only returns data for requested UUIDs."""
        cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
        ]

        # Mock data includes extra CMS not in the requested list
        mock_cms_data_list = [
            {"uuid": "f0c3ca9b-06ff-479f-8337-cf2414fe18e5", "title": "Construction 1"},
            {"uuid": "d8734161-e718-40b5-b1a1-3e66aa9561de", "title": "Construction 2"},
            {"uuid": "extra-uuid-not-requested", "title": "Extra Construction"},
        ]
        mock_get_cms_list_detail.return_value = mock_cms_data_list

        result = fetch_cms_data_map(cms_uuids, self.kanna_user)

        # Should only include the requested UUIDs
        self.assertEqual(len(result), 2)
        self.assertIn(uuid.UUID("f0c3ca9b-06ff-479f-8337-cf2414fe18e5"), result)
        self.assertIn(uuid.UUID("d8734161-e718-40b5-b1a1-3e66aa9561de"), result)
        self.assertNotIn("extra-uuid-not-requested", result)

    def test_get_cms_user_current(self):
        """Test get_cms_user_current returns correct list of CMS UUIDs."""
        # Create some CMS user records with valid UUIDs
        cms_user1 = CmsUserKanna.objects.create(
            cms_uuid="f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            user_uuid=self.kanna_user.user_uuid,
        )
        cms_user2 = CmsUserKanna.objects.create(
            cms_uuid="d8734161-e718-40b5-b1a1-3e66aa9561de",
            user_uuid=self.kanna_user.user_uuid,
        )

        result = get_cms_user_current(self.kanna_user)

        self.assertEqual(len(result), 2)
        self.assertIn("f0c3ca9b-06ff-479f-8337-cf2414fe18e5", result)
        self.assertIn("d8734161-e718-40b5-b1a1-3e66aa9561de", result)

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_success(self, mock_execute_parallel):
        """Test get_cms_list_detail successfully fetches CMS details."""
        cms_uuids = ["uuid-1", "uuid-2", "uuid-3"]

        mock_execute_parallel.return_value = [
            {"uuid": "uuid-1", "title": "CMS 1"},
            {"uuid": "uuid-2", "title": "CMS 2"},
            {"uuid": "uuid-3", "title": "CMS 3"},
        ]

        result = get_cms_list_detail(cms_uuids, self.kanna_user)

        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["title"], "CMS 1")
        self.assertEqual(result[1]["title"], "CMS 2")
        self.assertEqual(result[2]["title"], "CMS 3")

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_with_failures(self, mock_execute_parallel):
        """Test get_cms_list_detail handles individual CMS fetch failures."""
        cms_uuids = ["uuid-1", "uuid-2", "uuid-3"]

        mock_execute_parallel.return_value = [
            {"uuid": "uuid-1", "title": "CMS 1"},
            {},  # Failed request
            {"uuid": "uuid-3", "title": "CMS 3"},
        ]

        result = get_cms_list_detail(cms_uuids, self.kanna_user)

        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["title"], "CMS 1")
        self.assertEqual(result[1], {})  # Failed request returns empty dict
        self.assertEqual(result[2]["title"], "CMS 3")

    def test_get_cms_list_detail_empty_input(self):
        """Test get_cms_list_detail with empty input."""
        result = get_cms_list_detail([], self.kanna_user)
        self.assertEqual(result, [])

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_handles_exception(self, mock_execute_parallel):
        """Test get_cms_list_detail handles exceptions gracefully."""
        cms_uuids = ["uuid-1", "uuid-2"]

        mock_execute_parallel.side_effect = Exception("API Error")

        result = get_cms_list_detail(cms_uuids, self.kanna_user)

        # Should return empty dicts for all requests when exception occurs
        self.assertEqual(result, [{}, {}])

    @patch("cms.services._fetch_cms_page")
    def test_get_cms_list_parallel_single_page(self, mock_fetch_cms_page):
        """Test get_cms_list_parallel with single page."""
        mock_fetch_cms_page.return_value = (1, [{"uuid": "uuid-1", "title": "CMS 1"}])

        result = get_cms_list_parallel(self.kanna_user)

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["title"], "CMS 1")

    @patch("cms.services._fetch_cms_page")
    def test_get_cms_list_parallel_two_pages(self, mock_fetch_cms_page):
        """Test get_cms_list_parallel with two pages (sequential fetching)."""
        mock_fetch_cms_page.side_effect = [
            (2, [{"uuid": "uuid-1", "title": "CMS 1"}]),
            (2, [{"uuid": "uuid-2", "title": "CMS 2"}]),
        ]

        result = get_cms_list_parallel(self.kanna_user)

        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["title"], "CMS 1")
        self.assertEqual(result[1]["title"], "CMS 2")

    @patch("cms.services._fetch_cms_page")
    def test_get_cms_list_parallel_multiple_pages(self, mock_fetch_cms_page):
        """Test get_cms_list_parallel with multiple pages (parallel fetching)."""
        # Mock first page call (returns total_page=3 and first page data)
        mock_fetch_cms_page.side_effect = [
            (3, [{"uuid": self.faker.uuid4(), "title": "CMS 1"}]),  # First call: page 1
            (
                3,
                [{"uuid": self.faker.uuid4(), "title": "CMS 2"}],
            ),  # Parallel call: page 2
            (
                3,
                [{"uuid": self.faker.uuid4(), "title": "CMS 3"}],
            ),  # Parallel call: page 3
        ]

        result = get_cms_list_parallel(self.kanna_user)

        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["title"], "CMS 1")
        self.assertEqual(result[1]["title"], "CMS 2")
        self.assertEqual(result[2]["title"], "CMS 3")
