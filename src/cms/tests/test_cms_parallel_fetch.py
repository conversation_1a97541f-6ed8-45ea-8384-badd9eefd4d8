from unittest.mock import MagicMock, patch

from faker import Faker

from django.test import TestCase

from cms.services import (
    _execute_parallel_cms_fetch_simple,
    _fetch_cms_simple,
    _process_completed_futures_simple,
    get_cms_list_detail,
)
from kanna_api.models.user_kanna import UserKanna


class TestCmsParallelFetch(TestCase):
    def setUp(self):
        """Set up test data."""
        self.faker = Faker()
        self.kanna_user = UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=self.faker.email(),
            name=self.faker.name(),
        )
        self.cms_uuids = [
            "f0c3ca9b-06ff-479f-8337-cf2414fe18e5",
            "d8734161-e718-40b5-b1a1-3e66aa9561de",
            "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        ]

    @patch("cms.services.get_cms_kanna")
    def test_fetch_cms_simple_success(self, mock_get_cms_kanna):
        """Test _fetch_cms_simple with successful API call."""
        mock_cms_data = {
            "uuid": self.cms_uuids[0],
            "title": "Test Construction",
            "description": "Test description",
        }
        mock_get_cms_kanna.return_value = mock_cms_data
        index, result = _fetch_cms_simple(
            self.cms_uuids[0], self.kanna_user, self.cms_uuids
        )
        self.assertEqual(index, 0)  # First UUID in the list
        self.assertEqual(result, mock_cms_data)
        mock_get_cms_kanna.assert_called_once_with(self.cms_uuids[0], self.kanna_user)

    @patch("cms.services.get_cms_kanna")
    def test_fetch_cms_simple_api_error(self, mock_get_cms_kanna):
        """Test _fetch_cms_simple when API returns error."""
        mock_get_cms_kanna.return_value = {"error": "CMS not found"}
        index, result = _fetch_cms_simple(
            self.cms_uuids[0], self.kanna_user, self.cms_uuids
        )
        self.assertEqual(index, 0)
        self.assertEqual(result, {})  # Should return empty dict for error
        mock_get_cms_kanna.assert_called_once_with(self.cms_uuids[0], self.kanna_user)

    @patch("cms.services.get_cms_kanna")
    def test_fetch_cms_simple_exception(self, mock_get_cms_kanna):
        """Test _fetch_cms_simple when API call raises exception."""
        mock_get_cms_kanna.side_effect = Exception("Network error")
        index, result = _fetch_cms_simple(
            self.cms_uuids[0], self.kanna_user, self.cms_uuids
        )

        self.assertEqual(index, 0)
        self.assertEqual(result, {})  # Should return empty dict for exception
        mock_get_cms_kanna.assert_called_once_with(self.cms_uuids[0], self.kanna_user)

    @patch("cms.services.as_completed")
    def test_process_completed_futures_simple_success(self, mock_as_completed):
        """Test _process_completed_futures_simple with successful futures."""
        future1 = MagicMock()
        future1.result.return_value = (0, {"title": "Construction 1"})

        future2 = MagicMock()
        future2.result.return_value = (1, {"title": "Construction 2"})

        future_to_uuid = {future1: self.cms_uuids[0], future2: self.cms_uuids[1]}

        mock_as_completed.return_value = [future1, future2]
        results = _process_completed_futures_simple(future_to_uuid, self.cms_uuids)
        self.assertEqual(len(results), 3)  # Should have 3 items (length of cms_uuids)
        self.assertEqual(results[0], {"title": "Construction 1"})
        self.assertEqual(results[1], {"title": "Construction 2"})
        self.assertEqual(results[2], {})  # Default empty dict for missing item

    @patch("cms.services.as_completed")
    def test_process_completed_futures_simple_with_exceptions(self, mock_as_completed):
        """Test _process_completed_futures_simple with future exceptions."""
        # Create mock futures with one exception
        future1 = MagicMock()
        future1.result.return_value = (0, {"title": "Construction 1"})

        future2 = MagicMock()
        future2.result.side_effect = Exception("Thread error")
        future_to_uuid = {future1: self.cms_uuids[0], future2: self.cms_uuids[1]}
        mock_as_completed.return_value = [future1, future2]
        results = _process_completed_futures_simple(future_to_uuid, self.cms_uuids)
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0], {"title": "Construction 1"})
        self.assertEqual(results[1], {})  # Should remain empty dict due to exception
        self.assertEqual(results[2], {})  # Default empty dict

    @patch("cms.services.ThreadPoolExecutor")
    @patch("cms.services._fetch_cms_simple")
    @patch("cms.services.as_completed")
    def test_execute_parallel_cms_fetch_simple_success(
        self, mock_as_completed, mock_fetch_cms, mock_executor
    ):
        """Test _execute_parallel_cms_fetch_simple with successful execution."""
        mock_executor_instance = MagicMock()
        mock_executor.return_value.__enter__.return_value = mock_executor_instance

        mock_future1 = MagicMock()
        mock_future2 = MagicMock()
        mock_executor_instance.submit.side_effect = [mock_future1, mock_future2]
        mock_as_completed.return_value = [mock_future1, mock_future2]
        mock_future1.result.return_value = (0, {"title": "Construction 1"})
        mock_future2.result.return_value = (1, {"title": "Construction 2"})

        results = _execute_parallel_cms_fetch_simple(
            self.cms_uuids[:2], self.kanna_user
        )

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], {"title": "Construction 1"})
        self.assertEqual(results[1], {"title": "Construction 2"})

        # Verify executor was called correctly
        mock_executor.assert_called_once_with(max_workers=10)
        self.assertEqual(mock_executor_instance.submit.call_count, 2)

    @patch("cms.services.ThreadPoolExecutor")
    def test_execute_parallel_cms_fetch_simple_exception(self, mock_executor):
        """Test _execute_parallel_cms_fetch_simple when executor raises exception."""
        mock_executor.side_effect = Exception("Executor error")
        with self.assertRaises(Exception):
            _execute_parallel_cms_fetch_simple(self.cms_uuids[:2], self.kanna_user)

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_success(self, mock_execute_fetch):
        """Test get_cms_list_detail with successful execution."""
        mock_cms_data = [
            {"title": "Construction 1"},
            {"title": "Construction 2"},
        ]
        mock_execute_fetch.return_value = mock_cms_data
        results = get_cms_list_detail(self.cms_uuids[:2], self.kanna_user)
        self.assertEqual(results, mock_cms_data)
        mock_execute_fetch.assert_called_once_with(self.cms_uuids[:2], self.kanna_user)

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_empty_input(self, mock_execute_fetch):
        """Test get_cms_list_detail with empty input."""
        results = get_cms_list_detail([], self.kanna_user)
        self.assertEqual(results, [])
        mock_execute_fetch.assert_not_called()

    @patch("cms.services._execute_parallel_cms_fetch_simple")
    def test_get_cms_list_detail_exception(self, mock_execute_fetch):
        """Test get_cms_list_detail when underlying function raises exception."""
        mock_execute_fetch.side_effect = Exception("Fetch error")
        results = get_cms_list_detail(self.cms_uuids[:2], self.kanna_user)

        # Verify results - should return empty dicts for all requests
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], {})
        self.assertEqual(results[1], {})

    @patch("cms.services.get_cms_kanna")
    def test_get_cms_list_detail_integration(self, mock_get_cms_kanna):
        """Integration test for get_cms_list_detail with real parallel execution."""
        # Mock successful responses for each UUID
        mock_responses = [
            {"title": f"Construction {i}", "uuid": uuid}
            for i, uuid in enumerate(self.cms_uuids)
        ]
        mock_get_cms_kanna.side_effect = mock_responses
        results = get_cms_list_detail(self.cms_uuids, self.kanna_user)

        self.assertEqual(len(results), 3)
        for i, result in enumerate(results):
            self.assertEqual(result["title"], f"Construction {i}")
            self.assertEqual(result["uuid"], self.cms_uuids[i])

        self.assertEqual(mock_get_cms_kanna.call_count, 3)
        for uuid in self.cms_uuids:
            mock_get_cms_kanna.assert_any_call(uuid, self.kanna_user)
