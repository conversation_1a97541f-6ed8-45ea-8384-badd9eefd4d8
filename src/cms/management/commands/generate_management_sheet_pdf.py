from django.core.management.base import BaseCommand, CommandError
from management_sheets.models import ManagementSheet
from approve_flow.services import render_to_pdf_and_send_to_kanna_management_sheet
from kanna_api.models.user_kanna import LevelChoices, UserKanna
from management_sheets.serializers import GetManagementSheetDetailSerialzer
import json


class Command(BaseCommand):
    help = 'Generate management sheet PDF and send to Kanna'

    def add_arguments(self, parser):
        parser.add_argument('management_id', type=int, help='ID of the management sheet')

    def handle(self, *args, **options):
        management_id = 2449
        management_id = 2450
        kanna_user: UserKanna = UserKanna.objects.get(pk=1557)
        user = kanna_user.user
        
        management_sheet = ManagementSheet.objects.get(id=management_id)
        
        # Serialize the management sheet to ensure all data is properly formatted
        serializer = GetManagementSheetDetailSerialzer(management_sheet, context={'user': user})
        management_sheet_data = serializer.data
        
        # Debug: Print the actual_construction_methods data
        for diagnostic in management_sheet_data.get('diagnostics', []):
            for actual_construction in diagnostic.get('actual_constructions', []):
                # self.stdout.write(self.style.WARNING(f'Actual construction methods: {json.dumps(actual_construction.get("actual_construction_methods", []), indent=2)}'))
                actual_construction_methods = actual_construction.get('actual_construction_methods', [])
                print('-' * 100)
                for actual_construction_method in actual_construction_methods:
                    print(actual_construction_method)
                    # print(actual_construction_method, actual_construction_method['material'])

        result = render_to_pdf_and_send_to_kanna_management_sheet(
            management_sheet=management_sheet,
            cms_uuid=management_sheet.cms_uuid,
            kanna_user=kanna_user
        )
        
        self.stdout.write(self.style.SUCCESS(f'Result: {result}'))