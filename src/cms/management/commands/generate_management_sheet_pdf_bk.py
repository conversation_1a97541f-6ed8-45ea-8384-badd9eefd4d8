from django.core.management.base import BaseCommand, CommandError

from diagnostics.models import DiagnosticConstructionMethod
from management_sheets.models import ManagementSheet, ActualConstructionMethod
from approve_flow.services import render_to_pdf_and_send_to_kanna_management_sheet
from kanna_api.models.user_kanna import LevelChoices, UserKanna
from management_sheets.serializers import GetManagementSheetDetailSerialzer
import json


class Command(BaseCommand):
    help = 'Generate management sheet PDF and send to Kanna'

    def add_arguments(self, parser):
        parser.add_argument('management_id', type=int, help='ID of the management sheet')

    def check_wrong_actual_construction_methods(self, management_sheet: ManagementSheet):
        actual_constructions = management_sheet.actual_constructions.all()
        for actual_construction in actual_constructions:
            construction_method = actual_construction.diagnostic_construction_method.construction_method
            actual_cms = (
                ActualConstructionMethod.objects.select_related("material")
                .filter(
                    actual_construction_id=actual_construction.pk,

                )
                .exclude(material_id__in=construction_method.construction_method_step.values_list(
                        "material_id", flat=True
                    ),)
                .order_by("id")
            )
            if actual_cms:
                return True
        return False

    def handle(self, *args, **options):
        kanna_user: UserKanna = UserKanna.objects.get(pk=1557)
        # management_sheets = ManagementSheet.objects.all()
        # wrong_actual_construction_methods = []
        # for management_sheet in management_sheets:
        #     try:
        #         wrong = self.check_wrong_actual_construction_methods(management_sheet)
        #     except AttributeError:
        #         wrong = False
        #         print(f"Ignore invalid management sheet: {management_sheet.pk}")
        #     if wrong:
        #         wrong_actual_construction_methods.append(management_sheet.pk)
        #
        # print(f"Total wrong management sheet: {len(wrong_actual_construction_methods)}")
        # print(f"Wrong management sheet: {wrong_actual_construction_methods}")
        management_sheet = ManagementSheet.objects.get(pk=2451)
        for diagnostic in management_sheet.diagnostic:
            diagnostic_cm = DiagnosticConstructionMethod.objects.filter(diagnostic=diagnostic)
        print(dir(management_sheet))

pass


def check_wrong_actual_construction_methods(management_sheet: ManagementSheet):
    actual_constructions = management_sheet.actual_constructions.all()
    for actual_construction in actual_constructions:
        construction_method = actual_construction.diagnostic_construction_method.construction_method
        actual_cms = (
            ActualConstructionMethod.objects.select_related("material")
            .filter(
                actual_construction_id=actual_construction.pk,

            )
            .exclude(material_id__in=construction_method.construction_method_step.values_list(
                "material_id", flat=True
            ), )
            .order_by("id")
        )
        if actual_cms:
            return True
    return False

