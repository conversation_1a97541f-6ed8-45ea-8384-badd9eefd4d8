from django.core.management.base import BaseCommand
from django.db import transaction

from cms.models import CmsUserKanna
from notification_settings.models import NotificationEmailSetting


class Command(BaseCommand):
    help = "Sync old data to notification and email settings"

    def handle(self, *args, **options):
        try:
            with transaction.atomic():
                cms_user_kanna = CmsUserKanna.objects.all()
                number_of_cms_user_kanna = len(cms_user_kanna)
                number_of_notification_email_setting_exist = 0
                number_of_notification_email_setting_create = 0

                for item in cms_user_kanna:
                    (
                        notification_email_setting,
                        is_created,
                    ) = NotificationEmailSetting.objects.get_or_create(
                        cms_uuid=item.cms_uuid,
                        user_uuid=item.user_uuid,
                    )
                    if is_created:
                        number_of_notification_email_setting_create += 1
                    else:
                        number_of_notification_email_setting_exist += 1

            print(f"number_of_cms_user_kanna: {number_of_cms_user_kanna}")
            print(
                f"number_of_notification_email_setting_exist: {number_of_notification_email_setting_exist}"
            )
            print(
                f"number_of_notification_email_setting_create: {number_of_notification_email_setting_create}"
            )

        except Exception as e:
            print(e)
