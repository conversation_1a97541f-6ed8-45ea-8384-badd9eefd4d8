from rest_framework import serializers
from rest_framework.serializers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Date<PERSON><PERSON>Field,
    IntegerField,
    UUIDField,
)

from authentication.models import User
from management_sheets.models import ManagementSheet
from properties.serializers import CmsPropertySerializer
from users.models import UserConstruction


class CmsIndustryCategoriesSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = Char<PERSON>ield()


class CmsPropertyImagesSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = Char<PERSON>ield()
    image_url = Char<PERSON>ield()
    image_thumb_url = CharField()


class CmsClientSerializer(serializers.Serializer):
    uuid = CharField()
    management_id = CharField(allow_blank=True, default="")
    business_form_type = CharField(allow_blank=True, default="")
    business_form_type_ja = CharField(allow_blank=True, default="")
    full_name = Char<PERSON>ield(allow_blank=True, default="")
    full_name_kana = <PERSON><PERSON><PERSON><PERSON>(allow_blank=True, default="")
    company_name = Char<PERSON>ield(allow_blank=True, default="")
    company_name_kana = CharField(allow_blank=True, default="")
    gender_ja = CharField(allow_blank=True, default="")
    birthdate = DateField(allow_null=True, default=None)
    staff_full_name = CharField(allow_blank=True, default="")
    staff_full_name_kana = CharField(allow_blank=True, default="")
    address = CharField(allow_blank=True, default="")
    post_code = CharField(allow_blank=True, default="")
    property_name = CharField(allow_blank=True, default="")
    room_number = CharField(allow_blank=True, default="")
    phone_number = CharField(allow_blank=True, default="")
    phone_number2 = CharField(allow_blank=True, default="")
    fax_number = CharField(allow_blank=True, default="")
    email = CharField(allow_blank=True, default="")
    note = CharField(allow_blank=True, default="")


class CmsSerializer(serializers.Serializer):
    uuid = UUIDField()
    management_id = CharField(allow_blank=True, default="")
    title = CharField()
    project_status_ja = CharField()
    project_status = CharField()
    management_id = CharField(required=False, allow_blank=True, default=None)
    is_in_house = BooleanField()
    description = CharField(allow_blank=True, default="")
    started_on = DateField(allow_null=True, default=None)
    finished_on = DateField(allow_null=True, default=None)
    created_at = DateTimeField()
    created_by = CharField(allow_blank=True, default="")
    updated_at = DateTimeField()
    industry_categories = CmsIndustryCategoriesSerializer(many=True)
    industry_code = CharField(allow_blank=True, default="")
    business_category = CharField(allow_blank=True, default="")
    business_office = CharField(allow_blank=True, default="")
    chat_id = CharField(allow_blank=True, default="")
    chat_url = CharField(allow_blank=True, default="")
    note = CharField(allow_blank=True, default="")
    property = CmsPropertySerializer(allow_null=True, default=None)
    client = CmsClientSerializer(allow_null=True, default=None)


class CmsLiteSerializer(serializers.Serializer):
    uuid = UUIDField()
    title = CharField()


class CmsListIndustryCategoriesSerializer(serializers.Serializer):
    name = CharField()


class CmsListPropertyImagesSerializer(serializers.Serializer):
    uuid = UUIDField()
    name = CharField()
    image_url = CharField()
    image_thumb_url = CharField()


class CmsListPropertySerializer(serializers.Serializer):
    name = CharField(allow_blank=True, default="")
    images = CmsListPropertyImagesSerializer(many=True, required=False)
    address = CharField(allow_blank=True, default="")
    postcode = CharField(allow_blank=True, default="")
    prefecture = CharField(allow_blank=True, default="")
    city = CharField(allow_blank=True, default="")
    street_number = CharField(allow_blank=True, default="")


class UserCmsSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("id", "email", "first_name", "last_name", "phone")


class CmsListSerializer(serializers.Serializer):
    uuid = UUIDField()
    construction_no = serializers.SerializerMethodField(read_only=True)
    title = CharField()
    project_status_ja = CharField()
    management_id = CharField(required=False, allow_blank=True, default=None)
    industry_categories = CmsListIndustryCategoriesSerializer(many=True)
    industry_code = CharField(allow_blank=True, default="")
    property = CmsListPropertySerializer(allow_null=True, default=None)
    finished_on = DateField(allow_null=True, default=None)
    started_on = DateField(allow_null=True, default=None)
    created_at = DateTimeField()
    business_office = CharField(allow_blank=True, default="")
    owner_information = serializers.SerializerMethodField(read_only=True)
    number_of_diagnostic = IntegerField(allow_null=True, default=0)
    number_of_management_sheet = IntegerField(allow_null=True, default=0)
    number_of_warranty = IntegerField(allow_null=True, default=0)
    owner_name_kanna = CharField(allow_blank=True, default="")
    client = CmsClientSerializer(allow_null=True, default=None)

    def get_construction_no(self, obj):
        ms_uuid = ManagementSheet.objects.filter(cms_uuid=obj["uuid"]).first()
        construction_no = None
        if ms_uuid:
            construction_no = ms_uuid.construction_no

        return construction_no

    def get_owner_information(self, obj):
        owner_information = UserConstruction.objects.filter(
            construction_uuid=obj["uuid"]
        ).first()
        owner = None
        if owner_information:
            owner = User.objects.filter(email=owner_information.owner)
        serializer = UserCmsSerializer(owner, many=True)

        return serializer.data


class CmsPageSerializer(serializers.Serializer):
    count = IntegerField()
    results = CmsListSerializer(many=True)


class CmsCreateRequestSerializer(serializers.Serializer):
    title = CharField()
    project_status = CharField()


class CmsUpdateRequestSerializer(serializers.Serializer):
    title = CharField(required=False)
    project_status = CharField(required=False)
    management_id = CharField(required=False, allow_blank=True, default=None)
    description = CharField(required=False, allow_blank=True)
    started_on = DateField(required=False, allow_null=True)
    finished_on = DateField(required=False, allow_null=True)
    note = CharField(required=False, allow_blank=True)
    property = CmsPropertySerializer(required=False)
    industry_code = CharField(required=False, allow_blank=True, default=None)
    business_category = CharField(required=False, allow_blank=True, default=None)
    business_office = CharField(required=False, allow_blank=True, default=None)
    construction_no = CharField(required=False, allow_blank=True, default=None)


class CmsDetailRequestSerializer(serializers.Serializer):
    uuid = UUIDField()
    title = CharField(required=False)
    project_status = CharField(required=False)
    project_status_ja = CharField(required=False)
    management_id = CharField(required=False, allow_blank=True, default=None)
    description = CharField(required=False, allow_blank=True)
    started_on = DateField(required=False, allow_null=True)
    finished_on = DateField(required=False, allow_null=True)
    note = CharField(required=False, allow_blank=True)
    property = CmsPropertySerializer(required=False)
    industry_code = CharField(required=False, allow_blank=True, default=None)
    business_category = CharField(required=False, allow_blank=True, default=None)
    business_office = CharField(required=False, allow_blank=True, default=None)
    construction_no = CharField(required=False, allow_blank=True, default=None)
    title = CharField(required=False, allow_blank=True, default=None)
    owner_information = serializers.SerializerMethodField(read_only=True)
    owner_name_kanna = CharField(required=False, allow_blank=True, default=None)
    client = CmsClientSerializer(allow_null=True, default=None)

    def get_owner_information(self, obj):
        owner_information = UserConstruction.objects.filter(
            construction_uuid=obj["uuid"]
        ).first()
        owner = None
        if owner_information:
            owner = User.objects.filter(email=owner_information.owner)
        serializer = UserCmsSerializer(owner, many=True)

        return serializer.data
