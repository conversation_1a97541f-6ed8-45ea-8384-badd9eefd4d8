import random
import string
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import timed<PERSON><PERSON>
from logging import getLogger
from typing import Any, Dict, List

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q, TextField
from django.db.models.functions import Cast
from django.utils import timezone

from api import constants
from authentication.models import User
from cms.cms import cms_filter, get_cms, get_cms_list
from cms.models import CmsUserKanna
from companies.models import CompanyType, ShopDealer
from kanna_api.cms_users import add_cms_users
from kanna_api.common import KaRequests
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.cms import CmsParam
from notification_settings.models import NotificationEmailSetting
from notification_settings.services import (
    update_cms_notification_email_setting,
)
from utilities.json_convert.convert import camel_to_snake_json
from utilities.kanna_api.multi_filter import MultiFilter

logger = getLogger(__name__)
RETRY_BACKOFF_SECONDS = 1
CMS_API_MAX_WORKERS = 10


def get_cms_customize(cms: dict):
    customized_items = []
    # カスタマイズ項目の案件情報
    customized_sections = cms.get("customized_sections", [])
    for customized_section in customized_sections:
        customized_section_name = customized_section.get("name", "")
        if customized_section_name == "案件情報":
            customized_items = customized_section.get("customized_items", [])

    # 案件情報から工事コード_業態_営業所を取得
    for customized_item in customized_items:
        label = customized_item.get("label", "")
        if label == "工事コード":
            values = customized_item.get("values", [])
            cms["industry_code"] = values[0] if values else ""
        elif label == "業態":
            values = customized_item.get("values", [])
            cms["business_category"] = values[0] if values else ""
        elif label == "営業所":
            values = customized_item.get("values", [])
            cms["business_office"] = values[0] if values else ""
        elif label == "工事番号":
            values = customized_item.get("values", [])
            cms["construction_no"] = values[0] if values else ""
        elif label == "施主名":
            values = customized_item.get("values", [])
            cms["owner_name_kanna"] = values[0] if values else ""


def get_property_customize(property_data):
    # 物件コードを取得
    for customized_item in property_data.get("customized_items", []):
        label = customized_item.get("label", "")
        if label == "物件コード":
            values = customized_item.get("values", [""])
            property_data["code"] = values[0] if values else ""


def get_kanna_cms_list(request):
    params = CmsParam(**request.query_params.dict())
    # list cms on kanna
    kanna_user = UserKanna.objects.get(email=request.user.email)
    # cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)
    cms_list = get_cms_list(user=kanna_user)
    cms_list_data = camel_to_snake_json(cms_list)
    for cms in cms_list_data:
        get_cms_customize(cms)
    cms_list_data = cms_filter(
        request.user,
        params=params,
        cms_list_data=cms_list_data,
        on_user_cms_uuid_list=[],
        not_on_user_cms_uuid_list=[],
        not_on_group_cms_uuid_list=[],
        cms_uuid_list=[],
    )
    cms_list_short_fields = {}
    for cms in cms_list_data:
        construction_no = ""
        if "construction_no" in cms:
            construction_no = cms["construction_no"]
        cms_list_short_field = {
            cms["uuid"]: {"title": cms["title"], "construction_no": construction_no}
        }
        cms_list_short_fields.update(cms_list_short_field)
    return cms_list_short_fields


def get_cms_kanna(cms_uuid, kanna_user):
    # cms取得
    try:
        cms = get_cms(kanna_user, cms_uuid)
        if not cms:
            return {"error": "cms not found"}
        cms_data = camel_to_snake_json(cms)
        # カスタマイズ項目取得
        get_cms_customize(cms_data)
        # 案件詳細に物件情報がある場合カスタマイズ項目取得
        if cms_data_propery := cms_data.get("property"):
            get_property_customize(cms_data_propery)
        # 案件作成者を追加
        cms_data["created_by"] = cms_data.get("registered_user").get("full_name")
        return cms_data
    except ObjectDoesNotExist:
        return False


def get_cms_user_current(user_kanna):
    return list(
        CmsUserKanna.objects.annotate(str=Cast("cms_uuid", output_field=TextField()))
        .values_list("str", flat=True)
        .filter(Q(user_uuid=user_kanna.user_uuid))
    )


def update_user_assign_cms(user_kanna: UserKanna):
    """
    Update user's assigned CMS list by syncing with KANNA API.

    Args:
        user_kanna: The Kanna user to update CMS assignments for

    Returns:
        List of CMS data from KANNA API

    Raises:
        Exception: If sync operation fails
    """
    try:
        # Get cms for user_uuid
        uuid_cms = []
        multi_filter_new = MultiFilter()
        multi_filter_remove = MultiFilter()
        cms_list = get_cms_list(user=user_kanna, cms_user_uuid=user_kanna.user_uuid)

        # Get current cms user kanna in PJP
        cms_user_current = list(
            CmsUserKanna.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(Q(user_uuid=user_kanna.user_uuid))
        )
        cms_user_current = get_cms_user_current(user_kanna)

        # Add filleter
        multi_filter_new.add(
            lambda cms_list: cms_list.get("uuid") not in cms_user_current
        )
        cms_list_data_new = multi_filter_new.run(cms_list)

        # Bulk_create new cms for user kanna
        if cms_list_data_new:
            for cms in cms_list_data_new:
                cms_user = CmsUserKanna(
                    cms_uuid=cms["uuid"],
                    user_uuid=user_kanna.user_uuid,
                )
                uuid_cms.append(cms_user)

            CmsUserKanna.objects.bulk_create(uuid_cms)

        # Remove cms for user kanna in AGC
        # Add filleter

        uuid_cms_remove = []
        for cms_item in cms_list:
            uuid_cms_remove.append(cms_item["uuid"])

        multi_filter_remove.add(
            lambda cms_user_current: cms_user_current not in uuid_cms_remove
        )
        cms_list_data_remove = multi_filter_remove.run(cms_user_current)
        CmsUserKanna.objects.filter(cms_uuid__in=cms_list_data_remove).delete()
        update_cms_notification_email_setting(
            user_uuid=user_kanna.user_uuid,
        )

        return cms_list

    except Exception as e:
        raise Exception(str(e))


def gen_verify_code(user: User):
    from authentication.serializers import UserVerifySerializer

    gen_code = "".join(
        random.choices(string.ascii_uppercase + string.digits, k=settings.TOKEN_LENGTH)
    )
    exp_in = timezone.now() + timedelta(minutes=settings.TOKEN_EXPIRE)

    user_verify_code = {"user": user.id, "code": gen_code, "expire_time": exp_in}

    serializer_user_verify_code = UserVerifySerializer(
        data=user_verify_code, many=False
    )
    serializer_user_verify_code.is_valid(raise_exception=True)
    serializer_user_verify_code.save()

    gen_code = serializer_user_verify_code.data["code"]

    return gen_code


def get_cms_related_member_uuids(kanna_user_request: UserKanna):
    """
    Get a list of related member UUIDs based on the user's company and dealer relationships.

    This function retrieves UUIDs of users who are:
    1. In the same company as the requesting user
    2. In the dealer company associated with the user's company
    3. In companies with type ACT

    Args:
        kanna_user_request (UserKanna): The Kanna user requesting the related members list

    Returns:
        list[str]: A list of user UUIDs, ordered by last login time (most recent first)

    Note:
        - The function uses a TextField cast to ensure consistent UUID string format
        - Results are ordered by last_login_at in descending order
        - Only users with non-null user_uuid are included
    """
    company_dealer: ShopDealer = ShopDealer.objects.filter(
        shop=kanna_user_request.company
    ).first()
    related_member_uuids = (
        UserKanna.objects.annotate(str=Cast("user_uuid", output_field=TextField()))
        .filter(
            Q(company__in=[kanna_user_request.company, company_dealer.dealer])
            | Q(company__type=CompanyType.ACT),
            Q(user_uuid__isnull=False),
        )
        .order_by("-last_login_at")
        .values_list("str", flat=True)
    )
    return list(related_member_uuids)


def link_related_members_to_construction(
    cms_data: dict[str, str], related_member_uuids: list[str]
):
    """
    Link related members to a construction project and set up their notification settings.

    This function:
    1. Adds an internal default Kanna user if not already in the list
    2. For each member UUID:
        - Adds them to the CMS with ADMIN role via API
        - Creates a CmsUserKanna record
        - Creates a NotificationEmailSetting record
    3. Bulk creates all records for better performance

    Args:
        cms_data (dict[str, str]): Dictionary containing CMS information, must include 'uuid'
        related_member_uuids (list[str]): List of user UUIDs to be linked to the construction

    Note:
        - Uses bulk_create for better performance
        - Continues processing even if individual user addition fails
        - Logs success and failure information for each user
        - Requires an internal Kanna user for API operations
    """
    cms_user_kanna_bulk = []
    notification_email_setting_bulk = []
    kanna_user_internal_default = (
        UserKanna.objects.filter(user__email=settings.USER_KANNA_EMAIL_TO_CREATE_CMS)
        .order_by("-last_login_at")
        .first()
    )
    if str(kanna_user_internal_default.user_uuid) not in related_member_uuids:
        cms_user_kanna_bulk.append(
            CmsUserKanna(
                cms_uuid=cms_data["uuid"],
                user_uuid=kanna_user_internal_default.user_uuid,
            )
        )
        notification_email_setting_bulk.append(
            NotificationEmailSetting(
                cms_uuid=cms_data["uuid"],
                user_uuid=kanna_user_internal_default.user_uuid,
            )
        )
    for user_uuid in related_member_uuids:
        try:
            logger.info({"User before assign": user_uuid})
            response = add_cms_users(
                kanna_user_internal_default,
                cms_data["uuid"],
                data={"userUuid": user_uuid, "role": "ADMIN"},
            )
            cms_user_kanna_bulk.append(
                CmsUserKanna(
                    cms_uuid=cms_data["uuid"],
                    user_uuid=user_uuid,
                )
            )
            notification_email_setting_bulk.append(
                NotificationEmailSetting(
                    cms_uuid=cms_data["uuid"],
                    user_uuid=user_uuid,
                )
            )
            logger.info(
                {
                    "STATUS": "SUCCESS",
                    "email": response.get("cmsUser", {})
                    .get("user", {})
                    .get("email", "None"),
                    "fullName": response.get("cmsUser", {})
                    .get("user", {})
                    .get("fullName", "None"),
                    "uuid": response.get("cmsUser", {})
                    .get("user", {})
                    .get("uuid", "None"),
                }
            )
        except Exception as e:
            logger.exception(e)
            continue
    CmsUserKanna.objects.bulk_create(cms_user_kanna_bulk)
    NotificationEmailSetting.objects.bulk_create(notification_email_setting_bulk)


def _fetch_cms_simple(
    cms_uuid: str, kanna_user: UserKanna, cms_uuids: List[str]
) -> tuple[int, Any]:
    """
    Fetch CMS details with simple error handling.

    Args:
        cms_uuid (str): The CMS UUID to fetch
        kanna_user (UserKanna): The Kanna user for API authentication
        cms_uuids (List[str]): Original list of UUIDs to maintain index

    Returns:
        tuple[int, Any]: Tuple of (index, result) where index is the position
                        in the original list and result is the CMS data or empty dict
    """
    try:
        logger.info(f"Fetching CMS {cms_uuid}")
        result = get_cms_kanna(cms_uuid, kanna_user)

        if (
            result
            and not isinstance(result, dict)
            or (isinstance(result, dict) and "error" not in result)
        ):
            logger.info(f"Successfully fetched CMS {cms_uuid}")
            return cms_uuids.index(cms_uuid), result
        else:
            logger.warning(f"Failed to fetch CMS {cms_uuid}")
            return cms_uuids.index(cms_uuid), {}

    except Exception as e:
        logger.error(f"Exception while fetching CMS {cms_uuid}: {str(e)}")
        return cms_uuids.index(cms_uuid), {}


def _process_completed_futures_simple(
    future_to_uuid: dict, cms_uuids: List[str]
) -> List[Any]:
    """
    Process completed futures and collect results maintaining order.

    Args:
        future_to_uuid (dict): Mapping of futures to UUIDs
        cms_uuids (List[str]): Original list of UUIDs

    Returns:
        List[Any]: List of CMS results in original order
    """
    results = [{}] * len(cms_uuids)  # Default to empty dict for failed requests

    for future in as_completed(future_to_uuid):
        try:
            index, result = future.result()
            if result:  # Only update if we got valid data
                results[index] = result

        except Exception as e:
            uuid = future_to_uuid[future]
            logger.error(f"Exception in thread for CMS {uuid}: {str(e)}")
            # Keep default empty dict for this index

    return results


def _execute_parallel_cms_fetch_simple(
    cms_uuids: List[str], kanna_user: UserKanna
) -> List[Any]:
    """
    Execute parallel CMS fetching using ThreadPoolExecutor with simple error handling.

    Args:
        cms_uuids (List[str]): List of CMS UUIDs to fetch
        kanna_user (UserKanna): The Kanna user for API authentication

    Returns:
        List[Any]: List of CMS results in original order, empty dict for failed requests
    """
    with ThreadPoolExecutor(max_workers=CMS_API_MAX_WORKERS) as executor:
        # Submit all tasks
        future_to_uuid = {
            executor.submit(
                _fetch_cms_simple, cms_uuid, kanna_user, cms_uuids
            ): cms_uuid
            for cms_uuid in cms_uuids
        }

        # Process completed futures
        results = _process_completed_futures_simple(future_to_uuid, cms_uuids)

        logger.info(f"Completed fetching details for {len(cms_uuids)} CMS records")
        return results


def _fetch_cms_page(user: UserKanna, page: int, cms_user_uuid=None):
    """
    Fetch a single page of CMS data.

    Args:
        user (UserKanna): The Kanna user for API authentication
        page (int): Page number to fetch
        cms_user_uuid: Optional CMS user UUID filter

    Returns:
        tuple[int, list]: Tuple of (total_page, cms_list) or (total_page, []) if failed
    """
    try:
        logger.info(f"Fetching CMS page {page}")
        response = KaRequests(user).get(
            "/v1/cms",
            params={
                "page": page,
                "limit": constants.KANNA_LIST_LIMIT,
                "cmsUserUuid": cms_user_uuid,
            },
        )
        response_data = response.json()
        total_page = int(response.headers.get("total_page"))
        cms_list = response_data.get("constructionManagements", [])

        # Fix industryCategories field name
        for cms_data in cms_list:
            cms_data["industryCategories"] = cms_data.pop("industoryCategories")

        logger.info(f"Successfully fetched CMS page {page} with {len(cms_list)} items")
        return total_page, cms_list

    except Exception as e:
        logger.error(f"Failed to fetch CMS page {page}: {str(e)}")
        return page, []


def get_cms_list_parallel(user: UserKanna):
    """
    This function optimizes CMS list fetching by:
    1. First call to get total_page from the first page
    2. If total_page > 2, use multi-threading to fetch remaining pages in parallel
    3. Limit to 10 concurrent threads to avoid overwhelming the API

    Args:
        user (UserKanna): The Kanna user for API authentication
        cms_user_uuid: Optional CMS user UUID filter

    Returns:
        list: Combined list of all CMS data from all pages
    """
    cms_user_uuid = None
    if user.company and user.company.type in [CompanyType.SHOP, CompanyType.DEALER]:
        cms_user_uuid = user.user_uuid
    total_page, cms_list = _fetch_cms_page(
        user=user, page=constants.KANNA_LIST_FIRST_PAGE, cms_user_uuid=cms_user_uuid
    )
    logger.info(f"Fetched first page. Total pages: {total_page}")

    # If only 1 page, return immediately
    if total_page <= 1:
        return cms_list

    # If total_page > 2, use parallel fetching for remaining pages
    if total_page > 2:
        logger.info(f"Using parallel fetching for {total_page - 1} remaining pages")

        # Create list of pages to fetch (excluding page 1 which we already have)
        pages_to_fetch = list(range(2, total_page + 1))

        # Use ThreadPoolExecutor to fetch pages in parallel
        with ThreadPoolExecutor(max_workers=CMS_API_MAX_WORKERS) as executor:
            # Submit all page fetching tasks
            future_to_page = {
                executor.submit(_fetch_cms_page, user, page, cms_user_uuid): page
                for page in pages_to_fetch
            }

            for future in as_completed(future_to_page):
                page, cms_page_data = future.result()
                if cms_page_data:
                    cms_list.extend(cms_page_data)
    else:
        # For 2 pages total, fetch page 2 sequentially
        logger.info("Fetching page 2 sequentially")
        page, cms_page_data = _fetch_cms_page(user, 2, cms_user_uuid)
        if cms_page_data:
            cms_list.extend(cms_page_data)

    logger.info(f"Completed fetching all pages. Total CMS items: {len(cms_list)}")
    return cms_list


def get_cms_list_detail(cms_uuids: List[str], kanna_user: UserKanna) -> List[Any]:
    """
    Get detailed CMS information for multiple UUIDs using parallel processing.

    This function uses ThreadPoolExecutor to fetch CMS details concurrently,
    maintaining the original order of the input UUIDs. Failed requests return empty dict.

    Args:
        cms_uuids (List[str]): List of CMS UUIDs to fetch details for
        kanna_user (UserKanna): The Kanna user for API authentication

    Returns:
        List[Any]: List of CMS details in the same order as input UUIDs,
                  empty dict for failed requests
    """
    if not cms_uuids:
        return []

    try:
        return _execute_parallel_cms_fetch_simple(cms_uuids, kanna_user)
    except Exception as e:
        logger.error(f"Exception in get_cms_list_detail: {str(e)}")
        return [{}] * len(cms_uuids)  # Return empty dicts for all requests


def fetch_cms_data_map(
    cms_uuids: List[str], kanna_user: UserKanna
) -> Dict[uuid.UUID, Any]:
    """
    Fetch CMS data for a list of UUIDs and return a mapping from UUID to CMS data.

    Args:
        cms_uuids (List[str]): A list of CMS UUIDs to fetch data for.
        kanna_user (UserKanna): Authenticated user context used for CMS API access.

    Returns:
        Dict[str, Any]: A dictionary mapping each CMS UUID to its corresponding CMS data.
                        If a UUID has no data, it is excluded from the result.
    """
    if not cms_uuids:
        return {}

    if len(cms_uuids) > CMS_API_MAX_WORKERS:
        cms_data_list = get_cms_list_parallel(user=kanna_user)
    else:
        cms_data_list = get_cms_list_detail(cms_uuids, kanna_user)
    cms_data_map = {}
    cms_uuid_str_list = [str(cms_uuid) for cms_uuid in cms_uuids]
    for cms_data in cms_data_list:
        if not cms_data:
            continue
        try:
            cms_uuid = uuid.UUID(cms_data["uuid"])
        except ValueError:
            logger.error(f"Invalid get CMS UUID: {cms_data}")
            continue
        if cms_data["uuid"] in cms_uuid_str_list:
            cms_data_map[cms_uuid] = cms_data
    return cms_data_map
