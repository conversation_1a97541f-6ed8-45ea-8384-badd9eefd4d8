import logging
import threading

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q, TextField
from django.db.models.functions import Cast
from rest_framework import generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from approve_flow.models import ApproveFlowSettingInstance
from cms.models import CmsUserKanna
from companies.models import CompanyType
from dashboard.utils import get_management_sheets_by_user
from diagnostics.models import Diagnostic
from diagnostics.serializers import (
    DiagnosticApproveHistorySerializer,
    DiagnostiInfoApproveByConstructionSerializer,
)
from kanna_api.customized import get_customized_cms
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.cms import CmsParam
from management_sheets.choices import ManagementSheetStatus
from management_sheets.models import ManagementSheet
from management_sheets.serializers import (
    ManagementSheetApproveByConstructionSerializer,
)
from utilities.exceptions.api_exception import UserWhitelistEmailError
from utilities.json_convert.convert import (
    camel_to_snake_json,
    snake_to_camel_json,
)
from utilities.order_filtering.ordering import Ordering
from utilities.permissions.custom_permission import HasKannaPermission
from warranty.models import Warranty
from warranty.serializers import WarrantyByConstructionSerializer
from .cms import cms_filter, create_cms, get_cms, get_cms_list, update_cms
from .serializers import (
    CmsCreateRequestSerializer,
    CmsDetailRequestSerializer,
    CmsListSerializer,
    CmsSerializer,
    CmsUpdateRequestSerializer,
)
from .services import (
    get_cms_customize,
    get_property_customize,
    update_user_assign_cms,
    get_cms_related_member_uuids,
    link_related_members_to_construction,
)

logger = logging.getLogger(__name__)


class CmsViewSet(viewsets.GenericViewSet):
    permission_classes = [HasKannaPermission]
    queryset = None

    def get_serializer_class(self):
        if self.action in ["list", "construction_list_shop", "construction_list"]:
            return CmsListSerializer
        if self.action == "update":
            return CmsUpdateRequestSerializer
        if self.action == "retrieve":
            return CmsDetailRequestSerializer
        if self.action == "create":
            return CmsCreateRequestSerializer
        if self.action == "diagnotic_approve_history":
            return DiagnosticApproveHistorySerializer

        return CmsSerializer

    # @swagger_auto_schema(manual_parameters=CmsParam.yasg_params())
    def list(self, request, *args, **kwargs):
        """
        案件一覧
        """
        params = CmsParam(**request.query_params.dict())
        # cms取得
        kanna_user = UserKanna.objects.get(email=request.user.email)
        if kanna_user:
            update_user_assign_cms(kanna_user)
        cms_list = get_cms_list(
            user=kanna_user,
            cms_user_uuid=kanna_user.user_uuid
            if kanna_user.company.type == CompanyType.SHOP
            or kanna_user.company.type == CompanyType.DEALER
            else None,
        )
        cms_list_data = camel_to_snake_json(cms_list)
        for cms in cms_list_data:
            get_cms_customize(cms)
            cms["number_of_management_sheet"] = ManagementSheet.objects.filter(
                cms_uuid=cms["uuid"],
                is_active=True,
            ).count()

            if cms["number_of_management_sheet"] != 0:
                cms["number_of_diagnostic"] = Diagnostic.objects.filter(
                    cms_uuid=cms["uuid"],
                ).count()
                cms["number_of_warranty"] = Warranty.objects.filter(
                    management_sheet__cms_uuid=cms["uuid"],
                ).count()
            # Construction no
            management_sheet = (
                ManagementSheet.objects.filter(
                    cms_uuid=cms["uuid"],
                    is_active=True,
                )
                .exclude(construction_no__exact="")
                .first()
            )
            cms["construction_no"] = ""
            if management_sheet is not None:
                cms["construction_no"] = management_sheet.construction_no

        # クエリパラメータでフィルター
        cms_list_data = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=[],
        )

        page = self.paginate_queryset(cms_list_data)
        if page is not None:
            serialize = self.get_serializer(
                page, many=True, context={"request": request}
            )
            return self.get_paginated_response(serialize.data)
        return Response(
            self.serialize(
                cms_list_data,
                many=True,
                context={
                    "request": request,
                },
            ).data,
            200,
        )

    # Filter construction list by shop, dealer, act
    # @swagger_auto_schema(manual_parameters=CmsParam.yasg_params())
    @action(
        detail=False,
        methods=["get"],
        url_path="construction_list",  # noqa
    )
    def construction_list(self, request, *args, **kwargs):
        """
        案件一覧
        """
        params = CmsParam(**request.query_params.dict())
        # cms取得
        kanna_user = UserKanna.objects.get(email=request.user.email)
        if kanna_user:
            update_user_assign_cms(kanna_user)
        cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)

        cms_list_data = camel_to_snake_json(cms_list)

        list_management_ids = get_management_sheets_by_user(kanna_user)
        cms_uuid_list_filter = (
            ManagementSheet.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(id__in=list_management_ids)
        )
        
        # クエリパラメータでフィルター
        cms_list_data = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=list(cms_uuid_list_filter),
        )

        for cms in cms_list_data:
            get_cms_customize(cms)
            cms["number_of_management_sheet"] = ManagementSheet.objects.filter(
                cms_uuid=cms["uuid"],
                is_active=True,
            ).count()

            if cms["number_of_management_sheet"] != 0:
                cms["number_of_diagnostic"] = Diagnostic.objects.filter(
                    cms_uuid=cms["uuid"],
                ).count()
                cms["number_of_warranty"] = Warranty.objects.filter(
                    management_sheet__cms_uuid=cms["uuid"],
                ).count()
            # Construction no
            management_sheet = (
                ManagementSheet.objects.filter(
                    cms_uuid=cms["uuid"],
                    is_active=True,
                )
                .exclude(construction_no__exact="")
                .first()
            )
            cms["construction_no"] = ""
            if management_sheet is not None:
                cms["construction_no"] = management_sheet.construction_no

        serializer = self.get_serializer(
            cms_list_data, many=True, context={"request": request}
        )
        params = self.request.query_params.get("sort_by", None)
        sort_by_list = {
            "construction_no",
            "title",
            "postcode",
            "prefecture",
            "started_on",
            "finished_on",
            "name",
            "number_of_diagnostic",
            "number_of_warranty",
            "number_of_management_sheet",
        }
        list_combine = []
        params_direction = self.request.query_params.get("order_by_direction", None)
        for data in serializer.data:
            if data["owner_information"] != []:
                data["name"] = (
                        data["owner_information"][0]["last_name"]
                        + " "
                        + data["owner_information"][0]["first_name"]
                )
            if data["property"] is not None:
                data["postcode"] = data["property"]["postcode"]
                data["prefecture"] = data["property"]["prefecture"]
        result = Ordering.order_filtering(
            serializer.data,
            params,
            params_direction,
            sort_by_list,
            list_combine,
            False,
        )
        for data in result:
            if "name" in data:
                del data["name"]
            if "postcode" in data:
                del data["postcode"]
            if "prefecture" in data:
                del data["prefecture"]
        page = self.paginate_queryset(result)
        if page is not None:
            return self.get_paginated_response(page)
        return Response(
            result,
            200,
        )

    def retrieve(self, request, pk, *args, **kwargs):
        """
        案件詳細
        """
        # cms取得
        try:
            kanna_user = UserKanna.objects.get(email=request.user.email)
            cms = get_cms(kanna_user, pk)
            if not cms:
                return Response(
                    {"error": "cms not found"}, status=status.HTTP_404_NOT_FOUND
                )
            cms_data = camel_to_snake_json(cms)
            # カスタマイズ項目取得
            get_cms_customize(cms_data)
            # 案件詳細に物件情報がある場合カスタマイズ項目取得
            if cms_data_propery := cms_data.get("property"):
                get_property_customize(cms_data_propery)
            # 案件作成者を追加
            cms_data["created_by"] = cms_data.get("registered_user").get("full_name")
            serializer = self.get_serializer(data=cms_data)
            serializer.is_valid(raise_exception=True)
            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    def create(self, request, *args, **kwargs):
        try:
            kanna_user_request = UserKanna.objects.get(email=request.user.email)
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            kanna_user_internal_default = (
                UserKanna.objects.filter(
                    user__email=settings.USER_KANNA_EMAIL_TO_CREATE_CMS
                )
                .order_by("-last_login_at")
                .first()
            )
            data = snake_to_camel_json(serializer.data)
            related_member_uuids = get_cms_related_member_uuids(kanna_user_request=kanna_user_request)
            cms = create_cms(kanna_user_internal_default, data)
            cms_data = camel_to_snake_json(cms)
            threading.Thread(
                target=link_related_members_to_construction,
                args=(cms_data, related_member_uuids)
            ).start()
            return Response(data=cms_data)

        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    def update(self, request, pk, *args, **kwargs):
        """
        案件更新
        """
        # リクエストデータ取得
        try:
            serializer = CmsUpdateRequestSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            data = snake_to_camel_json(serializer.data)
            kanna_user = UserKanna.objects.get(email=request.user.email)
            # カスタマイズ項目設定
            customized_items = []
            customized_cms = get_customized_cms(kanna_user)
            for item in customized_cms.get("customizedInputItems", []):
                label = item.get("label", "")
                key = item["customizedInputKey"]
                if label == "工事コード" and data.get("industryCode"):
                    customized_items.append(
                        {
                            "customizedInputKey": key,
                            "values": [data.pop("industryCode", "")],
                        }
                    )

                elif label == "業態" and data.get("businessCategory"):
                    customized_items.append(
                        {
                            "customizedInputKey": key,
                            "values": [data.pop("businessCategory", "")],
                        }
                    )

                elif label == "営業所" and data.get("businessOffice"):
                    customized_items.append(
                        {
                            "customizedInputKey": key,
                            "values": [data.pop("businessOffice", "")],
                        }
                    )

            data["customizedItems"] = customized_items
            # cms取得
            cms = update_cms(kanna_user, pk, data)
            cms_data = camel_to_snake_json(cms)
            # カスタマイズ項目取得
            get_cms_customize(cms_data)
            serializer = self.get_serializer(data=cms_data)
            serializer.is_valid(raise_exception=True)
            return Response(data=serializer.data)
        except ObjectDoesNotExist:
            raise UserWhitelistEmailError

    @action(
        detail=False,
        methods=["get"],
        url_path="construction_list_shop",  # noqa
    )
    def construction_list_shop(self, request, *args, **kwargs):

        kanna_user = UserKanna.objects.get(email=request.user.email)
        if kanna_user:
            update_user_assign_cms(kanna_user)
        company = kanna_user.company

        cms_user_current = list(
            CmsUserKanna.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(Q(user_uuid=kanna_user.user_uuid))
        )

        management_sheet_ids = (
            ApproveFlowSettingInstance.objects.filter(
                Q(shop=company) | Q(dealer=company) | Q(act=company)
            )
            .values_list("management_sheet_id")
            .all()
        )

        management_sheet_ids_distinct = (
            ManagementSheet.objects.values_list("id", flat=True)
            .filter(
                Q(id__in=(management_sheet_ids))
                | Q(diagnostic_management_sheet__company=company),
                Q(cms_uuid__in=cms_user_current),
            )
            .distinct()
            .all()
        )

        management_sheet_cms_uuids = (
            ManagementSheet.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(id__in=management_sheet_ids_distinct)
        )
        params = CmsParam(**request.query_params.dict())
        cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)
        cms_list_data = camel_to_snake_json(cms_list)
        for cms in cms_list_data:
            get_cms_customize(cms)
        cms_list_data = cms_filter(
            request.user,
            params=params,
            cms_list_data=cms_list_data,
            on_user_cms_uuid_list=[],
            not_on_user_cms_uuid_list=[],
            not_on_group_cms_uuid_list=[],
            cms_uuid_list=list(management_sheet_cms_uuids),
        )

        serializer = self.get_serializer(
            cms_list_data, many=True, context={"request": request}
        )
        params = self.request.query_params.get("sort_by", None)
        sort_by_list = {
            "construction_no",
            "title",
            "postcode",
            "prefecture",
            "started_on",
            "finished_on",
            "name",
            "number_of_diagnostic",
            "number_of_warranty",
            "number_of_management_sheet",
        }
        list_combine = []
        params_direction = self.request.query_params.get("order_by_direction", None)
        for data in serializer.data:
            if data["owner_information"] != []:
                data["name"] = (
                    data["owner_information"][0]["last_name"]
                    + " "
                    + data["owner_information"][0]["first_name"]
                )
            if data["property"] is not None:
                data["postcode"] = data["property"]["postcode"]
                data["prefecture"] = data["property"]["prefecture"]
        result = Ordering.order_filtering(
            serializer.data,
            params,
            params_direction,
            sort_by_list,
            list_combine,
            False,
        )
        for data in result:
            if "name" in data:
                del data["name"]
            if "postcode" in data:
                del data["postcode"]
            if "prefecture" in data:
                del data["prefecture"]

        page = self.paginate_queryset(result)
        if page is not None:
            return self.get_paginated_response(page)
        return Response(
            result,
            200,
        )


class GetDiagnocticByConstructionAPIView(generics.GenericAPIView):
    permission_classes = [HasKannaPermission]
    serializer_class = DiagnostiInfoApproveByConstructionSerializer

    @action(
        detail=True,
        methods=["get"],
        url_path="(?P<pk>\d+)/diagnotic-approve",  # noqa
    )
    def get(self, request, pk, *args, **kwargs):
        diagnotics = Diagnostic.objects.filter(Q(cms_uuid=pk))
        diagnotics_construction = self.get_serializer(diagnotics, many=True)
        return Response(
            data={
                "data": {
                    "diagnotics_construction": diagnotics_construction.data,
                }
            },
            status=status.HTTP_200_OK,
        )


class GetManagementSheetByConstructionAPIView(generics.GenericAPIView):
    permission_classes = [HasKannaPermission]
    serializer_class = ManagementSheetApproveByConstructionSerializer

    @action(
        detail=True,
        methods=["get"],
        url_path="(?P<pk>\d+)/management-sheet-approve",  # noqa
    )
    def get(self, request, pk, *args, **kwargs):
        management_sheet = ManagementSheet.objects.filter(Q(cms_uuid=pk))
        management_sheet_construction = self.get_serializer(management_sheet, many=True)
        return Response(
            data={
                "data": {
                    "management_sheet_construction": management_sheet_construction.data,
                }
            },
            status=status.HTTP_200_OK,
        )


class WarrantyGetByConstructionAPIView(generics.GenericAPIView):
    permission_classes = [HasKannaPermission]
    serializer_class = WarrantyByConstructionSerializer

    @action(
        detail=True,
        methods=["get"],
        url_path="(?P<pk>\d+)/warranty-approve",  # noqa
    )
    def get(self, request, pk, *args, **kwargs):
        management_sheet = ManagementSheet.objects.filter(
            Q(cms_uuid=pk), ~Q(status=ManagementSheetStatus.TEMP)
        ).first()
        warranty = []
        if management_sheet:
            warranty = Warranty.objects.filter(management_sheet_id=management_sheet)

        warranty_construction = self.get_serializer(warranty, many=True)
        return Response(
            data={
                "data": {
                    "warranty_construction": warranty_construction.data,
                }
            },
            status=status.HTTP_200_OK,
        )


class OwnerInfoByConstructionAPIView(generics.GenericAPIView):
    permission_classes = [HasKannaPermission]

    @action(
        detail=True,
        methods=["get"],
        url_path="(?P<pk>\d+)/owner-information",  # noqa
    )
    def get(self, request, pk, *args, **kwargs):
        construction_owner = ""
        building_name = ""
        sds_cmd_uuid = Diagnostic.objects.filter(cms_uuid=pk).first()
        if sds_cmd_uuid:
            construction_owner = sds_cmd_uuid.construction_owner
            building_name = sds_cmd_uuid.building_name

        return Response(
            data={
                "construction_owner": construction_owner,
                "building_name": building_name,
            },
        )
