from datetime import datetime

from api import constants
from kanna_api.choices.project_status import ProjectStatus
from kanna_api.common import KaRequests
from kanna_api.models import UserKanna
from kanna_api.query_params.cms import CmsParam
from utilities.kanna_api.compare import contain_ja
from utilities.kanna_api.multi_filter import MultiFilter


def get_cms_list(user: UserK<PERSON>, cms_user_uuid=None):
    """
    案件一覧取得
    """
    response = KaRequests(user).get(
        "/v1/cms",
        params={
            "page": constants.KANNA_LIST_FIRST_PAGE,
            "limit": constants.KANNA_LIST_LIMIT,
            "cmsUserUuid": cms_user_uuid,
        },
    )
    response_data = response.json()
    total_page = response.headers.get("total_page")
    cms = response_data.get("constructionManagements")

    for page in range(2, int(total_page) + 1):
        res = KaRequests(user).get(
            "/v1/cms",
            params={
                "page": page,
                "limit": constants.KANNA_LIST_LIMIT,
                "cmsUserUuid": cms_user_uuid,
            },
        )
        res_data = res.json()
        cms += res_data.get("constructionManagements")
    for cms_data in cms:
        cms_data["industryCategories"] = cms_data.pop("industoryCategories")

    return cms


def get_cms(user: UserKanna, cms_uuid: str) -> dict:
    """
    案件詳細取得
    """
    try:
        response = KaRequests(user).get(f"/v1/cms/{cms_uuid}")
    except Exception:
        return {}
    response_data = response.json()
    cms = response_data.get("constructionManagement")
    cms["industryCategories"] = cms.pop("industoryCategories")
    return cms


def update_cms(user: UserKanna, cms_uuid: str, cms_data: dict) -> dict:
    """
    案件更新
    """
    response = KaRequests(user).patch(f"/v1/cms/{cms_uuid}", json=cms_data)
    response_data = response.json()
    cms = response_data.get("constructionManagement")
    cms["industryCategories"] = cms.pop("industoryCategories")
    return cms


def create_cms(user: UserKanna, cms_data: dict) -> dict:
    """Create Project from Kanna

    Args:
        user (UserKanna): user kanna
        cms_data (dict): data for create project

    Returns:
        dict: response from Kanna
    """
    response = KaRequests(user).post("/v1/cms", json=cms_data)
    response_data = response.json()
    cms = response_data.get("constructionManagement")
    cms["industryCategories"] = cms.pop("industoryCategories")
    return cms


def cms_filter(
    user,
    params: CmsParam,
    cms_list_data: list,
    on_user_cms_uuid_list: list,
    not_on_user_cms_uuid_list: list,
    not_on_group_cms_uuid_list: list,
    cms_uuid_list: list,
) -> list:
    multi_filter = MultiFilter()
    from users.models import UserConstruction

    # 案件名
    if params.title:
        multi_filter.add(lambda cms: contain_ja(params.title, cms.get("title", "")))

    # 担当者フィルター
    if params.on_user_uuid:
        multi_filter.add(
            lambda on_user_cms_uuid: on_user_cms_uuid.get("uuid")
            in on_user_cms_uuid_list
        )

    if params.not_on_user_uuid:
        multi_filter.add(
            lambda not_on_user_cms_uuid: not_on_user_cms_uuid.get("uuid")
            not in not_on_user_cms_uuid_list
        )

    # 担当グループフィルター
    if params.not_on_group_id:
        multi_filter.add(
            lambda not_on_group_cms_uuid: not_on_group_cms_uuid.get("uuid")
            not in not_on_group_cms_uuid_list
        )
    # 工事コード
    if params.industry_code:
        multi_filter.add(
            lambda cms: params.industry_code in cms.get("industry_code", "")
        )

    # 工事コード
    if params.construction_no:
        multi_filter.add(
            lambda cms: params.construction_no in cms.get("construction_no", "")
        )

    # 工事フロー
    if params.project_status:
        multi_filter.add(
            lambda cms: cms.get("project_status", "") == params.project_status.value
        )
    if params.is_available_for_sds:
        multi_filter.add(
            lambda cms: cms.get("project_status", "")
            in [
                ProjectStatus.BEFORE_CONSTRUCTION,
                ProjectStatus.BEFORE_CONTRACT,
                ProjectStatus.BEFORE_QUOTATION,
                ProjectStatus.IN_PROGRESS_CONSTRUCTION,
                ProjectStatus.RECEPTION,
            ]
        )
    elif params.project_status:
        multi_filter.add(
            lambda cms: cms.get("project_status", "") == params.project_status.value
        )
    # 着工日(開始)
    if params.started_on_from:
        multi_filter.add(
            lambda cms: cms.get("started_on")
            and params.started_on_from
            <= datetime.strptime(cms.get("started_on"), "%Y-%m-%d").date(),
        )

    # 着工日(終了)
    if params.started_on_to:
        multi_filter.add(
            lambda cms: cms.get("started_on")
            and params.started_on_to
            >= datetime.strptime(cms.get("started_on"), "%Y-%m-%d").date(),
        )

    # 完了日(開始)
    if params.finished_on_from:
        multi_filter.add(
            lambda cms: cms.get("finished_on")
            and params.finished_on_from
            <= datetime.strptime(cms.get("finished_on"), "%Y-%m-%d").date(),
        )

    # 完了日(終了)
    if params.finished_on_to:
        multi_filter.add(
            lambda cms: cms.get("finished_on")
            and params.finished_on_to
            >= datetime.strptime(cms.get("finished_on"), "%Y-%m-%d").date(),
        )

    if params.created_at_from:
        multi_filter.add(
            lambda cms: cms.get("created_at")
            and params.created_at_from
            <= datetime.strptime(cms.get("created_at"), "%Y-%m-%dT%H:%M:%SZ").date(),
        )

    if params.created_at_to:

        multi_filter.add(
            lambda cms: cms.get("created_at")
            and params.created_at_to
            >= datetime.strptime(cms.get("created_at"), "%Y-%m-%dT%H:%M:%SZ").date(),
        )

    if params.owner_id:
        cms_by_owner = UserConstruction.objects.values_list(
            "construction_uuid", flat=True
        ).filter(owner=params.owner_id)
        multi_filter.add(lambda cms: cms.get("uuid", "") in list(cms_by_owner))

    if params.client_uuid:
        multi_filter.add(
            lambda cms: params.client_uuid in cms.get("client", {}).get("uuid", "")
        )

    if cms_uuid_list:
        multi_filter.add(lambda cms: cms.get("uuid", "") in cms_uuid_list)

    if params.property_uuid:
        multi_filter.add(
            lambda cms: params.property_uuid in cms.get("property", {}).get("uuid", "")
        )

    cms_list_data = multi_filter.run(cms_list_data)

    return cms_list_data
