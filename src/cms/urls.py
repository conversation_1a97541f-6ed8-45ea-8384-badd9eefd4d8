from rest_framework.routers import DefaultRouter

from django.urls import include, path
from django.urls import re_path as url

from .views import (
    CmsViewSet,
    GetDiagnocticByConstructionAPIView,
    GetManagementSheetByConstructionAPIView,
    OwnerInfoByConstructionAPIView,
    WarrantyGetByConstructionAPIView,
)

router = DefaultRouter()
router.register(r"", CmsViewSet, basename="cms")

urlpatterns = [
    url(r"^", include(router.urls)),
    path(
        "<str:pk>/diagnotic-approve/",
        GetDiagnocticByConstructionAPIView.as_view(),
        name="diagnostic-construction-detail",
    ),
    path(
        "<str:pk>/management-sheet-approve/",
        GetManagementSheetByConstructionAPIView.as_view(),
        name="management-sheet-construction-detail",
    ),
    path(
        "<str:pk>/warranty-approve/",
        WarrantyGetByConstructionAPIView.as_view(),
        name="warranty-construction-detail",
    ),
    path(
        "<str:pk>/owner-information/",
        OwnerInfoByConstructionAPIView.as_view(),
        name="owner-information",
    ),
]
