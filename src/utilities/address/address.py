def ensure_prefecture_city_in_address(prefecture: str, city: str, address: str) -> str:
    """
    This function intelligently combines the prefecture, city, and address
    components to create a complete Japanese address. It avoids duplication
    by checking if the city or prefecture is already present in the address
    before adding them.
    
    The function processes components in the following order:
    1. Start with the address as the base
    2. Add city if not already present
    3. Add prefecture if not already present
    """
    result = address

    if city and city not in result:
        result = city + result

    if prefecture and prefecture not in result:
        result = prefecture + result

    return result
