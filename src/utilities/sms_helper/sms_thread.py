import logging
import re
import threading

import boto3
from botocore.exceptions import ClientError

from django.conf import settings

logger = logging.getLogger(__name__)


class SNS:
    def __init__(self) -> None:
        self.client = boto3.client(
            service_name="sns",
            region_name=settings.AWS_SNS_REGION_NAME,
            aws_access_key_id=settings.AWS_SNS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SNS_SECRET_ACCESS_KEY,
        )

    def __phone_parser(self, phone_number):
        if not phone_number:
            return None

        def format_e164(number, country_code):
            return f"+{country_code}{number}"

        def is_valid_jp_phone_number(number):
            return bool(re.match(r'^0\d{9,10}$', number))

        country_code = "81"  # country_code JP
        parsed_number = re.sub(r'\D', '', phone_number)

        if not is_valid_jp_phone_number(parsed_number):
            raise ValueError("Phone number is invalid")

        parsed_number = re.sub(r'^0', '', parsed_number)

        return format_e164(parsed_number, country_code)

    def publish_message(self, message, phone_number):
        try:
            phone_number = self.__phone_parser(phone_number)
            if not phone_number:
                return
            logger.info(
                "\nPublish message:\n'%s' to phone number '%s'",message, phone_number
            )
            response = self.client.publish(
                PhoneNumber=phone_number,
                Message=message,
            )
            logger.info(
                "\nResponse publish message: %s", str(response)
            )
            return response
        except ClientError as error:
            logger.exception(
                "Couldn't publish message '%s' to phone number '%s'",
                message,
                phone_number,
            )
            raise error


class _SMSThread(threading.Thread):
    def __init__(self, sms: SNS, message: str, phone_number):
        self.sms = sms
        self.message = message
        self.phone_number = phone_number
        threading.Thread.__init__(self)

    def run(self):
        self.sms.publish_message(self.message, self.phone_number)


def send(message, phone_number):
    sms = SNS()
    _SMSThread(sms, message, phone_number).start()
