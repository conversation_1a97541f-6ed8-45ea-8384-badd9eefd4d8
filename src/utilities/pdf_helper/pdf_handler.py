# from io import Bytes<PERSON>

# from xhtml2pdf import pisa
import os
from io import Bytes<PERSON>
from logging import getLogger
from urllib.parse import urljoin

import pdfkit
import PyPDF2

from django.conf import settings
from django.template.loader import render_to_string

from api.constants import PDF_WARRANTY_DIR, PDF_WARRANTY_SUB_DIR

logger = getLogger(__name__)


def watermark_wrapper_by_pdf(pdf):
    try:
        watermark_file = settings.STATIC_ROOT + "pdf/watermark.pdf"
        pdf = BytesIO(pdf)
        reader = PyPDF2.PdfReader(watermark_file)
        watermark_page = reader.pages[0]
        writer = PyPDF2.PdfWriter()
        reader = PyPDF2.PdfReader(pdf)
        page_indices = list(range(0, len(reader.pages)))
        for index in page_indices:
            content_page = reader.pages[index]
            mediabox = content_page.mediabox
            content_page.merge_page(watermark_page)
            content_page.mediabox = mediabox
            writer.add_page(content_page)

        pdf_add_watermark = BytesIO()
        writer.write(pdf_add_watermark)
        return pdf_add_watermark.getvalue()
    except Exception as err:
        logger.exception(f"Error adding watermark: {err}")
        return pdf


def render_to_pdf(
    template_src, context_dict={}, watermark=False, page_size="A4", dpi=400
):
    html = render_to_string(template_src, context_dict)
    try:
        pdf = pdfkit.from_string(
            html,
            False,
            options={
                "page-size": page_size,
                "dpi": dpi,
                "encoding": "utf-8",
                "enable-local-file-access": True,
            },
        )
        if watermark is True:
            pdf = watermark_wrapper_by_pdf(pdf)
        return pdf
    except Exception as e:
        logger.exception(f"Error rendering PDF: {e}")
        return None


def file_path_to_url(file_name):
    """
    converts an absolute native path to a FILE URL.

    Parameters
    ----------
    path : a path in native format

    Returns
    -------
    a valid FILE URL
    """
    url = urljoin(settings.API_URL, PDF_WARRANTY_DIR + file_name)
    return url


def render_to_pdf_agc(
    template_src, file_name, context_dict={}, watermark=False, page_size="A4", dpi=400
):
    html = render_to_string(template_src, context_dict)
    path = os.path.join(settings.STATIC_ROOT, PDF_WARRANTY_SUB_DIR, file_name)
    pdfkit.from_string(
        html,
        path,
        options={
            "page-size": page_size,
            "dpi": dpi,
            "encoding": "utf-8",
            "enable-local-file-access": True,
        },
    )

    return file_path_to_url(file_name)
