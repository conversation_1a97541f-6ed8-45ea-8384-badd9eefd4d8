import json
import re
from dataclasses import dataclass
from logging import getLogger
from typing import Optional

import requests

logger = getLogger(__name__)


@dataclass
class CityAddress:
    city: str
    prefecture: str = None


def is_valid_japanese_zipcode(zipcode: str) -> bool:
    """
    Check if the zipcode is a valid Japanese postal code format

    Args:
        zipcode (str): Postal code to validate

    Returns:
        bool: True if valid, False otherwise
    """
    if not zipcode:
        return False

    # Remove any whitespace and the Japanese postal mark '〒'
    zipcode = zipcode.strip().replace("〒", "").replace(" ", "")

    # Check for common formats: 7 digits (1234567) or with hyphen (123-4567)
    pattern = r"^(\d{3}-\d{4}|\d{7})$"
    return bool(re.match(pattern, zipcode))


def zipcode_to_address_by_yubinbango(zipcode: str) -> Optional[CityAddress]:
    """
    Get city information from a Japanese zipcode

    Args:
        zipcode (str): Japanese postal code (7 digits)

    Returns:
        dict: Dictionary containing prefecture, city, and town information
    """
    # Ensure zipcode is properly formatted (remove hyphens if present)
    zipcode = zipcode.replace("-", "")

    # Get data from yubinbango API
    url = f"https://yubinbango.github.io/yubinbango-data/data/{zipcode[:3]}.js"
    response = requests.get(url)

    if response.status_code != 200:
        logger.warning(f"Failed to retrieve data for zipcode: {zipcode}")
        return None

    # Parse JSON-like content (the API returns JavaScript, not pure JSON)
    data = response.text
    match = re.search(r"\$yubin\((\{.*\})\);?", data, re.DOTALL)

    if not match:
        logger.warning(f"Could not parse response for zipcode: {zipcode}")
        return None

    zipcode_map = json.loads(match.group(1))
    address_data = zipcode_map.get(zipcode)

    # The address data is an array with prefecture, city, town information
    # Format: [prefecture_id, city_name, town_name]
    if not address_data or len(address_data) < 2:
        logger.warning(f"No data found for zipcode: {zipcode}")
        return None

    return CityAddress(city=address_data[1])


def zipcode_to_address(zipcode: str) -> Optional[CityAddress]:
    """
    Get city information from a Japanese zipcode.
    Uses only yubinbango API.

    Args:
        zipcode (str): Japanese postal code (7 digits)

    Returns:
        Optional[CityAddress]: Object containing prefecture and city information,
                              or None if the method fails
    """
    # Validate zipcode format first
    if not is_valid_japanese_zipcode(zipcode):
        logger.warning(f"Invalid zipcode format: {zipcode}")
        return None

    # Normalize zipcode (remove hyphen)
    zipcode = zipcode.replace("-", "")

    # Use yubinbango API
    return zipcode_to_address_by_yubinbango(zipcode)


def extract_zipcode_from_string(text: str) -> Optional[str]:
    """
    Extract a Japanese zipcode from a string

    Args:
        text (str): Text that may contain a zipcode

    Returns:
        Optional[str]: Extracted zipcode if found, None otherwise
    """
    if not text:
        return None

    # Pattern to match Japanese postal codes in various formats:
    # - 〒123-4567
    # - 〒1234567
    # - 123-4567
    # - 1234567 (when it appears as a standalone number)
    patterns = [
        r"〒\s*(\d{3}-\d{4}|\d{7})",  # With 〒 mark
        r"(\d{3}-\d{4})",  # With hyphen
        r"(?<!\d)(\d{7})(?!\d)",  # 7 digits without surrounding digits
    ]

    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            zipcode = match.group(1).replace(" ", "")
            if is_valid_japanese_zipcode(zipcode):
                return zipcode

    return None
