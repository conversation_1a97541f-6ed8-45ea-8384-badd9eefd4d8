from django.test import TestCase

from utilities.address.address import ensure_prefecture_city_in_address


class AddressTestCase(TestCase):
    """Test cases for Japanese address utility functions."""

    def test_combine_full_address_complete_components(self):
        """Test combine_full_address with all components provided."""
        # Test with all components
        result = ensure_prefecture_city_in_address("東京都", "千代田区", "1-1-1")
        self.assertEqual(result, "東京都千代田区1-1-1")

        result = ensure_prefecture_city_in_address("大阪府", "大阪市", "中央区1-1-1")
        self.assertEqual(result, "大阪府大阪市中央区1-1-1")

        result = ensure_prefecture_city_in_address("神奈川県", "横浜市", "西区みなとみらい2-2-1")
        self.assertEqual(result, "神奈川県横浜市西区みなとみらい2-2-1")

    def test_combine_full_address_with_duplicate_city(self):
        """Test combine_full_address when city is already in address."""
        # Test when city is already in address
        result = ensure_prefecture_city_in_address("東京都", "千代田区", "千代田区1-1-1")
        self.assertEqual(result, "東京都千代田区1-1-1")

        result = ensure_prefecture_city_in_address("大阪府", "大阪市", "大阪市中央区1-1-1")
        self.assertEqual(result, "大阪府大阪市中央区1-1-1")

        result = ensure_prefecture_city_in_address("大阪府", "高槻市", "南松原町1-1-1")
        self.assertEqual(result, "大阪府高槻市南松原町1-1-1")

        result = ensure_prefecture_city_in_address("新潟県", "佐渡市", "相川下戸村1-1-1")
        self.assertEqual(result, "新潟県佐渡市相川下戸村1-1-1")

    def test_combine_full_address_with_duplicate_prefecture(self):
        """Test combine_full_address when prefecture is already in address."""
        # Test when prefecture is already in address
        result = ensure_prefecture_city_in_address("東京都", "千代田区", "東京都千代田区1-1-1")
        self.assertEqual(result, "東京都千代田区1-1-1")

        result = ensure_prefecture_city_in_address("大阪府", "大阪市", "大阪府大阪市中央区1-1-1")
        self.assertEqual(result, "大阪府大阪市中央区1-1-1")

    def test_combine_full_address_missing_prefecture(self):
        """Test combine_full_address when prefecture is missing."""
        # Test with missing prefecture
        result = ensure_prefecture_city_in_address("", "千代田区", "1-1-1")
        self.assertEqual(result, "千代田区1-1-1")

        result = ensure_prefecture_city_in_address(None, "渋谷区", "1-1-1")
        self.assertEqual(result, "渋谷区1-1-1")

    def test_combine_full_address_missing_city(self):
        """Test combine_full_address when city is missing."""
        # Test with missing city
        result = ensure_prefecture_city_in_address("東京都", "", "1-1-1")
        self.assertEqual(result, "東京都1-1-1")

        result = ensure_prefecture_city_in_address("大阪府", None, "1-1-1")
        self.assertEqual(result, "大阪府1-1-1")

    def test_combine_full_address_missing_both_prefecture_and_city(self):
        """Test combine_full_address when both prefecture and city are missing."""
        # Test with missing both prefecture and city
        result = ensure_prefecture_city_in_address("", "", "1-1-1")
        self.assertEqual(result, "1-1-1")

        result = ensure_prefecture_city_in_address(None, None, "丸の内1丁目")
        self.assertEqual(result, "丸の内1丁目")

    def test_combine_full_address_edge_cases(self):
        """Test combine_full_address with edge cases."""
        # Test edge cases
        result = ensure_prefecture_city_in_address("", "", "")
        self.assertEqual(result, "")

        result = ensure_prefecture_city_in_address("東京都", "千代田区", "")
        self.assertEqual(result, "東京都千代田区")

        result = ensure_prefecture_city_in_address("", "", "")
        self.assertEqual(result, "")

    def test_combine_full_address_real_japanese_addresses(self):
        """Test combine_full_address with real Japanese address examples."""
        # Real Japanese address examples
        test_cases = [
            {
                "prefecture": "東京都",
                "city": "千代田区",
                "address": "丸の内1-9-1",
                "expected": "東京都千代田区丸の内1-9-1",
            },
            {
                "prefecture": "大阪府",
                "city": "大阪市",
                "address": "中央区大手前1-3-43",
                "expected": "大阪府大阪市中央区大手前1-3-43",
            },
            {
                "prefecture": "神奈川県",
                "city": "横浜市",
                "address": "西区みなとみらい2-2-1",
                "expected": "神奈川県横浜市西区みなとみらい2-2-1",
            },
            {
                "prefecture": "愛知県",
                "city": "名古屋市",
                "address": "中区三の丸3-1-2",
                "expected": "愛知県名古屋市中区三の丸3-1-2",
            },
            {
                "prefecture": "福岡県",
                "city": "福岡市",
                "address": "博多区東公園7-7",
                "expected": "福岡県福岡市博多区東公園7-7",
            },
        ]

        for case in test_cases:
            with self.subTest(case=case):
                result = ensure_prefecture_city_in_address(
                    case["prefecture"], case["city"], case["address"]
                )
                self.assertEqual(result, case["expected"])

    def test_combine_full_address_with_existing_components(self):
        """Test combine_full_address when components already exist in address."""
        # Test when components already exist in address
        test_cases = [
            {
                "prefecture": "東京都",
                "city": "千代田区",
                "address": "東京都千代田区丸の内1-9-1",
                "expected": "東京都千代田区丸の内1-9-1",
            },
            {
                "prefecture": "大阪府",
                "city": "大阪市",
                "address": "大阪市中央区大手前1-3-43",
                "expected": "大阪府大阪市中央区大手前1-3-43",
            },
            {
                "prefecture": "神奈川県",
                "city": "横浜市",
                "address": "神奈川県横浜市西区みなとみらい2-2-1",
                "expected": "神奈川県横浜市西区みなとみらい2-2-1",
            },
        ]

        for case in test_cases:
            with self.subTest(case=case):
                result = ensure_prefecture_city_in_address(
                    case["prefecture"], case["city"], case["address"]
                )
                self.assertEqual(result, case["expected"])
