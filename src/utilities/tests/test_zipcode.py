from django.test import TestCase
from unittest.mock import patch, MagicMock

from utilities.zipcode.zipcode import (
    is_valid_japanese_zipcode,
    zipcode_to_address_by_yubinbango,
    zipcode_to_address,
    extract_zipcode_from_string,
    CityAddress,
)


class ZipcodeTestCase(TestCase):
    def test_is_valid_japanese_zipcode(self):
        # Valid formats
        self.assertTrue(is_valid_japanese_zipcode("1234567"))
        self.assertTrue(is_valid_japanese_zipcode("123-4567"))
        self.assertTrue(is_valid_japanese_zipcode("〒123-4567"))
        self.assertTrue(is_valid_japanese_zipcode(" 〒 123-4567 "))

        # Invalid formats
        self.assertFalse(is_valid_japanese_zipcode(""))
        self.assertFalse(is_valid_japanese_zipcode(None))
        self.assertFalse(is_valid_japanese_zipcode("12345"))
        self.assertFalse(is_valid_japanese_zipcode("123-45678"))
        self.assertFalse(is_valid_japanese_zipcode("12-34567"))
        self.assertFalse(is_valid_japanese_zipcode("abcdefg"))

    @patch("utilities.zipcode.zipcode.requests.get")
    def test_zipcode_to_address_by_yubinbango_success(self, mock_get):
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '$yubin({"1234567": ["13", "東京都", "千代田区"]});'
        mock_get.return_value = mock_response

        result = zipcode_to_address_by_yubinbango("1234567")

        self.assertIsNotNone(result)
        self.assertEqual(result.city, "東京都")
        mock_get.assert_called_once_with(
            "https://yubinbango.github.io/yubinbango-data/data/123.js"
        )

    @patch("utilities.zipcode.zipcode.requests.get")
    def test_zipcode_to_address_by_yubinbango_http_error(self, mock_get):
        # Mock failed API response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response

        result = zipcode_to_address_by_yubinbango("1234567")

        self.assertIsNone(result)
        mock_get.assert_called_once()

    @patch("utilities.zipcode.zipcode.requests.get")
    def test_zipcode_to_address_by_yubinbango_parse_error(self, mock_get):
        # Mock invalid response format
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "invalid json format"
        mock_get.return_value = mock_response

        result = zipcode_to_address_by_yubinbango("1234567")

        self.assertIsNone(result)
        mock_get.assert_called_once()

    @patch("utilities.zipcode.zipcode.requests.get")
    def test_zipcode_to_address_by_yubinbango_no_data(self, mock_get):
        # Mock response with no data for the zipcode
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '$yubin({"9999999": ["13", "東京都", "千代田区"]});'
        mock_get.return_value = mock_response

        result = zipcode_to_address_by_yubinbango("1234567")

        self.assertIsNone(result)
        mock_get.assert_called_once()

    @patch("utilities.zipcode.zipcode.zipcode_to_address_by_yubinbango")
    def test_zipcode_to_address_yubinbango_success(self, mock_yubinbango):
        # Test when yubinbango succeeds
        mock_yubinbango.return_value = CityAddress(city="千代田区", prefecture="東京都")

        result = zipcode_to_address("123-4567")

        self.assertEqual(result.city, "千代田区")
        self.assertEqual(result.prefecture, "東京都")
        mock_yubinbango.assert_called_once_with("1234567")

    @patch("utilities.zipcode.zipcode.zipcode_to_address_by_yubinbango")
    def test_zipcode_to_address_invalid_format(self, mock_yubinbango):
        # Test with invalid zipcode format
        result = zipcode_to_address("invalid")

        self.assertIsNone(result)
        mock_yubinbango.assert_not_called()

    def test_extract_zipcode_from_string(self):
        # Test with various formats
        self.assertEqual(extract_zipcode_from_string("〒123-4567"), "123-4567")
        self.assertEqual(extract_zipcode_from_string("〒 123-4567"), "123-4567")
        self.assertEqual(
            extract_zipcode_from_string("Address: 123-4567, Tokyo"), "123-4567"
        )
        self.assertEqual(
            extract_zipcode_from_string("Address: 1234567, Tokyo"), "1234567"
        )
        self.assertEqual(extract_zipcode_from_string("〒1234567"), "1234567")

        # Test with invalid formats
        self.assertIsNone(extract_zipcode_from_string(""))
        self.assertIsNone(extract_zipcode_from_string(None))
        self.assertIsNone(extract_zipcode_from_string("Address: 12345, Tokyo"))
        self.assertIsNone(extract_zipcode_from_string("Address: abc-defg, Tokyo"))
        self.assertIsNone(extract_zipcode_from_string("12345678"))  # Too many digits
