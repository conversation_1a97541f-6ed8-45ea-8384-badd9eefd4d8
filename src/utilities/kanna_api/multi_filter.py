from collections.abc import Iterable
from typing import Any, Callable


class MultiFilter:
    def __init__(self, *filter_funcs: Callable[[Any], bool]) -> None:
        self._filters = list(filter_funcs)

    def add(self, *filter_func: Callable[[Any], bool]) -> "MultiFilter":
        self._filters.extend(filter_func)
        return self

    def run(self, data: Iterable) -> list:
        return [d for d in data if all([f(d) for f in self._filters])]
