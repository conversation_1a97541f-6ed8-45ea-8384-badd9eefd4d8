from rest_framework.permissions import BasePermission


class HasKannaPermission(BasePermission):
    def has_permission(self, request, view):
        # 認証チェック
        if not bool(
            request.user and request.user.is_authenticated and request.user.kanna_user
        ):
            return False

        return True


class IsKannaAdminPermission(BasePermission):
    def has_permission(self, request, view):
        if not bool(
                request.user and request.user.is_authenticated and request.user.kanna_user and request.user.kanna_user.is_admin
        ):
            return False

        return True
