import re
from typing import TypeVar

CAMEL_TO_SNAKE_PATTERN = re.compile(r"(?<!^)(?=[A-Z])")
SNAKE_TO_CAMEL_PATTERN = re.compile(r"_(.)")
T = TypeVar("T", dict, list)


def camel_to_snake_json(data: T) -> T:
    if isinstance(data, dict):
        new_data = {}
        for k, v in data.items():
            if isinstance(v, dict) or isinstance(v, list):
                v = camel_to_snake_json(v)
            new_data[camel_to_snake(k)] = v
        return new_data
    elif isinstance(data, list):
        new_data = []
        for v in data:
            if isinstance(v, dict) or isinstance(v, list):
                v = camel_to_snake_json(v)
            new_data.append(v)
        return new_data


def camel_to_snake(name) -> str:
    return CAMEL_TO_SNAKE_PATTERN.sub("_", name).lower()


def snake_to_camel_json(data: T) -> T:
    if isinstance(data, dict):
        new_data = {}
        for k, v in data.items():
            if isinstance(v, dict) or isinstance(v, list):
                v = snake_to_camel_json(v)
            new_data[snake_to_camel(k)] = v
        return new_data
    elif isinstance(data, list):
        new_data = []
        for v in data:
            if isinstance(v, dict) or isinstance(v, list):
                v = snake_to_camel_json(v)
            new_data.append(v)
        return new_data


def snake_to_camel(name) -> str:
    return SNAKE_TO_CAMEL_PATTERN.sub(lambda x: x.group(1).upper(), name)
