import json

from django.conf import settings
from django.db.models import Q
from notifications.signals import notify

from api.constants import (
    KANNA_USER_ROLE,
    NOTIFICATION_FROM_TYPES,
    NOTIFICATION_SEND_TO,
    NOTIFICATION_VERBS,
)
from approve_flow.services import get_previous_level
from authentication.models import User
from kanna_api.models import UserKanna
from notification_settings.services import get_list_user_turn_on_notification


def save_notifications(
    recipients,
    sender: User,
    verb: str,
    action_object: object = None,
    target: object = None,
    data: json = None,
) -> None:
    notify.send(
        recipient=recipients,
        sender=sender,
        verb=verb,
        action_object=action_object,
        target=target,
        description=data,
    )


def send_approve_from_act_notifications(
    target_object,
    kanna_user,
    next_level="",
    approve_flow_setting_instance="",
    send_to="",
    company_id="",
    verb="",
):
    recipient_ids = []
    if verb == "":
        verb = NOTIFICATION_VERBS["APPROVE_REQUEST"]
    from_type = NOTIFICATION_FROM_TYPES["ACT"]
    if send_to == NOTIFICATION_SEND_TO["DEALER_SHOP"]:
        # if not approve level then send noti to dealer and shop
        dealer_id = None
        if approve_flow_setting_instance.dealer is not None:
            dealer_id = approve_flow_setting_instance.dealer.id
        shop_id = None
        if approve_flow_setting_instance.shop is not None:
            shop_id = approve_flow_setting_instance.shop.id
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            Q(company_id=dealer_id) | Q(company_id=shop_id)
        )

    else:
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company_id=company_id
        )
        if next_level is not None:
            recipient_ids = recipient_ids.filter(level=next_level)

    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    recipient_ids = list(recipient_ids)
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        try:
            # ACT approve
            target_object.send_notifications(
                verb=verb,
                sender=kanna_user.user,
                recipients=recipients,
                from_type=from_type,
                from_level=kanna_user.level,
            )
        except Exception as e:
            if settings.DEBUG:
                raise Exception(str(e))
            pass

    return True


def send_reject_from_act_notifications(
    target_object, kanna_user, approve_flow_setting_instance
):
    dealer_id = None
    if approve_flow_setting_instance.dealer is not None:
        dealer_id = approve_flow_setting_instance.dealer.id
    shop_id = None
    if approve_flow_setting_instance.shop is not None:
        shop_id = approve_flow_setting_instance.shop.id
    recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
        Q(company_id=dealer_id) | Q(company_id=shop_id)
    )

    recipients = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    if len(list(recipients)) > 0:
        recipients = User.objects.filter(id__in=list(recipients)).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["REJECT_REQUEST"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["ACT"],
            from_level=kanna_user.level,
        )
    return True


def send_return_from_act_notifications(
    target_object, approve_flow_setting_instance, kanna_user
):
    dealer_id = None
    if approve_flow_setting_instance.dealer is not None:
        dealer_id = approve_flow_setting_instance.dealer.id
    shop_id = None
    if approve_flow_setting_instance.shop is not None:
        shop_id = approve_flow_setting_instance.shop.id
    recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
        Q(company_id=dealer_id) | Q(company_id=shop_id)
    )
    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    recipient_ids = list(recipient_ids)
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["RETURN_REQUEST"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["ACT"],
            from_level=kanna_user.level,
        )
    return True


def get_recipients_of_return_and_reject_from_dealer(
    approve_flow_setting_instance, kanna_user, cms_uuid
):
    # if dealer 3 reject then send noti to dealer 1, 2 and shop
    # if dealer 2 reject then send noti to dealer 1, shop
    # if dealer 1 reject then send noti to shop
    recipient_id_dealers = []
    previous_level = get_previous_level(kanna_user)
    if previous_level and approve_flow_setting_instance.dealer is not None:
        recipient_id_dealers = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(
            company_id=approve_flow_setting_instance.dealer.id, level__in=previous_level
        )

    recipient_id_shops = []
    if approve_flow_setting_instance.shop is not None:
        recipient_id_shops = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(company_id=approve_flow_setting_instance.shop.id)

    recipient_id_dealers = get_list_user_turn_on_notification(
        cms_uuid=cms_uuid,
        user_kanna_uuids=recipient_id_dealers,
    )

    recipient_id_shops = get_list_user_turn_on_notification(
        cms_uuid=cms_uuid,
        user_kanna_uuids=recipient_id_shops,
    )

    return list(recipient_id_dealers) + list(recipient_id_shops)


# Dealer
def send_reject_from_dealer_notifications(
    target_object, approve_flow_setting_instance, kanna_user
):
    recipient_ids = get_recipients_of_return_and_reject_from_dealer(
        approve_flow_setting_instance, kanna_user, target_object.cms_uuid
    )
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["REJECT_REQUEST"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["DEALER"],
            from_level=kanna_user.level,
        )
    return True


def send_return_from_dealer_notifications(
    target_object, approve_flow_setting_instance, kanna_user
):
    recipient_ids = get_recipients_of_return_and_reject_from_dealer(
        approve_flow_setting_instance, kanna_user, target_object.cms_uuid
    )
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["RETURN_REQUEST"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["DEALER"],
            from_level=kanna_user.level,
        )
    return True


def send_approve_from_dealer_notifications(
    target_object, kanna_user, next_level="", send_to="", company_id=""
):
    recipient_ids = []
    verb = NOTIFICATION_VERBS["REQUEST_APPROVE"]
    from_type = NOTIFICATION_FROM_TYPES["DEALER"]
    if send_to == NOTIFICATION_SEND_TO["ACT"]:
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            role=KANNA_USER_ROLE["ACT"]
        )
        if next_level:
            recipient_ids = recipient_ids.filter(level=next_level)
    else:
        # Dealer
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company_id=company_id, level=next_level
        )

    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    recipient_ids = list(recipient_ids)
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=verb,
            sender=kanna_user.user,
            recipients=recipients,
            from_type=from_type,
            from_level=kanna_user.level,
        )

        return True


def send_submit_from_shop_notifications(
    target_object,
    kanna_user,
    approve_flow_setting_instance,
    send_to="",
    next_level="",
):
    recipient_ids = []
    if send_to == NOTIFICATION_SEND_TO["DEALER"]:
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company_id=approve_flow_setting_instance.dealer.id
        )
        if next_level is not None:
            recipient_ids = recipient_ids.filter(level=next_level)
    else:
        recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            role=KANNA_USER_ROLE["ACT"]
        )
        if next_level is not None:
            recipient_ids = recipient_ids.filter(level=next_level)

    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    recipient_ids = list(recipient_ids)
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["REQUEST_APPROVE"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["SHOP"],
        )
    return True


def send_update_after_approve_from_act_notifications(
    target_object, approve_flow_setting_instance, kanna_user
):
    # user_kanna_act = UserKanna.objects.values_list("user_id", flat=True).filter(
    #     role=KANNA_USER_ROLE["ACT"]
    # )
    dealer_id = None
    if approve_flow_setting_instance.dealer is not None:
        dealer_id = approve_flow_setting_instance.dealer.id
    shop_id = None
    if approve_flow_setting_instance.shop is not None:
        shop_id = approve_flow_setting_instance.shop.id
    recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
        Q(company_id=dealer_id) | Q(company_id=shop_id)
    )
    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        target_object.send_notifications(
            verb=NOTIFICATION_VERBS["UPDATE_AFTER_APPROVE"],
            sender=kanna_user.user,
            recipients=recipients,
            from_type=NOTIFICATION_FROM_TYPES["SHOP"],
            from_level=kanna_user.level,
        )
    return True


def send_remind_change_password(owner_user):
    data = {
        "from": NOTIFICATION_FROM_TYPES["OWNER"],
        "action": NOTIFICATION_VERBS["REMIND_CHANGE_PASSWORD"],
    }

    sender_ids = UserKanna.objects.values_list("user_id", flat=True).filter(
        role_id=KANNA_USER_ROLE["ADMIN_SYSTEM"]
    )
    if len(sender_ids) > 0:
        senders = User.objects.filter(id__in=sender_ids).all()
        save_notifications(
            verb=NOTIFICATION_VERBS["REMIND_CHANGE_PASSWORD"],
            sender=senders[0],
            recipients=owner_user,
            data=data,
        )

    return True


# ADMIN
def send_from_admin_notifications(
    verb, target_object, kanna_user, approve_flow_setting_instance
):
    recipient_ids = []
    dealer_id = None
    if approve_flow_setting_instance.dealer is not None:
        dealer_id = approve_flow_setting_instance.dealer.id
    shop_id = None
    if approve_flow_setting_instance.shop is not None:
        shop_id = approve_flow_setting_instance.shop.id
    recipient_ids = UserKanna.objects.values_list("user_uuid", flat=True).filter(
        Q(company_id=dealer_id) | Q(company_id=shop_id)
    )

    recipient_ids = get_list_user_turn_on_notification(
        cms_uuid=target_object.cms_uuid,
        user_kanna_uuids=recipient_ids,
    )
    recipient_ids = list(recipient_ids)
    if len(recipient_ids) > 0:
        recipients = User.objects.filter(id__in=recipient_ids).all()
        try:
            target_object.send_notifications(
                verb=verb,
                sender=kanna_user.user,
                recipients=recipients,
                from_type=NOTIFICATION_FROM_TYPES["ADMIN"],
                from_level="",
            )
        except Exception as e:
            if settings.DEBUG:
                raise Exception(str(e))
            pass

    return True
