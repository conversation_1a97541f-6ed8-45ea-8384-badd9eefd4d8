[{"model": "construction_methods.constructionmethodstep", "pk": 1, "fields": {"material": 1, "type": "PAINT_BOTTOM_1", "dilution": "清水 0~5", "required_volume_min": 0.06, "required_volume_max": 0.08, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー刷毛", "construction_method": 1}}, {"model": "construction_methods.constructionmethodstep", "pk": 2, "fields": {"material": 1, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.06, "required_volume_max": 0.08, "number_paints": 1, "methods_implement": "ウールローラー刷毛", "construction_method": 1}}, {"model": "construction_methods.constructionmethodstep", "pk": 3, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー刷毛", "construction_method": 2}}, {"model": "construction_methods.constructionmethodstep", "pk": 4, "fields": {"material": 15, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 3, "methods_implement": "ウールローラー刷毛", "construction_method": 2}}, {"model": "construction_methods.constructionmethodstep", "pk": 5, "fields": {"material": 15, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー刷毛", "construction_method": 2}}, {"model": "construction_methods.constructionmethodstep", "pk": 6, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー刷毛", "construction_method": 3}}, {"model": "construction_methods.constructionmethodstep", "pk": 7, "fields": {"material": 16, "type": "PAINT_OVER_1", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー刷毛", "construction_method": 3}}, {"model": "construction_methods.constructionmethodstep", "pk": 8, "fields": {"material": 16, "type": "PAINT_OVER_2", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー刷毛", "construction_method": 3}}, {"model": "construction_methods.constructionmethodstep", "pk": 9, "fields": {"material": 1, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー刷毛", "construction_method": 4}}, {"model": "construction_methods.constructionmethodstep", "pk": 10, "fields": {"material": 17, "type": "PAINT_MIDDLE", "dilution": "清水 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ルミステージパレ中塗 (パレ近似色)", "construction_method": 4}}, {"model": "construction_methods.constructionmethodstep", "pk": 11, "fields": {"material": 29, "type": "PAINT_OVER_1", "dilution": "清水 2~5", "required_volume_min": 1, "required_volume_max": 1.5, "number_paints": 1, "methods_implement": "リシンガン 口径(5~6mm)", "construction_method": 4}}, {"model": "construction_methods.constructionmethodstep", "pk": 12, "fields": {"material": 1, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー刷毛", "construction_method": 5}}, {"model": "construction_methods.constructionmethodstep", "pk": 13, "fields": {"material": 17, "type": "PAINT_MIDDLE", "dilution": "清水 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 5}}, {"model": "construction_methods.constructionmethodstep", "pk": 14, "fields": {"material": 30, "type": "PAINT_OVER_1", "dilution": "清水 1~4", "required_volume_min": 1, "required_volume_max": 1.2, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー (標準目)", "construction_method": 5}}, {"model": "construction_methods.constructionmethodstep", "pk": 15, "fields": {"material": 30, "type": "PAINT_OVER_2", "dilution": "清水 1~4", "required_volume_min": 1, "required_volume_max": 1.2, "number_paints": 1, "methods_implement": "砂骨ローラー (標準目)", "construction_method": 5}}, {"model": "construction_methods.constructionmethodstep", "pk": 16, "fields": {"material": 3, "type": "PAINT_BOTTOM_1", "dilution": "清水 10 ~ 30", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー刷毛", "construction_method": 6}}, {"model": "construction_methods.constructionmethodstep", "pk": 17, "fields": {"material": 18, "type": "PAINT_MIDDLE", "dilution": "清水 5 ~ 7.5", "required_volume_min": 0.2, "required_volume_max": 0.3, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 6}}, {"model": "construction_methods.constructionmethodstep", "pk": 18, "fields": {"material": 31, "type": "PAINT_OVER_1", "dilution": "清水 5~9", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー", "construction_method": 6}}, {"model": "construction_methods.constructionmethodstep", "pk": 19, "fields": {"material": 31, "type": "PAINT_OVER_2", "dilution": "清水 5~9", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "methods_implement": "砂骨ローラー", "construction_method": 6}}, {"model": "construction_methods.constructionmethodstep", "pk": 20, "fields": {"material": 3, "type": "PAINT_BOTTOM_1", "dilution": "清水 10 ~ 30", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー刷毛", "construction_method": 7}}, {"model": "construction_methods.constructionmethodstep", "pk": 21, "fields": {"material": 18, "type": "PAINT_MIDDLE", "dilution": "清水 5 ~ 7.5", "required_volume_min": 0.2, "required_volume_max": 0.3, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 7}}, {"model": "construction_methods.constructionmethodstep", "pk": 22, "fields": {"material": 32, "type": "PAINT_OVER_1", "dilution": "清水 2~4", "required_volume_min": 1.2, "required_volume_max": 1.5, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー", "construction_method": 7}}, {"model": "construction_methods.constructionmethodstep", "pk": 23, "fields": {"material": 39, "type": "PAINT_OVER_2", "dilution": "清水 6~8", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "methods_implement": "無泡ローラー (ポリエステル製中毛)", "construction_method": 7}}, {"model": "construction_methods.constructionmethodstep", "pk": 24, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 8}}, {"model": "construction_methods.constructionmethodstep", "pk": 25, "fields": {"material": 19, "type": "PAINT_OVER_1", "dilution": "清水 4~6", "required_volume_min": 0.6, "required_volume_max": 0.7, "number_paints": 1, "paint_distance_min": 6, "paint_distance_date": 3, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 8}}, {"model": "construction_methods.constructionmethodstep", "pk": 26, "fields": {"material": 19, "type": "PAINT_OVER_2", "dilution": "清水 4~6", "required_volume_min": 0.8, "required_volume_max": 0.9, "number_paints": 1, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 8}}, {"model": "construction_methods.constructionmethodstep", "pk": 27, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 9}}, {"model": "construction_methods.constructionmethodstep", "pk": 28, "fields": {"material": 15, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 9}}, {"model": "construction_methods.constructionmethodstep", "pk": 29, "fields": {"material": 15, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 9}}, {"model": "construction_methods.constructionmethodstep", "pk": 30, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 10}}, {"model": "construction_methods.constructionmethodstep", "pk": 31, "fields": {"material": 16, "type": "PAINT_OVER_1", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー 刷毛", "construction_method": 10}}, {"model": "construction_methods.constructionmethodstep", "pk": 32, "fields": {"material": 16, "type": "PAINT_OVER_2", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 10}}, {"model": "construction_methods.constructionmethodstep", "pk": 33, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 11}}, {"model": "construction_methods.constructionmethodstep", "pk": 34, "fields": {"material": 17, "type": "PAINT_MIDDLE", "dilution": "清水 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー", "construction_method": 11}}, {"model": "construction_methods.constructionmethodstep", "pk": 35, "fields": {"material": 29, "type": "PAINT_OVER_1", "dilution": "清水 2~5", "required_volume_min": 1, "required_volume_max": 1.5, "number_paints": 1, "methods_implement": "リシンガン 口径(5~6mm)", "construction_method": 11}}, {"model": "construction_methods.constructionmethodstep", "pk": 36, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 12}}, {"model": "construction_methods.constructionmethodstep", "pk": 37, "fields": {"material": 17, "type": "PAINT_MIDDLE", "dilution": "清水 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 12}}, {"model": "construction_methods.constructionmethodstep", "pk": 38, "fields": {"material": 30, "type": "PAINT_OVER_1", "dilution": "清水 1~4", "required_volume_min": 1, "required_volume_max": 1.2, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー (標準目)", "construction_method": 12}}, {"model": "construction_methods.constructionmethodstep", "pk": 39, "fields": {"material": 30, "type": "PAINT_OVER_2", "dilution": "清水 1~4", "required_volume_min": 1, "required_volume_max": 1.2, "number_paints": 1, "methods_implement": "砂骨ローラー (標準目)", "construction_method": 12}}, {"model": "construction_methods.constructionmethodstep", "pk": 40, "fields": {"material": 3, "type": "PAINT_BOTTOM_1", "dilution": "清水 10 ~ 30", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー刷毛", "construction_method": 13}}, {"model": "construction_methods.constructionmethodstep", "pk": 41, "fields": {"material": 18, "type": "PAINT_MIDDLE", "dilution": "清水 5 ~ 7.5", "required_volume_min": 0.2, "required_volume_max": 0.3, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 13}}, {"model": "construction_methods.constructionmethodstep", "pk": 42, "fields": {"material": 31, "type": "PAINT_OVER_1", "dilution": "清水 5~9", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー", "construction_method": 13}}, {"model": "construction_methods.constructionmethodstep", "pk": 43, "fields": {"material": 31, "type": "PAINT_OVER_2", "dilution": "清水 5~9", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "methods_implement": "砂骨ローラー", "construction_method": 13}}, {"model": "construction_methods.constructionmethodstep", "pk": 44, "fields": {"material": 3, "type": "PAINT_BOTTOM_1", "dilution": "清水 10 ~ 30", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー刷毛", "construction_method": 14}}, {"model": "construction_methods.constructionmethodstep", "pk": 45, "fields": {"material": 18, "type": "PAINT_MIDDLE", "dilution": "清水 5 ~ 7.5", "required_volume_min": 0.2, "required_volume_max": 0.3, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 14}}, {"model": "construction_methods.constructionmethodstep", "pk": 46, "fields": {"material": 32, "type": "PAINT_OVER_1", "dilution": "清水 2~4", "required_volume_min": 1.2, "required_volume_max": 1.5, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "砂骨ローラー", "construction_method": 14}}, {"model": "construction_methods.constructionmethodstep", "pk": 47, "fields": {"material": 39, "type": "PAINT_OVER_2", "dilution": "清水 6~8", "required_volume_min": 1, "required_volume_max": 1.3, "number_paints": 1, "methods_implement": "無泡ローラー (ポリエステル製中毛)", "construction_method": 14}}, {"model": "construction_methods.constructionmethodstep", "pk": 48, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 15}}, {"model": "construction_methods.constructionmethodstep", "pk": 49, "fields": {"material": 19, "type": "PAINT_OVER_1", "dilution": "清水 4~6", "required_volume_min": 0.6, "required_volume_max": 0.7, "number_paints": 1, "paint_distance_min": 6, "paint_distance_date": 3, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 15}}, {"model": "construction_methods.constructionmethodstep", "pk": 50, "fields": {"material": 19, "type": "PAINT_OVER_2", "dilution": "清水 4~6", "required_volume_min": 0.8, "required_volume_max": 0.9, "number_paints": 1, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 15}}, {"model": "construction_methods.constructionmethodstep", "pk": 51, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 16}}, {"model": "construction_methods.constructionmethodstep", "pk": 52, "fields": {"material": 19, "type": "PAINT_OVER_1", "dilution": "清水 4~6", "required_volume_min": 0.6, "required_volume_max": 0.7, "number_paints": 1, "paint_distance_min": 6, "paint_distance_date": 3, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 16}}, {"model": "construction_methods.constructionmethodstep", "pk": 53, "fields": {"material": 19, "type": "PAINT_OVER_2", "dilution": "清水 2~4", "required_volume_min": 1.3, "required_volume_max": 1.5, "number_paints": 1, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 16}}, {"model": "construction_methods.constructionmethodstep", "pk": 54, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー砂骨ローラー", "construction_method": 17}}, {"model": "construction_methods.constructionmethodstep", "pk": 55, "fields": {"material": 19, "type": "PAINT_OVER_1", "dilution": "清水 4~6", "required_volume_min": 0.6, "required_volume_max": 0.7, "number_paints": 1, "paint_distance_min": 6, "paint_distance_date": 3, "methods_implement": "リシンガン 口径(3~4mm)", "construction_method": 17}}, {"model": "construction_methods.constructionmethodstep", "pk": 56, "fields": {"material": 33, "type": "PAINT_OVER_2", "dilution": "清水 1~3", "required_volume_min": 2.5, "required_volume_max": 3.0, "number_paints": 1, "methods_implement": "リシンガン 口径(5~6mm)", "construction_method": 17}}, {"model": "construction_methods.constructionmethodstep", "pk": 57, "fields": {"material": 5, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー刷毛", "construction_method": 18}}, {"model": "construction_methods.constructionmethodstep", "pk": 58, "fields": {"material": 42, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.05, "required_volume_max": 0.07, "number_paints": 1, "methods_implement": "ウールローラー刷毛", "construction_method": 18}}, {"model": "construction_methods.constructionmethodstep", "pk": 59, "fields": {"material": 6, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.13, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 19}}, {"model": "construction_methods.constructionmethodstep", "pk": 60, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 19}}, {"model": "construction_methods.constructionmethodstep", "pk": 61, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 19}}, {"model": "construction_methods.constructionmethodstep", "pk": 62, "fields": {"material": 7, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 5 ~ 15", "required_volume_min": 0.1, "required_volume_max": 0.13, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 20}}, {"model": "construction_methods.constructionmethodstep", "pk": 63, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 20}}, {"model": "construction_methods.constructionmethodstep", "pk": 64, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 20}}, {"model": "construction_methods.constructionmethodstep", "pk": 65, "fields": {"material": 8, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.08, "required_volume_max": 0.18, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 21}}, {"model": "construction_methods.constructionmethodstep", "pk": 66, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 21}}, {"model": "construction_methods.constructionmethodstep", "pk": 67, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 21}}, {"model": "construction_methods.constructionmethodstep", "pk": 68, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー 砂骨ローラー", "construction_method": 22}}, {"model": "construction_methods.constructionmethodstep", "pk": 69, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 22}}, {"model": "construction_methods.constructionmethodstep", "pk": 70, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 22}}, {"model": "construction_methods.constructionmethodstep", "pk": 71, "fields": {"material": 9, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.1, "required_volume_max": 0.13, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 14, "methods_implement": "ウールローラー 砂骨ローラー", "construction_method": 23}}, {"model": "construction_methods.constructionmethodstep", "pk": 72, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 23}}, {"model": "construction_methods.constructionmethodstep", "pk": 73, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 23}}, {"model": "construction_methods.constructionmethodstep", "pk": 74, "fields": {"material": 10, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 24}}, {"model": "construction_methods.constructionmethodstep", "pk": 75, "fields": {"material": 20, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 0.7, "methods_implement": "ウールローラー 刷毛", "construction_method": 24}}, {"model": "construction_methods.constructionmethodstep", "pk": 75, "fields": {"material": 20, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 24}}, {"model": "construction_methods.constructionmethodstep", "pk": 76, "fields": {"material": 11, "type": "PAINT_BOTTOM_1", "dilution": "専用シンナー 0~5", "required_volume_min": 0.14, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 25}}, {"model": "construction_methods.constructionmethodstep", "pk": 77, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 25}}, {"model": "construction_methods.constructionmethodstep", "pk": 78, "fields": {"material": 23, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 25}}, {"model": "construction_methods.constructionmethodstep", "pk": 79, "fields": {"material": 11, "type": "PAINT_BOTTOM_1", "dilution": "専用シンナー 0~5", "required_volume_min": 0.16, "required_volume_max": 0.2, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 26}}, {"model": "construction_methods.constructionmethodstep", "pk": 80, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 26}}, {"model": "construction_methods.constructionmethodstep", "pk": 81, "fields": {"material": 23, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 26}}, {"model": "construction_methods.constructionmethodstep", "pk": 82, "fields": {"material": 12, "type": "PAINT_BOTTOM_1", "dilution": "専用シンナー 0~5", "required_volume_min": 0.14, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 27}}, {"model": "construction_methods.constructionmethodstep", "pk": 83, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 27}}, {"model": "construction_methods.constructionmethodstep", "pk": 84, "fields": {"material": 23, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 27}}, {"model": "construction_methods.constructionmethodstep", "pk": 85, "fields": {"material": 13, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.2, "required_volume_max": 0.24, "number_paints": 2, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 28}}, {"model": "construction_methods.constructionmethodstep", "pk": 86, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 28}}, {"model": "construction_methods.constructionmethodstep", "pk": 87, "fields": {"material": 23, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 28}}, {"model": "construction_methods.constructionmethodstep", "pk": 88, "fields": {"material": 6, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.13, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 29}}, {"model": "construction_methods.constructionmethodstep", "pk": 89, "fields": {"material": 24, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 29}}, {"model": "construction_methods.constructionmethodstep", "pk": 90, "fields": {"material": 24, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 29}}, {"model": "construction_methods.constructionmethodstep", "pk": 91, "fields": {"material": 6, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.12, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 30}}, {"model": "construction_methods.constructionmethodstep", "pk": 92, "fields": {"material": 24, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 30}}, {"model": "construction_methods.constructionmethodstep", "pk": 93, "fields": {"material": 24, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 30}}, {"model": "construction_methods.constructionmethodstep", "pk": 94, "fields": {"material": 6, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.09, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 31}}, {"model": "construction_methods.constructionmethodstep", "pk": 95, "fields": {"material": 24, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 31}}, {"model": "construction_methods.constructionmethodstep", "pk": 96, "fields": {"material": 24, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 31}}, {"model": "construction_methods.constructionmethodstep", "pk": 97, "fields": {"material": 9, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 32}}, {"model": "construction_methods.constructionmethodstep", "pk": 98, "fields": {"material": 24, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 32}}, {"model": "construction_methods.constructionmethodstep", "pk": 99, "fields": {"material": 24, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.08, "required_volume_max": 0.1, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 32}}, {"model": "construction_methods.constructionmethodstep", "pk": 100, "fields": {"material": 13, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.2, "required_volume_max": 0.24, "number_paints": 2, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 33}}, {"model": "construction_methods.constructionmethodstep", "pk": 101, "fields": {"material": 13, "type": "PAINT_MIDDLE", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 33}}, {"model": "construction_methods.constructionmethodstep", "pk": 102, "fields": {"material": 36, "type": "PAINT_OVER_1", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 33}}, {"model": "construction_methods.constructionmethodstep", "pk": 103, "fields": {"material": 40, "type": "PAINT_OVER_2", "dilution": "ボンフロンシンナー 0 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.08, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 33}}, {"model": "construction_methods.constructionmethodstep", "pk": 104, "fields": {"material": 13, "type": "PAINT_BOTTOM_1", "dilution": "専用シンナー 0~5", "required_volume_min": 0.14, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 34}}, {"model": "construction_methods.constructionmethodstep", "pk": 105, "fields": {"material": 13, "type": "PAINT_MIDDLE", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 34}}, {"model": "construction_methods.constructionmethodstep", "pk": 106, "fields": {"material": 36, "type": "PAINT_OVER_1", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 34}}, {"model": "construction_methods.constructionmethodstep", "pk": 107, "fields": {"material": 40, "type": "PAINT_OVER_2", "dilution": "ボンフロンシンナー 0 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.08, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 34}}, {"model": "construction_methods.constructionmethodstep", "pk": 108, "fields": {"material": 6, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.13, "required_volume_max": 0.17, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 35}}, {"model": "construction_methods.constructionmethodstep", "pk": 109, "fields": {"material": 26, "type": "PAINT_MIDDLE", "dilution": "ライトシンナー 5 ~ 10", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 35}}, {"model": "construction_methods.constructionmethodstep", "pk": 110, "fields": {"material": 37, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 35}}, {"model": "construction_methods.constructionmethodstep", "pk": 111, "fields": {"material": 41, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.05, "required_volume_max": 0.07, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 35}}, {"model": "construction_methods.constructionmethodstep", "pk": 112, "fields": {"material": 9, "type": "PAINT_BOTTOM_1", "dilution": "ライトシンナー 0~5", "required_volume_min": 0.13, "required_volume_max": 0.16, "number_paints": 1, "paint_distance_min": 16, "paint_distance_date": 14, "methods_implement": "ウールローラー 刷毛", "construction_method": 36}}, {"model": "construction_methods.constructionmethodstep", "pk": 113, "fields": {"material": 26, "type": "PAINT_MIDDLE", "dilution": "ライトシンナー 5 ~ 10", "required_volume_min": 0.11, "required_volume_max": 0.14, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 36}}, {"model": "construction_methods.constructionmethodstep", "pk": 114, "fields": {"material": 37, "type": "PAINT_OVER_1", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 36}}, {"model": "construction_methods.constructionmethodstep", "pk": 115, "fields": {"material": 41, "type": "PAINT_OVER_2", "dilution": "ライトシンナー 0 ~ 10", "required_volume_min": 0.05, "required_volume_max": 0.07, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 36}}, {"model": "construction_methods.constructionmethodstep", "pk": 116, "fields": {"material": 13, "type": "PAINT_BOTTOM_1", "dilution": "既調合", "required_volume_min": 0.2, "required_volume_max": 0.24, "number_paints": 2, "paint_distance_min": 16, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 37}}, {"model": "construction_methods.constructionmethodstep", "pk": 117, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 37}}, {"model": "construction_methods.constructionmethodstep", "pk": 118, "fields": {"material": 38, "type": "PAINT_OVER_2", "dilution": "ボンフロンシンナー 10 ~ 40", "required_volume_min": 0.09, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 37}}, {"model": "construction_methods.constructionmethodstep", "pk": 119, "fields": {"material": 11, "type": "PAINT_BOTTOM_1", "dilution": "専用シンナー 0~5", "required_volume_min": 0.14, "required_volume_max": 0.17, "number_paints": 1, "construction_method": 38}}, {"model": "construction_methods.constructionmethodstep", "pk": 120, "fields": {"material": 23, "type": "PAINT_OVER_1", "dilution": "ボンフロンシンナー 10 ~ 30", "required_volume_min": 0.07, "required_volume_max": 0.09, "number_paints": 1, "paint_distance_min": 3, "paint_distance_date": 7, "methods_implement": "ウールローラー 刷毛", "construction_method": 38}}, {"model": "construction_methods.constructionmethodstep", "pk": 121, "fields": {"material": 38, "type": "PAINT_OVER_2", "dilution": "ボンフロンシンナー 10 ~ 40", "required_volume_min": 0.09, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 38}}, {"model": "construction_methods.constructionmethodstep", "pk": 122, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 39}}, {"model": "construction_methods.constructionmethodstep", "pk": 123, "fields": {"material": 28, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 39}}, {"model": "construction_methods.constructionmethodstep", "pk": 124, "fields": {"material": 28, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 39}}, {"model": "construction_methods.constructionmethodstep", "pk": 125, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 40}}, {"model": "construction_methods.constructionmethodstep", "pk": 126, "fields": {"material": 27, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 40}}, {"model": "construction_methods.constructionmethodstep", "pk": 127, "fields": {"material": 27, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "paint_distance_min": 4, "paint_distance_date": 3, "methods_implement": "ウールローラー 刷毛", "construction_method": 40}}, {"model": "construction_methods.constructionmethodstep", "pk": 128, "fields": {"material": 2, "type": "PAINT_BOTTOM_1", "dilution": "清水 0 ~ 15", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 4, "methods_implement": "ウールローラー 刷毛", "construction_method": 41}}, {"model": "construction_methods.constructionmethodstep", "pk": 129, "fields": {"material": 28, "type": "PAINT_OVER_1", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー 刷毛", "construction_method": 41}}, {"model": "construction_methods.constructionmethodstep", "pk": 130, "fields": {"material": 28, "type": "PAINT_OVER_2", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 41}}, {"model": "construction_methods.constructionmethodstep", "pk": 131, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー 砂骨ローラー", "construction_method": 42}}, {"model": "construction_methods.constructionmethodstep", "pk": 132, "fields": {"material": 27, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー 刷毛", "construction_method": 42}}, {"model": "construction_methods.constructionmethodstep", "pk": 133, "fields": {"material": 27, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.12, "required_volume_max": 0.15, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 42}}, {"model": "construction_methods.constructionmethodstep", "pk": 134, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー 砂骨ローラー", "construction_method": 43}}, {"model": "construction_methods.constructionmethodstep", "pk": 135, "fields": {"material": 27, "type": "PAINT_OVER_1", "dilution": "清水 0~5", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー 刷毛", "construction_method": 43}}, {"model": "construction_methods.constructionmethodstep", "pk": 136, "fields": {"material": 27, "type": "PAINT_OVER_2", "dilution": "清水 0~5", "required_volume_min": 0.09, "required_volume_max": 0.11, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 43}}, {"model": "construction_methods.constructionmethodstep", "pk": 137, "fields": {"material": 4, "type": "PAINT_BOTTOM_1", "dilution": "5~8,清水 2~5", "required_volume_min": "0.3,0.8", "required_volume_max": "0.5,1.2", "number_paints": 1, "paint_distance_min": "3,16", "methods_implement": "ウールローラー 砂骨ローラー", "construction_method": 44}}, {"model": "construction_methods.constructionmethodstep", "pk": 138, "fields": {"material": 28, "type": "PAINT_OVER_1", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "paint_distance_min": 2, "methods_implement": "ウールローラー 刷毛", "construction_method": 44}}, {"model": "construction_methods.constructionmethodstep", "pk": 139, "fields": {"material": 28, "type": "PAINT_OVER_2", "dilution": "清水 0 ~ 10", "required_volume_min": 0.1, "required_volume_max": 0.12, "number_paints": 1, "methods_implement": "ウールローラー 刷毛", "construction_method": 44}}]