# Generated by Django 4.1.2 on 2022-11-19 07:13

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("materials", "0004_materials_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="MaterialsDetailSet",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "material_detail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="material_detail_set",
                        to="materials.materials",
                    ),
                ),
            ],
            options={
                "db_table": "materials_detail_set",
                "ordering": ["-id"],
            },
        ),
    ]
