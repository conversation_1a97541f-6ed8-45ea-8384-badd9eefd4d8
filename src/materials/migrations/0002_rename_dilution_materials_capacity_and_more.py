# Generated by Django 4.1 on 2022-09-14 04:32

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("materials", "0001_initial"),
    ]

    operations = [
        migrations.RenameField(
            model_name="materials",
            old_name="dilution",
            new_name="capacity",
        ),
        migrations.RemoveField(
            model_name="materials",
            name="methods_implement",
        ),
        migrations.RemoveField(
            model_name="materials",
            name="number_paints",
        ),
        migrations.RemoveField(
            model_name="materials",
            name="paint_distance",
        ),
        migrations.RemoveField(
            model_name="materials",
            name="required_volume",
        ),
        migrations.AddField(
            model_name="materials",
            name="name",
            field=models.CharField(default="", max_length=255),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="materials",
            name="created_at",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="materials",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
