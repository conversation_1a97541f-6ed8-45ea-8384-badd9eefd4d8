# Generated by Django 4.1.3 on 2022-11-29 07:38

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("materials", "0007_master_data_material"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="materials",
            name="name",
            field=mirage.fields.EncryptedCharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="materialsdetailset",
            name="additive",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="materialsdetailset",
            name="hardener",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="materialsdetailset",
            name="main_agent",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="materialsdetailset",
            name="paint_liquid",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
    ]
