# Generated by Django 4.1 on 2022-09-07 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Materials",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("dilution", models.FloatField()),
                ("required_volume", models.FloatField()),
                ("number_paints", models.IntegerField()),
                ("paint_distance", models.FloatField()),
                ("methods_implement", models.CharField(max_length=1000)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "materials",
            },
        ),
    ]
