from mirage import fields

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class ConstructionStepType(models.TextChoices):
    # kg / 缶
    KG_CAN = ("KG/CAN", _(" kg / 缶"))
    # kg / セット
    KG_SET = ("KG/SET", _("kg / セット"))
    # l / 缶
    L_CAN = ("L/CAN", _("ℓ /缶"))


class Materials(models.Model):
    id = models.BigAutoField(primary_key=True)
    # name = models.CharField(max_length=255)
    capacity = models.FloatField()
    name = fields.EncryptedCharField(max_length=255)
    # capacity = fields.EncryptedCharField(max_length=255)
    type = models.CharField(
        max_length=255,
        choices=ConstructionStepType.choices,
        default=ConstructionStepType.KG_CAN,
    )
    note = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "materials"
        ordering = ["-id"]


class MaterialsDetailSet(models.Model):
    id = models.BigAutoField(primary_key=True)
    material_detail = models.ForeignKey(
        Materials,
        to_field="id",
        related_name="material_detail_set",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )

    # 主剤
    # main_agent = models.FloatField(null=True, blank=True)
    main_agent = fields.EncryptedCharField(null=True, blank=True)

    # 硬化剤
    # hardener = models.FloatField(null=True, blank=True)
    hardener = fields.EncryptedCharField(null=True, blank=True)
    # 塗料液
    # paint_liquid = models.FloatField(null=True, blank=True)
    paint_liquid = fields.EncryptedCharField(null=True, blank=True)

    # 添加剤
    # additive = models.FloatField(null=True, blank=True)
    additive = fields.EncryptedCharField(null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "materials_detail_set"
        ordering = ["-id"]
