from rest_framework import viewsets
from rest_framework.exceptions import ValidationError

from django.db.models import Q

from construction_methods.models import ConstructionMethodStep
from materials.models import Materials
from materials.serializers import MaterialSerializer
from utilities.permissions.custom_permission import HasKannaPermission


class MaterialViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    serializer_class = MaterialSerializer
    http_method_names = ["get", "post", "put", "delete"]

    def get_queryset(self):
        queryset = Materials.objects.all()
        name = self.request.query_params.get("name", None)
        filter_list = []
        if name is not None:
            for instance in queryset:
                if instance.name and name in instance.name:
                    filter_list.append(instance.id)
            queryset = queryset.filter(Q(id__in=filter_list))
        return queryset

    def perform_destroy(self, request, *args, **kwargs):

        instance = self.get_object()
        construction_method_step = ConstructionMethodStep.objects.filter(
            material=instance, is_deleted=False
        )

        if construction_method_step:
            raise ValidationError({"detail": "この材料名は既に利用しています。削除することができません。"})

        return super().perform_destroy(instance)
