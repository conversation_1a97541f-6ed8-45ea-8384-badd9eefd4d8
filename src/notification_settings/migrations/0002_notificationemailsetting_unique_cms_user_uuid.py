# Generated by Django 4.1.3 on 2024-10-18 05:26

from django.db import migrations, models
from django.db.models import Count


def remove_duplicate_records(apps, schema_editor):
    NotificationEmailSetting = apps.get_model('notification_settings', 'NotificationEmailSetting')

    duplicate_records = (
        NotificationEmailSetting.objects
        .values('cms_uuid', 'user_uuid')
        .annotate(total=Count('id'))
        .filter(total__gt=1)
    )

    for record in duplicate_records:
        duplicates = NotificationEmailSetting.objects.filter(
            cms_uuid=record['cms_uuid'], user_uuid=record['user_uuid']
        )
        duplicates.exclude(id=duplicates.first().id).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("notification_settings", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_records),
        migrations.AddConstraint(
            model_name="notificationemailsetting",
            constraint=models.UniqueConstraint(
                fields=("cms_uuid", "user_uuid"), name="unique_cms_user_uuid"
            ),
        ),
    ]
