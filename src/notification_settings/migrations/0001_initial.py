# Generated by Django 4.1.3 on 2023-04-24 07:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="NotificationEmailSetting",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("cms_uuid", models.UUIDField()),
                ("user_uuid", models.UUIDField()),
                ("is_receive_email", models.BooleanField(default=True)),
                ("is_receive_notification", models.BooleanField(default=True)),
                ("is_assigned", models.BooleanField(default=True)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
