from django.db import models
from django.db.models import UniqueConstraint

from utilities.model.custom_model import CustomModel


class NotificationEmailSetting(CustomModel):
    cms_uuid = models.UUIDField()
    user_uuid = models.UUIDField()
    is_receive_email = models.BooleanField(default=True)
    is_receive_notification = models.BooleanField(default=True)
    is_assigned = models.BooleanField(default=True)

    class Meta:
        constraints = [
            UniqueConstraint(fields=["cms_uuid", "user_uuid"], name="unique_cms_user_uuid")]
