import logging

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from django.db.models import Q

from api.constants import MESSAGE_ALL_INSTANCE_UPDATE_SUCCESS
from utilities.permissions.custom_permission import <PERSON><PERSON><PERSON><PERSON>ermission

from .models import NotificationEmailSetting
from .serializers import NotificationEmailSettingSerializer

logger = logging.getLogger(__name__)


class NotificationEmailViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    http_method_names = ["get", "put"]

    def get_queryset(self):
        user = self.request.user
        kanna_user = user.kanna_user
        queryset = NotificationEmailSetting.objects.filter(
            Q(user_uuid=kanna_user.user_uuid), Q(is_assigned=True)
        ).order_by("-id")

        return queryset

    def get_serializer_class(self):

        return NotificationEmailSettingSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(
                page, many=True, context={"request": request}
            )
            return self.get_paginated_response(serializer.data)
        return Response(
            self.serialize(
                queryset,
                many=True,
                context={
                    "request": request,
                },
            ).data,
            status=status.HTTP_200_OK,
        )

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, context={"request": request})
        return Response(serializer.data)

    @action(
        detail=False,
        methods=["put"],
        url_path="toggle_all",
    )
    def toggle_all(self, request, *args, **kwargs):
        toggle_email = request.data.get("is_receive_email", None)
        toggle_notification = request.data.get("is_receive_notification", None)

        queryset = self.get_queryset()

        if toggle_email is not None:
            queryset.update(is_receive_email=toggle_email)

        if toggle_notification is not None:
            queryset.update(is_receive_notification=toggle_notification)

        return Response(
            status=status.HTTP_200_OK,
            data={
                "message": MESSAGE_ALL_INSTANCE_UPDATE_SUCCESS,
            },
        )
