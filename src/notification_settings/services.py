# add transaction
from django.db import transaction

from cms.models import CmsUserKanna
from kanna_api.models.user_kanna import UserKanna

from .models import NotificationEmailSetting


@transaction.atomic
def update_cms_notification_email_setting(user_uuid):

    cms_user_kanna = list(
        CmsUserKanna.objects.filter(user_uuid=user_uuid).values_list(
            "cms_uuid", flat=True
        )
    )
    cms_notification_setting = list(
        NotificationEmailSetting.objects.filter(
            user_uuid=user_uuid, is_assigned=True
        ).values_list("cms_uuid", flat=True)
    )

    list_active_from_inactive = list(
        set(cms_user_kanna) - set(cms_notification_setting)
    )
    list_inactive_from_active = list(
        set(cms_notification_setting) - set(cms_user_kanna)
    )

    if list_inactive_from_active:
        NotificationEmailSetting.objects.bulk_update(
            [
                setting
                for setting in NotificationEmailSetting.objects.filter(
                    cms_uuid__in=list_inactive_from_active
                )
                if not setattr(setting, "is_assigned", False)
            ],
            ["is_assigned"],
            batch_size=1000,
        )
    if list_active_from_inactive:
        for cms in list_active_from_inactive:
            NotificationEmailSetting.objects.update_or_create(
                cms_uuid=cms, user_uuid=user_uuid, defaults={"is_assigned": True}
            )


def get_list_user_turn_on_notification(cms_uuid, user_kanna_uuids):

    notification_settings = NotificationEmailSetting.objects.filter(
        cms_uuid=cms_uuid, is_assigned=True, is_receive_notification=True
    ).values_list("user_uuid", flat=True)

    user_kanna_uuids = [str(user_uuid) for user_uuid in user_kanna_uuids]
    notification_settings_str = [str(uuid) for uuid in notification_settings]

    users_turn_on_notifications = set(user_kanna_uuids).intersection(
        notification_settings_str
    )

    user_send_notification_to = UserKanna.objects.values_list(
        "user_id", flat=True
    ).filter(user_uuid__in=users_turn_on_notifications)
    if user_send_notification_to:
        return user_send_notification_to

    return []


def get_list_user_turn_on_email(cms_uuid, user_kanna_uuids):

    email_settings = NotificationEmailSetting.objects.filter(
        cms_uuid=cms_uuid, is_assigned=True, is_receive_email=True
    ).values_list("user_uuid", flat=True)
    user_kanna_uuids = [str(user_uuid) for user_uuid in user_kanna_uuids]
    email_settings_str = [str(uuid) for uuid in email_settings]

    users_turn_on_email = set(user_kanna_uuids).intersection(email_settings_str)

    user_email_to = UserKanna.objects.values_list("email", flat=True).filter(
        user_uuid__in=users_turn_on_email
    )
    if user_email_to:
        return user_email_to

    return []


def check_user_turn_on_notification(user_kanna, cms_uuid):

    is_receive = NotificationEmailSetting.objects.filter(
        user_uuid=user_kanna.user_uuid,
        cms_uuid=cms_uuid,
        is_assigned=True,
        is_receive_notification=True,
    ).exists()

    if is_receive:
        return user_kanna.user_id

    return None


def check_user_turn_on_email(user_kanna, cms_uuid):

    is_receive = NotificationEmailSetting.objects.filter(
        user_uuid=user_kanna.user_uuid,
        cms_uuid=cms_uuid,
        is_assigned=True,
        is_receive_email=True,
    ).exists()

    if is_receive:
        return user_kanna.email

    return None
