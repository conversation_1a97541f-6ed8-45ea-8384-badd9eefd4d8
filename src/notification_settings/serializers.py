import logging

from rest_framework import serializers
from rest_framework.serializers import Serializer<PERSON>ethod<PERSON>ield, UUIDField

from cms.cms import get_cms
from management_sheets.models import ManagementSheet
from utilities.exceptions.api_exception import KannaApiError

from .models import NotificationEmailSetting

logger = logging.getLogger(__name__)


class NotificationEmailSettingSerializer(serializers.ModelSerializer):
    cms_info = SerializerMethodField(read_only=True)
    user_uuid = UUIDField(read_only=True)
    is_all_notification_active = SerializerMethodField(read_only=True)
    is_all_email_active = SerializerMethodField(read_only=True)

    class Meta:
        model = NotificationEmailSetting
        fields = (
            "id",
            "cms_info",
            "user_uuid",
            "is_receive_email",
            "is_receive_notification",
            "is_all_notification_active",
            "is_all_email_active",
        )

    def get_cms_info(self, obj):
        try:
            kanna_user = self.context["request"].user.kanna_user
            cms = get_cms(user=kanna_user, cms_uuid=obj.cms_uuid)
            # cms = camel_to_snake_json(cms)
            # get_cms_customize(cms)
            management_sheet = (
                ManagementSheet.objects.filter(
                    cms_uuid=cms["uuid"],
                    is_active=True,
                )
                .exclude(construction_no__exact="")
                .first()
            )
            cms["construction_no"] = ""
            if management_sheet is not None:
                cms["construction_no"] = management_sheet.construction_no
            return {
                "title": cms.get("title", ""),
                "cms_no": cms.get("construction_no", ""),
                "cms_uuid": cms.get("uuid", ""),
            }
        except Exception as e:
            logger.error(e)
            raise KannaApiError(str(e))

    def get_is_all_notification_active(self, obj):
        if NotificationEmailSetting.objects.filter(
            user_uuid=obj.user_uuid, is_receive_notification=False
        ).exists():

            return False

        return True

    def get_is_all_email_active(self, obj):
        if NotificationEmailSetting.objects.filter(
            user_uuid=obj.user_uuid, is_receive_email=False
        ).exists():
            return False

        return True
