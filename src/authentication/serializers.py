import re
from ast import List

from rest_framework import serializers
from rest_framework.exceptions import (
    AuthenticationFailed,
    NotFound,
    ParseError,
    ValidationError,
)
from rest_framework_simplejwt.tokens import RefreshToken, TokenError

from django.contrib import auth
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.db.models import Q
from django.utils import timezone
from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from django.utils.translation import gettext_lazy as _

from api import constants
from cms.services import gen_verify_code
from companies.models import Company
from companies.serializers import CompanySerializer
from kanna_api.models.user_kanna import UserKanna
from templates.sms.sms import send_verify_code_sms
from users.models import RequestOwner, UserConstruction
from utilities.exceptions.api_exception import UserConstuctionError

from .models import User, UserStatusType, UserVerifyCode


class RegisterSerializer(serializers.ModelSerializer):
    company_id = serializers.IntegerField(required=True)
    phone = serializers.CharField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)

    class Meta:
        model = RequestOwner
        fields = [
            "email",
            "first_name",
            "last_name",
            "company_id",
            "address",
            "prefecture",
            "address_detail",
            "postcode",
            "phone",
            "notes",
            "created",
        ]

    def validate_email(self, value):
        email = value.lower()
        if User.objects.filter(email=email).exists():
            # check if existed email is current user
            raise serializers.ValidationError("このメールアドレスはAGCシステムに既に存在しています。")
        return email


class RegisterOwnerTempSerializer(serializers.ModelSerializer):
    company_id = serializers.IntegerField(required=True)
    phone = serializers.CharField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)

    class Meta:
        model = RequestOwner
        fields = [
            "email",
            "first_name",
            "last_name",
            "company_id",
            "address",
            "prefecture",
            "address_detail",
            "postcode",
            "phone",
            "notes",
            "created",
        ]


class UserShopRegisterSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = (
            "email",
            "first_name",
            "last_name",
            "company_id",
            "notes",
            "postcode",
            "address",
            "address_detail",
            "full_name",
            "prefecture",
            "phone",
            "created_at",
        )


class ShopRegisterSerializer(UserShopRegisterSerializer):
    company_id = serializers.IntegerField(required=False, allow_null=True)
    email = serializers.EmailField()
    construction_uuids = serializers.CharField(
        required=False, write_only=True, allow_blank=True
    )

    class Meta:
        model = User
        fields = UserShopRegisterSerializer.Meta.fields + ("construction_uuids",)
        extra_kwargs = {}

    def validate_email(self, value):
        email = value.lower()
        if User.objects.filter(email=email).exists():
            # check if existed email is current user
            raise serializers.ValidationError("このメールアドレスはAGCシステムに既に存在しています。")

        return email

    def validate_construction_uuids(self, value):
        construction_uuids = value

        construction_uuids = construction_uuids.split(",")
        for construction_uuid in construction_uuids:

            construction_uuid_used = UserConstruction.objects.filter(
                construction_uuid=construction_uuid
            )

            if construction_uuid_used:

                raise UserConstuctionError(
                    {
                        "construction_uuid": construction_uuid,
                        "detail": "construction was used",
                    },
                )

        return value

    def create(self, validated_data):

        user = list(validated_data)
        user.remove("construction_uuids")
        instance = User(
            **{
                "email": validated_data["email"],
                "first_name": validated_data["first_name"],
                "last_name": validated_data["last_name"],
                "company_id": validated_data["company_id"],
                "status": UserStatusType.ACTIVE,
                "notes": validated_data["notes"],
                "postcode": validated_data["postcode"],
                "address": validated_data["address"],
                "address_detail": validated_data["address_detail"],
                "prefecture": validated_data["prefecture"],
                "phone": validated_data["phone"],
            }
        )
        instance.save()
        construction_uuids = None
        arr_user_construction: List[UserConstruction] = []
        if "construction_uuids" in validated_data:
            construction_uuids = validated_data.pop("construction_uuids")

        if len(construction_uuids) == 0:
            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=instance.pk
            )
            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()
        else:
            construction_uuids = construction_uuids.split(",")
            for construction_uuid in construction_uuids:

                construction_uuid_used = UserConstruction.objects.filter(
                    ~Q(owner_id=instance.pk), construction_uuid=construction_uuid
                )

                if not construction_uuid_used:

                    item = UserConstruction(
                        **{
                            "owner_id": instance.pk,
                            "construction_uuid": construction_uuid,
                        }
                    )
                    arr_user_construction.append(item)
                else:
                    raise UserConstuctionError(
                        {
                            "owner_id": instance.pk,
                            "construction_uuid": construction_uuid,
                            "detail": "construction was used",
                        },
                    )

            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=instance.pk
            )

            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()

            UserConstruction.objects.bulk_create(arr_user_construction)

        return instance

    def get_company(self, obj):
        company = Company.objects.filter(pk=obj.company_id).first()
        if company:
            return CompanySerializer(company).data
        return None

    def get_full_name(self, obj):
        return obj.get_full_name()


class EmailVerificationSerializer(serializers.ModelSerializer):
    token = serializers.CharField(max_length=555)

    class Meta:
        model = User
        fields = ["token"]


class LoginSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(max_length=255, min_length=3)
    password = serializers.CharField(max_length=68, min_length=6, write_only=True)
    username = serializers.CharField(max_length=255, min_length=3, read_only=True)

    class Meta:
        model = User
        fields = ["email", "password", "username", "tokens"]

    def validate(self, attrs):
        email = attrs.get("email", "")
        password = attrs.get("password", "")
        user = auth.authenticate(email=email, password=password)
        if not user:
            raise AuthenticationFailed("Invalid credentials, try again")
        if not user.is_active:
            raise AuthenticationFailed("Account disabled, contact admin")
        if not user.is_verified:
            raise AuthenticationFailed("Email is not verified")
        if user.status == UserStatusType.INACTIVE and user.is_first_login is False:
            raise AuthenticationFailed("User is inactive.")

        return super().validate(attrs)


class SettingTwoAuthSerializer(serializers.Serializer):
    uidb64 = serializers.CharField(min_length=1, write_only=True)
    phone_number = serializers.CharField(min_length=1, write_only=True)

    class Meta:
        fields = ["uidb64", "phone_number"]

    def validate(self, attrs):
        try:
            uidb64 = attrs.get("uidb64")
            phone_number = attrs.get("phone_number")
            id_auto_gen = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(id=id_auto_gen)
            if user.phone == phone_number:
                raise ValidationError("Phone number is exist")

            user_kanna = UserKanna.objects.get(user=user)
            if user_kanna.tel == phone_number:
                raise ValidationError("Phone number is exist")

            self.instance = user

            return attrs
        except Exception as error:
            if isinstance(error, ValidationError):
                raise ValidationError(error.detail)

            raise ValidationError("Setting fail")

    def update(self, instance, validated_data):
        phone_number = validated_data.get("phone_number")
        user_kanna = UserKanna.objects.get(user=instance)

        user_kanna.tel = phone_number
        instance.phone = phone_number

        instance.save()
        user_kanna.save()

        gen_code = gen_verify_code(instance)
        send_verify_code_sms(user_kanna, gen_code)

        return instance


class LoginVerifySerializer(serializers.Serializer):
    email = serializers.EmailField(max_length=255, min_length=3)
    uidb64 = serializers.CharField(min_length=1, write_only=True)
    code = serializers.CharField(max_length=68, min_length=4, write_only=True)

    tokens = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ["email", "tokens", "uidb64", "code"]

    def get_tokens(self, obj):
        user = User.objects.get(email=obj["email"])

        return user.tokens()

    def validate(self, attrs):
        try:
            code = attrs.get("code")
            uidb64 = attrs.get("uidb64")
            id_auto_gen = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(id=id_auto_gen)
            user_verify_code = UserVerifyCode.objects.get(user_id=id_auto_gen)
            if not user_verify_code:
                raise NotFound("Not found verify code, try again")
            if user_verify_code.verify_time > 3:
                raise ParseError("3 回目の失敗後、一定時間ブロックされます。")
            if user_verify_code.expire_time < timezone.now():
                raise ParseError("認証コードの有効期限が切れました。")
            if user_verify_code.code != code:
                user_verify_code.verify_time += 1
                user_verify_code.save()
                raise ParseError("認証コードが間違っています。")

        except Exception as err:
            if isinstance(err, ParseError):
                raise ParseError(err.detail)
            raise ParseError("本人確認が失敗しました。")

        UserVerifyCode.objects.filter(user_id=id_auto_gen).delete()
        return {"email": user.email, "username": user.username, "tokens": user.tokens}


class UserVerifySerializer(serializers.ModelSerializer):
    class Meta:
        model = UserVerifyCode
        fields = ["code", "user", "expire_time"]

    def create(self, validated_data):
        UserVerifyCode.objects.filter(user_id=int(validated_data["user"].id)).delete()
        user_verify_user = UserVerifyCode.objects.create(**validated_data)

        return user_verify_user


class ResetPasswordEmailRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(min_length=2)

    redirect_url = serializers.CharField(max_length=500, required=False)

    class Meta:
        fields = ["email"]


class SetNewPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(min_length=6, max_length=68, write_only=True)
    token = serializers.CharField(min_length=1, write_only=True)
    uidb64 = serializers.CharField(min_length=1, write_only=True)

    class Meta:
        fields = ["password", "token", "uidb64"]

    def validate(self, attrs):
        try:
            password = attrs.get("password")
            token = attrs.get("token")
            uidb64 = attrs.get("uidb64")

            id_auto_gen = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(id=id_auto_gen)

            if not PasswordResetTokenGenerator().check_token(user, token):
                raise AuthenticationFailed("The reset link is invalid", 401)
            reg = constants.REGEX_CHECK_FORMAT_PASSWORD
            # compiling regex
            pat = re.compile(reg)
            # searching regex
            mat = re.search(pat, password)
            if not mat:
                raise serializers.ValidationError(_("password is invalid"))

            user.set_password(password)
            user.last_change_password_at = timezone.now()
            user.save()

            return user
        except Exception as err:
            print(err)
            raise AuthenticationFailed("The reset link is invalid", 401)


class LogoutSerializer(serializers.Serializer):
    refresh = serializers.CharField()

    default_error_message = {"message": (_("Token is expired or invalid"))}

    def validate(self, attrs):
        self.token = attrs["refresh"]
        return attrs

    def save(self, **kwargs):
        try:
            RefreshToken(self.token).blacklist()
        except TokenError:
            pass
            # self.fail("bad_token")


class GetUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id"]


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(max_length=128, write_only=True, required=True)
    new_password = serializers.CharField(max_length=128, write_only=True, required=True)
    new_password_confirm = serializers.CharField(
        max_length=128, write_only=True, required=True
    )

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError(
                _("Your old password was entered incorrectly. Please enter it again.")
            )
        return value

    def validate(self, data):
        if data["new_password"] != data["new_password_confirm"]:
            raise serializers.ValidationError(
                {"new_password_confirm": _("The two password fields didn't match.")}
            )

        password = data["new_password"]
        reg = constants.REGEX_CHECK_FORMAT_PASSWORD
        # compiling regex
        pat = re.compile(reg)
        # searching regex
        mat = re.search(pat, password)
        if not mat:
            raise serializers.ValidationError(_("password is invalid"))
        return data

    def save(self, **kwargs):
        password = self.validated_data["new_password"]
        user = self.context["request"].user
        user.set_password(password)
        if user.is_first_login is True:
            user.is_first_login = False
            user.status = UserStatusType.ACTIVE
        user.last_change_password_at = timezone.now()
        user.save()
        return user
