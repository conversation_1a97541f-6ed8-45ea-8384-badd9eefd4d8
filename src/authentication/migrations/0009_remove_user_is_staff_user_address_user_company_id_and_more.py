# Generated by Django 4.0.6 on 2022-09-09 03:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0008_remove_user_shop_user_shop_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='is_staff',
        ),
        migrations.AddField(
            model_name='user',
            name='address',
            field=models.Char<PERSON>ield(blank=True, max_length=225),
        ),
        migrations.AddField(
            model_name='user',
            name='company_id',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='first_name',
            field=models.Char<PERSON>ield(blank=True, max_length=125),
        ),
        migrations.AddField(
            model_name='user',
            name='last_name',
            field=models.Char<PERSON>ield(blank=True, max_length=125),
        ),
        migrations.AddField(
            model_name='user',
            name='phone',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=125),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='user',
            name='status',
            field=models.Char<PERSON>ield(blank=True, choices=[('Waiting_for_approve', 'Waiting'), ('Approved', 'Approved'), ('Inactive', 'Inactive'), ('Active', 'Active')], default='Inactive', max_length=125),
        ),
    ]
