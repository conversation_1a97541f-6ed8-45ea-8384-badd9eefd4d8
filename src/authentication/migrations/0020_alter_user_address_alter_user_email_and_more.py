# Generated by Django 4.1.3 on 2022-11-21 10:08

from django.db import migrations
import mirage.fields


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0019_user_last_check_notification_at"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="user",
            name="address",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=mirage.fields.EncryptedEmailField(
                blank=True, max_length=254, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="first_name",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="last_name",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="user",
            name="phone",
            field=mirage.fields.EncryptedCharField(
                blank=True, max_length=255, null=True
            ),
        ),
    ]
