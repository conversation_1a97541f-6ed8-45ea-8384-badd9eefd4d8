import time
from datetime import date, datetime, timedelta

from django.conf import settings
from django.core.management.base import BaseCommand

from authentication.models import User
from kanna_api.models import UserKanna
from templates.emails.mailer import send_remind_change_password_email
from utilities.helpers.notifications import send_remind_change_password


class Command(BaseCommand):
    def handle(self, *args, **kwargs):

        today = date.today()
        time_now = datetime.utcnow()
        print("Checked Day: ", time_now)
        kanna_user_emails = UserKanna.objects.values_list("email", flat=True).all()
        users = (
            User.objects.filter(is_active=True)
            .exclude(email__in=kanna_user_emails)
            .filter(is_superuser=False)
        )
        for user in users:
            start_date = user.last_change_password_at
            end_date = start_date + timedelta(
                days=settings.DAYS_TO_REMIND_CHANGE_PASSWORD
            )
            if end_date < today:
                time.sleep(settings.TIME_TO_DELAY_SENT_EMAIL)
                send_remind_change_password_email(user)
                send_remind_change_password(user)
