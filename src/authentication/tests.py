from unittest import mock

from djet import assertions
from rest_framework.test import APITestCase

from django.urls import reverse

from companies.factories import CompanyFactory
from prefectures.factories import PrefecturesFactory

from .factories import UserFactory
from .models import UserStatusType


class AuthenticationTests(APITestCase, assertions.EmailAssertionsMixin):
    @mock.patch("utilities.email_helper.send")
    def test_user_request_account(self, mock_send_email):
        mock_send_email.return_value = 1
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.pk)
        # test role and re-password are required fields
        user_data = {
            "email": "<EMAIL>",
            "first_name": "first",
            "last_name": "last",
            "company_id": company.pk,
            "notes": "This is my notes",
            "phone": "***********",
        }
        url = reverse("register", kwargs={"version": "v1"})
        response = self.client.post(url, user_data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["notes"], user_data["notes"])

        # test user create is success
        user_data = {
            "email": "<EMAIL>",
        }
        response = self.client.post(url, user_data)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["company_id"][0], "この項目は必須です。")

    @mock.patch("utilities.email_helper.send")
    def test_company_approve_user_success(self, mock_send_email):
        mock_send_email.return_value = 1
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.pk)

        user_data = {
            "email": "<EMAIL>",
            "first_name": "first",
            "last_name": "last",
            "prefectures_id": prefectures.pk,
            "company_id": company.pk,
            "phone": "123123",
        }
        url = reverse("register", kwargs={"version": "v1"})
        response = self.client.post(url, user_data)
        self.assertEqual(response.status_code, 201)

        url = reverse(
            "companies:companies-approve", kwargs={"version": "v1", "pk": company.pk}
        )
        data = {
            "owner_id": response.data["id"],
            "address": "100 Tokyo",
            "prefectures_id": "",
            "address_detail": "100 Tokyo Japan",
            "prefecture": "北海道",
            "postcode": "〒053-0804",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)

    @mock.patch("utilities.email_helper.send")
    def test_approve_users_for_company_not_success(self, mock_send_email):
        mock_send_email.return_value = 1

        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.pk)
        url = reverse(
            "companies:companies-approve", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.post(url)
        self.assertEqual(response.status_code, 400)

        # Test user not register with company
        owner = UserFactory()
        data = {"owner_ids": owner.pk}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 400)

        owner.company_id = company.pk
        owner.status = UserStatusType.APPROVED
        owner.save()

        data = {"owner_ids": owner.pk}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 400)

    @mock.patch("utilities.email_helper.send")
    def test_company_create_user(self, mock_send_email):
        mock_send_email.return_value = 1
        prefectures = PrefecturesFactory()
        company = CompanyFactory(prefectures_id=prefectures.id)
        # test role and re-password are required fields
        user_data = {
            "email": "<EMAIL>",
            "first_name": "first",
            "last_name": "last",
            "company_id": company.pk,
            "phone": "11",
            "address_detail": "123 Tokyo, Japan",
            "prefecture": "prefecture",
            "postcode": "12111",
        }
        url = reverse("register-by-shop", kwargs={"version": "v1"})
        response = self.client.post(url, user_data)
        self.assertEqual(response.data["address_detail"], user_data["address_detail"])
        self.assertEqual(response.data["prefecture"], user_data["prefecture"])
        self.assertEqual(response.data["postcode"], user_data["postcode"])

        self.assertEqual(response.status_code, 201)

        # test user create is not success
        user_data = {
            "email": "<EMAIL>",
        }
        response = self.client.post(url, user_data)
        self.assertEqual(response.status_code, 400)
