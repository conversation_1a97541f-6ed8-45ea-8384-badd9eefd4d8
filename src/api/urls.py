"""api URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
import notifications.urls
from drf_yasg import openapi
from drf_yasg.views import get_schema_view

from django.conf import settings
from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.urls import include, path, re_path

from areas.views import AreaViewSet
from prefectures.views import PrefecturesViewSet
from users.views import UserKannaViewSet

schema_view = get_schema_view(
    openapi.Info(
        title="AOC API Documents",
        default_version="v1",
    ),
    public=True,
)
schema_api_docs = []
if settings.DEBUG:
    schema_api_docs = [
        path(
            "",
            schema_view.with_ui("swagger", cache_timeout=0),
            name="Schema Swagger UI",
        ),
    ]

version_reg = "(?P<version>[v1,v2]+)"
urlpatterns = (
    [
        path("api/admin/", admin.site.urls),
        re_path(f"api/{version_reg}/auth/", include("authentication.urls")),
        re_path(f"api/{version_reg}/kanna/", include("kanna_api.urls")),
        re_path(f"api/{version_reg}/users/", include("users.urls")),
        re_path(f"api/{version_reg}/companies/", include("companies.urls")),
        re_path(f"api/{version_reg}/templates/", include("template_management.urls")),
        re_path(
            f"api/{version_reg}/role_managements/", include("role_management.urls")
        ),
        re_path(f"api/{version_reg}/cms/", include("cms.urls")),
        re_path(f"api/{version_reg}/diagnostics/", include("diagnostics.urls")),
        re_path(f"api/{version_reg}/materials/", include("materials.urls")),
        re_path(
            f"api/{version_reg}/inbox/notifications/",
            include(notifications.urls, namespace="notifications"),
        ),
        re_path(
            f"api/{version_reg}/notification_settings/",
            include("notification_settings.urls"),
        ),
        re_path(
            f"api/{version_reg}/application_base/", include("application_bases.urls")
        ),
        re_path(f"api/{version_reg}/properties/", include("properties.urls")),
        re_path(
            f"api/{version_reg}/management_sheets/", include("management_sheets.urls")
        ),
        re_path(f"api/{version_reg}/calendars/", include("japanese_calendars.urls")),
        re_path(f"api/{version_reg}/documents/", include("cms_document.urls")),
        re_path(f"api/{version_reg}/warranty/", include("warranty.urls")),
        re_path(f"api/{version_reg}/approve-list/", include("approve_history.urls")),
        re_path(
            f"api/{version_reg}/approve_flow_settings/",
            include("approve_flow_settings.urls"),
        ),
        re_path(
            f"api/{version_reg}/construction_methods/",
            include("construction_methods.urls"),
        ),
        re_path(
            f"api/{version_reg}/areas/",
            AreaViewSet.as_view({"get": "list"}),
            name="areas",
        ),
        re_path(
            f"api/{version_reg}/prefectures/",
            PrefecturesViewSet.as_view({"get": "list"}),
            name="prefectures",
        ),
        re_path(
            f"api/{version_reg}/kanna/users/",
            UserKannaViewSet.as_view({"get": "list"}),
            name="kanna_users",
        ),
        re_path(f"api/{version_reg}/customers/", include("customers.urls")),
        re_path(
            f"api/{version_reg}/building_managements/",
            include("building_managements.urls"),
        ),
        re_path(f"api/{version_reg}/dashboard/", include("dashboard.urls")),
        re_path(f"api/{version_reg}/cms_images/", include("cms_images.urls")),
        re_path(f"api/{version_reg}/files/", include("files.urls")),
        re_path(
            f"api/{version_reg}/diagnostic_files/", include("diagnostic_files.urls")
        ),
    ]
    + [path('silk/', include('silk.urls', namespace='silk'))]
    + staticfiles_urlpatterns()
    + schema_api_docs
)
