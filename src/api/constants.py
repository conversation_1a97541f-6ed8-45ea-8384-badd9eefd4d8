from enum import Enum

from django.conf import settings

# KANNA Open Api 一覧取得件数最大値

KANNA_LIST_LIMIT = 100

# KANNA Open Api 一覧先頭ページ
KANNA_LIST_FIRST_PAGE = 1

# テンプレートディレクトリ
TEMPLATE_DIR = "api/static/images/"

# PDFテンプレートディレクトリ
PDF_TEMPLATE_DIR = "api/static/pdf_templates/"

PDF_WARRANTY_DIR = "static/warranties/pdf/"
PDF_WARRANTY_SUB_DIR = "warranties/pdf"

# Excelテンプレートディレクトリ
EXCEL_TEMPLATE_DIR = "api/static/excel_templates/"

# KANNA Open Api 担当者権限
KANNA_USER_ROLE = {
    "admin": "ADMIN",
    "general": "GENERAL",
    "ADMIN_SYSTEM": 1,
    "ACT": 2,
    "DEALER": 3,
    "MAKEUP_SHOP": 4,
}

# OWNER 担当者権限
OWNER_USER_ROLE = 5

COLUMN_CSV_USER_KANNA = 8
MAX_DIAGNOSTICS = 5
MAX_CONSTRUCTION_METHOD = 4

# KANNA写真フォルダ層最大数
KANNA_IMAGE_CATEGORY_LAYER_MAX = 5

# 承認者ロールの権限名キー
APPROVE_AUTHORITY_ROLE = "C_APPROVE_EXECUTE"
# システム管理者の権限名キー
ADMIN_AUTHORITY = "M_ADMIN"

KANNA_DIAGNOSTIC_FOLDER_NAME = "診断書"
KANNA_MANAGEMENT_SHEET_FOLDER_NAME = "管理シート"
KANNA_WARRANTY_FOLDER_NAME = "保証書"

# Limit image size
MAX_IMAGE_WIDTH = 1280
MAX_IMAGE_HEIGHT = 720
MAX_LENGTH_NAME = 255
MAX_LENGTH_POSITION = 255
MAX_LENGTH_PHONE = 15
MAX_IMAGES = 100


# Sub folder name for SDS in Kanna
SUBSTRATE_CONDITION_FOLDER = "下地状況"
EXISTING_PAINT_FILM_CONDITION_FOLDER = "既存塗膜状況"
OTHER_CONDITION_FOLDER = "その他不具合状況"
SDS_OUTER_WALL = "外壁塗装診断書"
SDS_ROOF = "屋根塗装診断書"

# Notificaitons
NOTIFICATION_VERBS = {
    "APPROVE_REQUEST": "APPROVE_REQUEST",
    "REQUEST_APPROVE": "REQUEST_APPROVE",
    "REJECT_REQUEST": "REJECT_REQUEST",
    "RETURN_REQUEST": "RETURN_REQUEST",
    "RELEASE_WARRANTY": "RELEASE_WARRANTY",
    "UPDATE_AFTER_APPROVE": "UPDATE_AFTER_APPROVE",
    "REMIND_CHANGE_PASSWORD": "REMIND_CHANGE_PASSWORD",
}
NOTIFICATION_FROM_TYPES = {
    "SHOP": "SHOP",
    "DEALER": "DEALER",
    "ACT": "ACT",
    "OWNER": "OWNER",
    "ADMIN": "ADMIN",
}
NOTIFICATION_DOCUMENT_TYPES = {
    "MS": "MS",
    "SDS": "SDS",
    "USER": "USER",
    "WARRANTY": "WARRANTY",
}
NOTIFICATION_SEND_TO = {"ACT": "ACT", "DEALER": "DEALER", "DEALER_SHOP": "DEALER_SHOP"}
# PDF
MS_PDF_REQUIRED_FIELDS = ["金属屋根", "軒天", "破風", "金属部", "その他", "ﾁｮｰｷﾝｸﾞ", "塗膜剥離", "塗膜の膨れ"]
PDF_SDS_OUTER_WALL_REQUIRED_FIELDS_1 = ["コンクリート", "モルタル", "ALC"]
SDS_OUTER_WALL_PDF_REQUIRED_FIELDS = ["ﾁｮｰｷﾝｸﾞ", "塗膜の膨れ"]
SDS_OUTER_WALL_PDF_IGNORE_FIELDS = [
    "diagnosticDetail.common.form.label.repairMethod",
    "diagnosticDetail.common.form.label.boardSizeWitdh",
    "diagnosticDetail.common.form.label.boardSizeHeight",
]

LEVEL_MEMBERS = {
    "LEVEL_1": "LEVEL_1",
    "LEVEL_2": "LEVEL_2",
    "LEVEL_3": "LEVEL_3",
}

LEVEL_MEMBERS_NUMBER = {
    "LEVEL_1": 1,
    "LEVEL_2": 2,
    "LEVEL_3": 3,
}

NO_TWO_AUTH = AUTH_BY_EMAIL = 0
HAVE_TWO_AUTH = AUTH_BY_SMS = 1

TWO_AUTH_FLAG = ((NO_TWO_AUTH, "INDEX_0"), (HAVE_TWO_AUTH, "INDEX_1"))

COLUMN_CSV_APPROVE_FLOW_SETTING = 11

POSTAL_CODE_SYMBOL = "〒"

FE_DIAGNOSTIC_LIST_URL = settings.WEBSITE_URL + "diagnostic-list"
FE_MANAGEMENT_SHEET_LIST_URL = settings.WEBSITE_URL + "management-sheet"

# NOTIFICATION AND EMAIL SETTINGS
MESSAGE_ALL_INSTANCE_UPDATE_SUCCESS = "All instances have been updated."

REGEX_CHECK_FORMAT_PASSWORD = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!"#$%&\'()*+,-./:;<=>?@\\[\]^_`{|}~])[A-Za-z\d!"#$%&\'()*+,-./:;<=>?@\\[\]^_`{|}~]{8,32}$'

# SORT DIRECTION
ASCENDING = "ASC"
DESCENDING = "DESC"


# Enum

class KannaUserRoleEnum(Enum):
    """
    Enum class representing user roles in the Kanna system.
    """
    ADMIN = 1
    ACT = 2
    DEALER = 3
    SHOP = 4
    OWNER = 5


class ApproveFlowAction(Enum):
    REQUEST = "申請"
    RETURN = "差戻"
    REJECT = "却下"
    APPROVE = "承認"


class RequestType(Enum):
    ROOF = "屋根診断書"
    OUTER_WALL = "外壁診断書"
    MANAGEMENT_SHEET = "管理シート"
