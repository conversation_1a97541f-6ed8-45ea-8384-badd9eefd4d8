"""
Django settings for api project.

Generated by 'django-admin startproject' using Django 4.0.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""
import datetime
import os
from datetime import date
from pathlib import Path

import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

from utilities import config

config.load()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

STATIC_ROOT = os.path.join(BASE_DIR, "static/")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-1(5#)9d2i&md(bwu@dtkghaq-p^h*$%8^a04#l7apij_uy^bn("

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv("DEBUG", "").lower() == "true"
APP_ENV = os.getenv("APP_ENV", "local")

ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "").split(",")

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework_simplejwt.token_blacklist",
    "rest_framework_swagger",
    "drf_yasg",
    # user app
    "files",
    "authentication",
    "kanna_api",
    "kanna_api.authentication_kanna",
    "django_extensions",
    "api",
    "corsheaders",
    "users",
    "template_management",
    "role_management",
    "cms",
    "companies",
    "diagnostics",
    "materials",
    "application_bases",
    "properties",
    "construction_methods",
    "management_sheets",
    "approve_flow_settings",
    "areas",
    "prefectures",
    "japanese_calendars",
    "approve_flow",
    "cms_document",
    "warranty",
    "approve_history",
    "notifications",
    "customers",
    "building_managements",
    "dashboard",
    "cms_images",
    "notification_settings",
    "diagnostic_files",
    "silk",
]

AUTH_USER_MODEL = "authentication.User"

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'silk.middleware.SilkyMiddleware',
]

CORS_ORIGIN_WHITELIST = os.environ.get(
    "CORS_ORIGIN_WHITELIST", "http://localhost:3000"
).split(",")


ROOT_URLCONF = "api.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
            "libraries": {
                "staticfiles": "django.templatetags.static",
            },
        },
    },
]

WSGI_APPLICATION = "api.wsgi.application"
# Swagger
SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"}
    },
}

REST_FRAMEWORK = {
    # Pagination
    # "DEFAULT_PAGINATION_CLASS": "utilities.pagination.custom_pagination.CustomPageNumberPagination",
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 10000,
    # JWT
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    # Swagger
    "DEFAULT_SCHEMA_CLASS": "rest_framework.schemas.coreapi.AutoSchema",
    # Versioning: https://www.django-rest-framework.org/api-guide/versioning/#versioning-with-rest-framework
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.URLPathVersioning",  # Using URL path
    "DEFAULT_VERSION": "v1",  # If the parameter is not passed or the version is wrong, the version number obtained is v1
    "ALLOWED_VERSIONS": [
        "v1",
        # "v2",
    ],  # The version number must be v1 or v2, otherwise an error is reported,
    "VERSION_PARAM": "version",
    # Exception
    # "EXCEPTION_HANDLER": "utilities.exceptions.api_exception.custom_exception_handler",
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": datetime.timedelta(
        minutes=int(os.getenv("ACCESS_TOKEN_LIFETIME"))
    ),
    "REFRESH_TOKEN_LIFETIME": datetime.timedelta(
        minutes=int(os.getenv("REFRESH_TOKEN_LIFETIME"))
    ),
    "ALGORITHM": "HS256",
    "ROTATE_REFRESH_TOKENS": True,
}

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": os.getenv("DATABASE_ENGINE"),
        "NAME": os.getenv("DATABASE_NAME"),
        "USER": os.getenv("DATABASE_USER"),
        "PASSWORD": os.getenv("DATABASE_PASSWORD"),
        "HOST": os.getenv("DATABASE_HOST"),
        "PORT": os.getenv("DATABASE_PORT"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Logging config

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {"require_debug_false": {"()": "django.utils.log.RequireDebugFalse"}},
    "formatters": {
        "verbose": {
            "format": (
                "%(asctime)s [%(process)d] [%(levelname)s] "
                + "pathname=%(pathname)s lineno=%(lineno)s "
                + "funcname=%(funcName)s %(message)s "
            ),
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "simple": {
            "format": "%(levelname)s %(message)s",
        },
    },
    "handlers": {
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
        "slack_admins": {
            "level": "ERROR",
            "class": "utilities.helpers.slack_logger.SlackExceptionHandler",
        },
        "file": {
            "level": os.getenv("DJANGO_LOG_LEVEL", "ERROR"),
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "verbose",
            "when": "D",  # this specifies the interval
            "interval": 1,  # defaults to 1, only necessary for other values
            "backupCount": 730,
            "filename": "logs/" + str(date.today()) + ".log",
        },
        "login_file": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "verbose",
            "when": "D",  # this specifies the interval
            "interval": 1,  # defaults to 1, only necessary for other values
            "backupCount": 730,
            "filename": "logs/logins/" + str(date.today()) + ".log",
        },
        "upload_s3_log": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "verbose",
            "when": "D",  # this specifies the interval
            "interval": 1,  # defaults to 1, only necessary for other values
            "backupCount": 730,
            "filename": "logs/upload_s3/" + str(date.today()) + ".log",
        },
        "sync_user_kanna": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "verbose",
            "when": "D",  # this specifies the interval
            "interval": 1,  # defaults to 1, only necessary for other values
            "backupCount": 730,
            "filename": "logs/sync_user_kanna/" + str(date.today()) + ".log",
        },
    },
    "loggers": {
        "django.request": {
            "handlers": ["slack_admins"],
            "level": os.getenv("DJANGO_LOG_LEVEL", "ERROR"),
            "propagate": True,
        },
        "": {
            "handlers": ["file"],
            "level": os.getenv("DJANGO_LOG_LEVEL", "ERROR"),
        },
        "login": {
            "handlers": ["login_file"],
            "level": "INFO",
        },
        "upload_s3": {
            "handlers": ["upload_s3_log"],
            "level": "INFO",
        },
        "sync_user_kanna": {
            "handlers": ["sync_user_kanna"],
            "level": "INFO",
        },
    },
}

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = os.getenv("LANGUAGE_CODE")

TIME_ZONE = os.getenv("TIME_ZONE")

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/
API_URL = os.getenv("API_URL", "")

STATIC_URL = "static/"
STATICFILES_DIRS = (os.path.join(BASE_DIR, "templates"),)
# STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]


# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# メールサーバーへの接続設定
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.getenv("EMAIL_SERVER_HOST")
EMAIL_HOST_USER = os.getenv("EMAIL_SERVER_USERNAME")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_SERVER_PASSWORD")
EMAIL_PORT = os.getenv("EMAIL_SERVER_PORT")
EMAIL_USE_TLS = False
EMAIL_USE_SSL = False
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL")
ADMIN_EMAIL = os.getenv("ADMIN_EMAIL")
# Email Sendgrid
SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")

# sentry_sdk.init(
#     dsn=os.getenv("SENTRY_DSN"),
#     integrations=[DjangoIntegration()],
#     traces_sample_rate=1.0,
#     send_default_pii=True,
# )

# KANNA API 設定
KANNA_URL = os.getenv("KANNA_URL")
KANNA_CLIENT_ID = os.getenv("KANNA_CLIENT_ID")
KANNA_CLIENT_SECRET = os.getenv("KANNA_CLIENT_SECRET")

WEBSITE_URL = os.getenv("WEBSITE_URL")

# AWS S3
FILE_UPLOAD_STORAGE = os.getenv("FILE_UPLOAD_STORAGE", "local")
if FILE_UPLOAD_STORAGE == "local":
    MEDIA_ROOT_NAME = "media"
    MEDIA_ROOT = os.path.join(BASE_DIR, MEDIA_ROOT_NAME)
    MEDIA_URL = f"/{MEDIA_ROOT_NAME}/"

if FILE_UPLOAD_STORAGE == "s3":
    # Using django-storages
    # https://django-storages.readthedocs.io/en/latest/backends/amazon-S3.html
    DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

    AWS_S3_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "local")
    AWS_S3_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "local")
    AWS_STORAGE_LOGS_BUCKET_NAME = os.getenv("AWS_STORAGE_LOGS_BUCKET_NAME", "local")
    AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME", "local")
    AWS_S3_REGION_NAME = os.getenv("AWS_REGION", "local")
    AWS_S3_SIGNATURE_VERSION = os.getenv("AWS_S3_SIGNATURE_VERSION", "s3v4")
    AWS_S3_ENDPOINT_URL = os.getenv("AWS_S3_ENDPOINT_URL", "http://localhost:3000")

    # https://docs.aws.amazon.com/AmazonS3/latest/userguide/acl-overview.html#canned-acl
    AWS_DEFAULT_ACL = os.getenv("AWS_DEFAULT_ACL", "private")

    AWS_PRESIGNED_EXPIRY = int(os.getenv("AWS_PRESIGNED_EXPIRY", "10"))  # seconds

# encrypt DB
MIRAGE_SECRET_KEY = os.getenv("MIRAGE_SECRET_KEY")

# day to remind change password
DAYS_TO_REMIND_CHANGE_PASSWORD = int(os.getenv("DAYS_TO_REMIND_CHANGE_PASSWORD", "90"))

# time to delay sent email (in seconds)
TIME_TO_DELAY_SENT_EMAIL = int(os.getenv("TIME_TO_DELAY_SENT_EMAIL", "3"))


# time to remove notification (in hours)
TIME_TO_REMOVE_NOTIFICATION = int(os.getenv("TIME_TO_REMOVE_NOTIFICATION", "2160"))

# Email user kanna create cms from AGC
USER_KANNA_EMAIL_TO_CREATE_CMS = os.getenv("USER_KANNA_EMAIL_TO_CREATE_CMS")

AWS_SNS_REGION_NAME = os.getenv("AWS_SNS_REGION_NAME")
AWS_SNS_ACCESS_KEY_ID = os.getenv("AWS_SNS_ACCESS_KEY_ID")
AWS_SNS_SECRET_ACCESS_KEY = os.getenv("AWS_SNS_SECRET_ACCESS_KEY")

# 2auth token expire
TOKEN_EXPIRE = int(os.getenv("TOKEN_EXPIRE", "3"))  # minutes

# 2auth token length
TOKEN_LENGTH = 6

SILKY_IGNORE_PATHS = [
    r'^/silk/.*',
    r'^/admin/.*',
    r'^/health/.*',
    r'^/static/.*',
]
