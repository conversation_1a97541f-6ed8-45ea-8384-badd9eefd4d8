from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response

from kanna_api.cms_document import create_document
from utilities.permissions.custom_permission import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .serializers import UploadDocumentSerializer
from .services import get_document_folder_uuid


class CmsDocumentViewSet(viewsets.GenericViewSet):
    permission_classes = (HasKannaPermission,)
    parser_classes = (MultiPartParser,)
    serializer_class = UploadDocumentSerializer

    @action(
        detail=False,
        methods=["post"],
        parser_classes=(MultiPartParser,),
        url_path="(?P<cms_uuid>[a-zA-Z0-9\-_]+)/upload",  # noqa
    )
    @swagger_auto_schema(
        request_body=UploadDocumentSerializer(),
        # responses={200: {"success": True}},
    )
    def cms_document_list(self, request, cms_uuid: str, *args, **kwargs):
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        kanna_user = user.kanna_user
        document_type = data["document_type"]
        folder_uuid = get_document_folder_uuid(document_type, kanna_user, cms_uuid)
        for file in request.FILES.getlist("files", default=[]):
            files = {"file": (file.name, file, file.content_type)}
            create_document(kanna_user, cms_uuid, folder_uuid, files)

        return Response(data={"success": True}, status=status.HTTP_200_OK)
