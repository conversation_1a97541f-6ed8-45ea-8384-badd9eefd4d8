from django.db import models

from api import constants
from kanna_api.cms_document import get_document
from kanna_api.models import UserKanna


class DocumentType(models.TextChoices):
    DIAGNOSTIC = "diagnostic"
    MANAGEMENT_SHEET = "management_sheet"
    WARRANTY = "warranty"


class DocumentDownloadInformation(models.Model):
    image_uuid = models.UUIDField(null=True, blank=True)
    document_uuid = models.UUIDField(null=True, blank=True)
    cms_uuid = models.UUIDField(null=True, blank=True)
    diagnostic = models.OneToOneField(
        "diagnostics.Diagnostic",
        related_name="download_document_infomartion",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    management_sheet = models.OneToOneField(
        "management_sheets.ManagementSheet",
        related_name="download_document_infomartion",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    warranty = models.OneToOneField(
        "warranty.Warranty",
        related_name="download_document_infomartion",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def get_url_by_admin_account(self, user_request, document_type=None):
        if user_request.kanna_user:
            kanna_user_shop = user_request.kanna_user
        else:
            kanna_user_shop = UserKanna.objects.filter(
                company_id=user_request.shop_id
            ).first()

        if kanna_user_shop:
            user = user_request.kanna_user
            if user.role_id in [
                constants.KANNA_USER_ROLE["ACT"],
                constants.KANNA_USER_ROLE["ADMIN_SYSTEM"],
            ]:
                if document_type == "diagnostic":
                    return {
                        "url": self.diagnostic.pdf_url,
                        "download_url": self.diagnostic.pdf_url,
                    }
                elif document_type == "management-sheet":
                    return {
                        "url": self.management_sheet.pdf_url,
                        "download_url": self.management_sheet.pdf_url,
                    }
                elif document_type == "warranty":
                    return {
                        "url": self.warranty.pdf_url,
                        "download_url": self.warranty.pdf_url,
                    }
                else:
                    return {}
            else:
                kanna_image = get_document(
                    user=kanna_user_shop,
                    cms_uuid=self.cms_uuid,
                    document_category_uuid=self.document_uuid,
                    document_uuid=self.image_uuid,
                )
                return {
                    "url": kanna_image["document"]["documentUrl"],
                    "download_url": kanna_image["document"]["documentDownloadUrl"],
                }
        else:
            # @TODO: raise error
            return {}
