from rest_framework.exceptions import NotFound

from api.constants import (
    KANNA_DIAGNOSTIC_FOLDER_NAME,
    KANNA_MANAGEMENT_SHEET_FOLDER_NAME,
    KANNA_WARRANTY_FOLDER_NAME,
)
from kanna_api.cms_document import get_document_categories

from .models import DocumentDownloadInformation, DocumentType


def get_folder_name(document_type):
    if document_type == DocumentType.DIAGNOSTIC.value:
        return KANNA_DIAGNOSTIC_FOLDER_NAME
    elif document_type == DocumentType.MANAGEMENT_SHEET.value:
        return KANNA_MANAGEMENT_SHEET_FOLDER_NAME
    elif document_type == DocumentType.WARRANTY.value:
        return KANNA_WARRANTY_FOLDER_NAME
    else:
        raise NotFound(detail="Folder not found")


def get_document_folder_uuid(document_type, user_kanna, cms_uuid):
    list_folders = get_document_categories(user_kanna, cms_uuid)
    folder_name = get_folder_name(document_type)
    for folder in list_folders:
        if folder["name"] == folder_name:
            return folder["uuid"]

    return cms_uuid


def store_document_information(
    image_uuid,
    document_uuid,
    cms_uuid,
    diagnostic=None,
    management_sheet=None,
    warranty=None,
):
    DocumentDownloadInformation.objects.update_or_create(
        document_uuid=document_uuid,
        cms_uuid=cms_uuid,
        diagnostic=diagnostic,
        management_sheet=management_sheet,
        warranty=warranty,
        defaults={"image_uuid": image_uuid},
    )
