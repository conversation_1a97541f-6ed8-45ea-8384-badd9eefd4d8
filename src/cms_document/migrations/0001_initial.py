# Generated by Django 4.1.2 on 2022-11-01 08:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("warranty", "0004_warranty_warranty_year_type_and_more"),
        ("diagnostics", "0020_rename_buinding_structure_diagnostic_building_structure"),
        (
            "management_sheets",
            "0029_alter_actualconstructionmethod_amount_of_use_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentDownloadInformation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image_uuid", models.UUIDField(blank=True, null=True)),
                ("document_uuid", models.UUIDField(blank=True, null=True)),
                ("cms_uuid", models.UUIDField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "diagnostic",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="download_document_infomartion",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "management_sheet",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="download_document_infomartion",
                        to="management_sheets.managementsheet",
                    ),
                ),
                (
                    "warranty",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="download_document_infomartion",
                        to="warranty.warranty",
                    ),
                ),
            ],
        ),
    ]
