from rest_framework import serializers

from .models import DocumentDownloadInformation, DocumentType


class UploadDocumentSerializer(serializers.Serializer):
    files = serializers.ImageField()
    document_type = serializers.ChoiceField(choices=DocumentType)


class GetDocumentDownloadInformationSerializer(serializers.ModelSerializer):
    urls = serializers.SerializerMethodField()

    class Meta:
        model = DocumentDownloadInformation

    def get_urls(self, obj):
        pass
