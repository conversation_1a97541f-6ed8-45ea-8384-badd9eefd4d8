import logging
import os
from datetime import date, timedelta

from django.conf import settings
from django.core.management.base import BaseCommand

from files.services import S3

logger_s3 = logging.getLogger("upload_s3")


class Command(BaseCommand):
    def __handle_logins_log(self) -> bool:
        logger_s3.info("Start upload login logs to s3")
        pwd = os.getcwd()
        login_date = date.today()
        login_previous_date = date.today() - timedelta(days=1)

        all_file = os.listdir(pwd + "/logs/logins")
        files = [
            f for f in all_file if str(login_previous_date) in f or str(login_date) in f
        ]

        for _file in files:
            log_logins_path = pwd + "/logs/logins/" + _file
            if os.path.exists(log_logins_path):
                S3(
                    bucket_name=settings.AWS_STORAGE_LOGS_BUCKET_NAME
                ).upload_direct_to_s3(
                    log_logins_path,
                    "logs/logins/" + _file,
                )
                logger_s3.info(f"Done upload {_file} to s3")

        logger_s3.info("Done upload login logs to s3")

        return True

    def __handle_common_log(self) -> bool:
        logger_s3.info("Start upload common logs to s3")

        pwd = os.getcwd()

        date_today = date.today()
        previous_date = date.today() - timedelta(days=1)
        all_file = os.listdir(pwd + "/logs")
        files = [f for f in all_file if str(previous_date) in f or str(date_today) in f]
        for _file in files:
            log_path = pwd + "/logs/" + _file
            if os.path.exists(log_path):
                S3(
                    bucket_name=settings.AWS_STORAGE_LOGS_BUCKET_NAME
                ).upload_direct_to_s3(
                    log_path,
                    "logs/" + _file,
                )
                logger_s3.info(f"Done upload {_file} to s3")

        logger_s3.info("Done upload common logs to s3")

        return True

    def __handle_sync_user_kanna(self) -> bool:
        logger_s3.info("Start upload sync user kanna logs to s3")

        pwd = os.getcwd()

        date_today = date.today()
        previous_date = date.today() - timedelta(days=1)

        all_file = os.listdir(pwd + "/logs/sync_user_kanna")

        files = [f for f in all_file if str(previous_date) in f or str(date_today) in f]
        for _file in files:
            log_path = pwd + "/logs/sync_user_kanna/" + _file
            if os.path.exists(log_path):
                S3(
                    bucket_name=settings.AWS_STORAGE_LOGS_BUCKET_NAME
                ).upload_direct_to_s3(
                    log_path,
                    "logs/sync_user_kanna/" + _file,
                )
                logger_s3.info(f"Done upload {_file} to s3")

        logger_s3.info("Done upload sync user kanna logs to s3")

        return True

    def handle(self, *args, **options):

        self.__handle_logins_log()

        self.__handle_common_log()

        self.__handle_sync_user_kanna()
