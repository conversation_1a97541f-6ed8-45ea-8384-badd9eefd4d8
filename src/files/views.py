import logging

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, views
from rest_framework.response import Response

from django.shortcuts import get_object_or_404

from files.services import FileDirectUploadService

from .models import File
from .serializers import (
    FileUploadFinishResponseSerializer,
    FileUploadFinishSerializer,
    FileUploadStartResponseSerializer,
    FileUploadStartSerializer,
)

logger = logging.getLogger(__name__)


class FileDirectUploadStartApi(views.APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = FileUploadStartSerializer

    @swagger_auto_schema(
        responses={
            200: openapi.Response(
                description="Presigned data for file upload",
                schema=FileUploadStartResponseSerializer,
            ),
            400: "Bad Request",
            401: "Unauthorized",
            403: "Forbidden",
            404: "Not Found",
        },
        request_body=FileUploadStartSerializer(),
    )
    def post(self, request, *args, **kwargs):
        serializer = FileUploadStartSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        service = FileDirectUploadService()
        presigned_data = service.start(**serializer.validated_data)
        return Response(data=presigned_data)


class FileDirectUploadFinishApi(views.APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = FileUploadFinishSerializer

    @swagger_auto_schema(
        responses={
            200: openapi.Response(
                description="File upload finish response",
                schema=FileUploadFinishResponseSerializer,
            ),
        },
        request_body=FileUploadFinishSerializer(),
    )
    def post(self, request, *args, **kwargs):
        serializer = FileUploadFinishSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file_id = serializer.validated_data["file_id"]
        file = get_object_or_404(File, id=file_id)
        image_url = file.get_public_image_url()

        service = FileDirectUploadService()
        service.finish(file=file)

        return Response({"id": file.id, "url": image_url})
