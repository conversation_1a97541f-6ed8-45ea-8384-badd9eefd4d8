from django.conf import settings
from django.db import models

from utilities.helpers.files import file_generate_upload_path


class File(models.Model):
    file = models.FileField(upload_to=file_generate_upload_path, blank=True, null=True)
    original_file_name = models.TextField()
    file_name = models.CharField(max_length=255, unique=True)
    file_type = models.CharField(max_length=255)
    upload_finished_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = "file"

    @property
    def is_valid(self):
        """
        We consider a file "valid" if the the datetime flag has value.
        """
        return bool(self.upload_finished_at)

    @property
    def url(self):
        if settings.FILE_UPLOAD_STORAGE == "s3":
            return self.file.url

        return f"{settings.APP_DOMAIN}{self.file.url}"

    def get_public_image_url(self):
        if settings.FILE_UPLOAD_STORAGE == "s3":
            return f"{settings.AWS_S3_ENDPOINT_URL}/public/{self.file_name}"

        return f"{settings.APP_DOMAIN}{self.file.url}"
