from rest_framework import serializers


class FileUploadStartSerializer(serializers.Serializer):
    file_name = serializers.CharField()
    file_type = serializers.CharField()


class S3PresignResponseSerializer(serializers.Serializer):
    key = serializers.CharField()
    policy = serializers.CharField()


class FileUploadStartResponseSerializer(serializers.Serializer):
    id = serializers.CharField()
    url = serializers.CharField()
    fields = S3PresignResponseSerializer()


class FileUploadFinishSerializer(serializers.Serializer):
    file_id = serializers.CharField()


class FileUploadFinishResponseSerializer(serializers.Serializer):
    id = serializers.CharField()


class FileDirectUploadSerializer(serializers.Serializer):
    file = serializers.FileField()
