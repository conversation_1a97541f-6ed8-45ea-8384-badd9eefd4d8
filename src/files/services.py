import logging
import pathlib
from uuid import uuid4

import boto3
from botocore.exceptions import ClientError

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from utilities.helpers.files import (
    file_generate_local_upload_url,
    file_generate_name,
    file_generate_upload_path,
)

from .models import File

logger = logging.getLogger(__name__)


class FileDirectUploadService:
    @transaction.atomic
    def start(self, *, file_name: str, file_type: str):
        original_file_name = file_name
        file_name = file_generate_name(file_name)
        file = File(
            original_file_name=original_file_name,
            file_name=file_name,
            file_type=file_type,
            file=None,
        )
        file.full_clean()
        file.save()
        upload_path = file_generate_upload_path(file, file.file_name)
        """
        We are doing this in order to have an associated file for the field.
        """
        file.file = file.file.field.attr_class(file, file.file.field, upload_path)
        file.save()
        presigned_data = {}
        if settings.FILE_UPLOAD_STORAGE == "s3":
            presigned_data = S3().generate_presigned_post(file_name, file_type)
        else:
            presigned_data = {
                "url": file_generate_local_upload_url(file_id=str(file.id)),
            }
        return {"id": file.id, **presigned_data}

    @transaction.atomic
    def finish(self, *, file: File) -> File:
        file.upload_finished_at = timezone.now()
        file.full_clean()
        file.save()
        return file

    @transaction.atomic
    def upload_local(self, *, file: File, file_obj) -> File:
        file.file = file_obj
        file.full_clean()
        file.save()
        return file


class S3:
    def __init__(self, bucket_name=None) -> None:
        self.client = boto3.client(
            service_name="s3",
            region_name=settings.AWS_S3_REGION_NAME,
            aws_access_key_id=settings.AWS_S3_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
        )
        if bucket_name:
            self.bucket_name = bucket_name
        else:
            self.bucket_name = settings.AWS_STORAGE_BUCKET_NAME

    def upload_direct_to_s3(self, file_path, object_name=None):
        if object_name is None:
            object_name = file_path
        try:
            with open(file_path, "rb") as f:
                self.client.put_object(Bucket=self.bucket_name, Key=object_name, Body=f)
        except ClientError as error:
            logger.exception(
                f"Couldn't upload file '{file_path}' to bucket '{self.bucket_name}' and object '{object_name}' with error: {error}"
            )

        return True

    def get_presigned_url(self, file_name, time=3600):
        object_key = f"public/{uuid4().hex}{pathlib.Path(file_name).suffix}"

        try:
            url = self.client.generate_presigned_url(
                ClientMethod="put_object",
                ExpiresIn=time,
                Params={"Bucket": self.bucket_name, "Key": object_key},
            )
        except ClientError as error:
            logger.exception(
                "Couldn't get a presigned POST URL for bucket '%s' and object '%s'",
                self.bucket_name,
                object_key,
            )
            raise error
        return {"url": url, "file_name": file_name}

    def generate_presigned_post(self, file_name, file_type, expires_in=3600):
        object_key = f"public/{file_name}"
        acl = settings.AWS_DEFAULT_ACL
        try:
            presigned_data = self.client.generate_presigned_post(
                self.bucket_name,
                object_key,
                Fields={"acl": acl, "Content-Type": file_type},
                Conditions=[
                    {"acl": acl},
                    {"Content-Type": file_type},
                ],
                ExpiresIn=expires_in,
            )
        except ClientError as error:
            logger.exception(
                "Couldn't get a presigned POST URL for bucket '%s' and object '%s'",
                self.bucket_name,
                object_key,
            )
            return error
        return presigned_data

    def get_file(self, key, time=3600):
        return self.client.generate_presigned_url(
            ClientMethod="get_object",
            ExpiresIn=time,
            Params={"Bucket": self.bucket_name, "Key": key},
        )

    def delete_file(self, key):
        return self.client.delete_object(Bucket=self.bucket_name, Key=key)
