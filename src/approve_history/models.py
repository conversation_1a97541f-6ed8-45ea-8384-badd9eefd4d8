from django.db import models

from utilities.model.custom_model import CustomModel


class ApproveList(CustomModel):
    management_sheet = models.OneToOneField(
        "management_sheets.ManagementSheet",
        related_name="approve_list",
        on_delete=models.CASCADE,
        null=True,
    )
    diagnostic = models.OneToOneField(
        "diagnostics.Diagnostic",
        related_name="approve_list",
        on_delete=models.CASCADE,
        null=True,
    )
    document_type = models.CharField(max_length=10, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
