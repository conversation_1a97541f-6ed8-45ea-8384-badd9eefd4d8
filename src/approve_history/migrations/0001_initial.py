# Generated by Django 4.1.2 on 2022-11-06 14:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("diagnostics", "0022_alter_diagnostic_diagnosis_date"),
        ("management_sheets", "0030_managementsheet_building_owner_name_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ApproveList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "document_type",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "diagnostic",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approve_list",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "management_sheet",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approve_list",
                        to="management_sheets.managementsheet",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
