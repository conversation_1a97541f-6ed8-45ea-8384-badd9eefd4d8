
# Generated by Django 4.1 on 2022-09-20 09:11

from django.db import migrations


def delete_approve_list_instance(apps, schema_editor):
    approve_list_instance_model = apps.get_model("approve_history", "ApproveList")
    approve_list_instance_model.objects.all().delete()


def create_approve_list_instance(apps, schema_editor):
    approve_list_instance_model = apps.get_model("approve_history", "ApproveList")
    diagnostic_model = apps.get_model("diagnostics", "Diagnostic")
    management_sheet_model = apps.get_model("management_sheets", "ManagementSheet")
    diagnostics = diagnostic_model.objects.all()
    management_sheets = management_sheet_model.objects.all()
    # Insert diagnostic into approve list
    for diagnostic in diagnostics:
        try:
            if diagnostic.status != "TEMP":
                approve_list_instance_model.objects.get_or_create(
                    diagnostic=diagnostic,
                    document_type=diagnostic.type
                )
        except Exception as e:
            print(e)
            pass

    # Insert management_sheet into approve list
    for management_sheet in management_sheets:
        try:
            if management_sheet.status != "TEMP":
                approve_list_instance_model.objects.get_or_create(
                    management_sheet=management_sheet,
                    document_type="MS"
                )
        except Exception as e:
            print(e)
            pass


class Migration(migrations.Migration):
    dependencies = [
        ("approve_history", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(create_approve_list_instance, reverse_code=delete_approve_list_instance),
    ]
