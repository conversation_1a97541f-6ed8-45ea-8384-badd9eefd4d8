from rest_framework import serializers

from approve_flow.models import (
    ApproveFlowStep,
    ApproveFlowStepStatusChoices,
    UserElectronicMark, UserRoleType,
)
from cms.services import get_cms_kanna
from companies.models import CompanyType
from diagnostics.choices import DiagnosticStatus
from .models import ApproveList


class ApproveListSerializers(serializers.ModelSerializer):

    construction_name = serializers.SerializerMethodField(read_only=True)
    status = serializers.SerializerMethodField(read_only=True)
    applicant_name = serializers.SerializerMethodField(read_only=True)
    approve_flow = serializers.SerializerMethodField(read_only=True)
    expected_approve_date = serializers.SerializerMethodField(read_only=True)
    cms_uuid = serializers.SerializerMethodField(read_only=True)
    id_document = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ApproveList
        fields = (
            "id_document",
            "cms_uuid",
            "construction_name",
            "status",
            "document_type",
            "applicant_name",
            "approve_flow",
            "expected_approve_date",
        )

    def get_construction_name(self, obj):
        if diagnostic := obj.diagnostic:
            cms = get_cms_kanna(diagnostic.cms_uuid, self.context["kanna_user"])
        else:
            cms = get_cms_kanna(
                obj.management_sheet.cms_uuid, self.context["kanna_user"]
            )
        return cms.get("title")

    def get_status(self, obj):
        if diagnostic := obj.diagnostic:
            return diagnostic.status
        else:
            return obj.management_sheet.status

    def get_id_document(self, obj):
        if diagnostic := obj.diagnostic:
            return diagnostic.id
        else:
            return obj.management_sheet.id

    def get_applicant_name(self, obj):
        if diagnostic := obj.diagnostic:
            user_mark = UserElectronicMark.objects.filter(
                diagnostic=diagnostic, user_role_type=UserRoleType.SHOP
            ).first()
            applicant_name = ""
            if user_mark:
                applicant_name = user_mark.user.get_full_name()

            else:
                # if SDS return
                if diagnostic.status == DiagnosticStatus.RETURN:
                    approve_flow_step = ApproveFlowStep.objects.filter(
                        diagnostic=diagnostic,
                        status=ApproveFlowStepStatusChoices.APPROVED,
                        company__type=CompanyType.SHOP,
                    ).first()
                    applicant_name = approve_flow_step.user.get_full_name()

            return applicant_name
        else:
            user_mark = UserElectronicMark.objects.filter(
                management_sheet=obj.management_sheet, user_role_type=UserRoleType.SHOP
            ).first()

            applicant_name = ""
            if user_mark:
                applicant_name = user_mark.user.get_full_name()

            else:
                # if MS return
                if obj.management_sheet.status == DiagnosticStatus.RETURN:
                    approve_flow_step = ApproveFlowStep.objects.filter(
                        management_sheet=obj.management_sheet,
                        status=ApproveFlowStepStatusChoices.APPROVED,
                        company__type=CompanyType.SHOP,
                    ).first()
                    applicant_name = approve_flow_step.user.get_full_name()

            return applicant_name

    def get_approve_flow(self, obj):
        if diagnostic := obj.diagnostic:
            return diagnostic.approve_flow_setting.name
        else:
            return obj.management_sheet.approve_flow_setting.name

    def get_expected_approve_date(self, obj):
        if diagnostic := obj.diagnostic:
            return diagnostic.expected_approve_date
        else:
            return obj.management_sheet.expected_approve_date

    def get_cms_uuid(self, obj):
        if diagnostic := obj.diagnostic:
            return diagnostic.cms_uuid
        else:
            return obj.management_sheet.cms_uuid
