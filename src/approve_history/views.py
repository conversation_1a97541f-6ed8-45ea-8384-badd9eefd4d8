from django.db.models import Q, TextField
from django.db.models.functions import Cast
from rest_framework import generics
from rest_framework.response import Response

from approve_flow.models import ApproveFlowSettingInstance, ApproveFlowStep
from authentication.models import User
from cms.cms import cms_filter, get_cms_list
from cms.models import CmsUserKanna
from cms.services import update_user_assign_cms
from companies.models import CompanyType
from diagnostics.models import DiagnosticStatus
from kanna_api.query_params.cms import CmsParam
from management_sheets.choices import ManagementSheetStatus
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import Has<PERSON>annaPermission
from .models import ApproveList
from .serializers import ApproveListSerializers

from api.constants import KannaUserRoleEnum


# Create your views here.
class ApproveHistoryListAPIView(generics.ListAPIView):
    permission_classes = [HasKannaPermission]
    serializer_class = ApproveListSerializers

    def get_queryset(self):
        user = self.request.user
        kanna_user = user.kanna_user
        company = kanna_user.company
        if kanna_user:
            update_user_assign_cms(kanna_user)
        cms_user_current = list(
            CmsUserKanna.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(Q(user_uuid=kanna_user.user_uuid))
        )

        if kanna_user.role_id == KannaUserRoleEnum.ADMIN.value:
            approve_flow_setting_queryset = ApproveFlowSettingInstance.objects.all()
        elif company:
            approve_flow_setting_queryset = ApproveFlowSettingInstance.objects.filter(
                Q(shop=company) | Q(dealer=company) | Q(act=company)
            )

        # filter diagnostic
        list_diagnostic_ids = approve_flow_setting_queryset.filter(
            ~Q(diagnostic__status=DiagnosticStatus.TEMP),
            Q(diagnostic__cms_uuid__in=cms_user_current),
        ).values_list("diagnostic_id")

        # filter management_sheet
        management_sheet_ids = approve_flow_setting_queryset.filter(
            ~Q(management_sheet__status=ManagementSheetStatus.TEMP),
            Q(management_sheet__cms_uuid__in=cms_user_current),
        ).values_list("management_sheet_id")

        query_set = ApproveList.objects.filter(
            Q(diagnostic__id__in=(list_diagnostic_ids))
            | Q(management_sheet__id__in=(management_sheet_ids))
        ).distinct()

        # @TODO: Search
        query_param = self.request.query_params

        title = self.request.query_params.get("title", None)
        if title is not None:

            params = CmsParam(**self.request.query_params.dict())
            # cms取得

            cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)
            cms_list_data = camel_to_snake_json(cms_list)

            cms_list_data = cms_filter(
                user,
                params=params,
                cms_list_data=cms_list_data,
                on_user_cms_uuid_list=[],
                not_on_user_cms_uuid_list=[],
                not_on_group_cms_uuid_list=[],
                cms_uuid_list=[],
            )

            data_response = []
            data_append = {}
            for data in cms_list_data:
                try:
                    data_append = data["uuid"]
                    data_response.append(data_append)
                except Exception as e:
                    print(e)

            query_set = query_set.filter(
                Q(diagnostic__cms_uuid__in=data_response)
                | Q(management_sheet__cms_uuid__in=data_response)
            )

        if document_type := query_param.get("document_type"):
            # filter sds
            if document_type.startswith("SDS"):
                document_type = document_type[4:]

            query_set = query_set.filter(Q(document_type=document_type))

        if applicant_name := query_param.get("applicant_name"):
            users = User.objects.all()
            user_ids = []
            keyword = applicant_name.lower()
            for user in users:
                full_name = user.get_full_name()
                full_name_reverse = f"{user.first_name} {user.last_name}"
                first_name = user.first_name if user.first_name else ""
                last_name = user.last_name if user.last_name else ""
                if (
                    keyword in first_name.lower()
                    or keyword in last_name.lower()
                    or keyword in full_name.lower()
                    or keyword in full_name_reverse.lower()
                    or keyword.replace(" ", "") in full_name.lower().replace(" ", "")
                    or keyword.replace(" ", "")
                    in full_name_reverse.lower().replace(" ", "")
                ):
                    user_ids.append(user.id)
            users = users.filter(Q(id__in=user_ids))
            list_document_sds = ApproveFlowStep.objects.values_list(
                "diagnostic_id", flat=True
            ).filter(
                Q(diagnostic_id__isnull=False),
                Q(user__in=users),
                Q(company__type=CompanyType.SHOP),
            )
            list_document_ms = ApproveFlowStep.objects.values_list(
                "management_sheet_id", flat=True
            ).filter(
                Q(management_sheet_id__isnull=False),
                Q(user__in=users),
                Q(company__type=CompanyType.SHOP),
            )

            query_set = query_set.filter(
                Q(diagnostic__id__in=list_document_sds)
                | Q(management_sheet__id__in=list_document_ms)
            )

        if status := query_param.get("status"):
            list_status = status.split(",")
            query_set = query_set.filter(
                Q(diagnostic__status__in=list_status)
                | Q(management_sheet__status__in=list_status)
            )

        return query_set.order_by("-id")

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(
                page, many=True, context={"kanna_user": self.request.user.kanna_user}
            )
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
