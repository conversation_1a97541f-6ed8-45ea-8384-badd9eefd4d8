import csv
import os

import pandas as pd

from django.core.management.base import BaseCommand, CommandParser

from companies.models import CompanyType
from companies.utils import create_companies, create_relations_shop_dealer


class Command(BaseCommand):
    help = "Generate shop data"

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument(
            "--file_name",
            "-f",
            type=str,
            dest="file_name",
            help="File name",
            required=True,
        )

        return super().add_arguments(parser)

    def handle(self, *args, **options):
        current_directory = os.getcwd()
        file = current_directory + "/companies/csv_data/" + options["file_name"]

        # create companies in makeupshop sheet
        data_shop = pd.read_excel(file, sheet_name="shop")
        if data_shop is not None:
            data = data_shop.to_csv(index=False).split("\n")
            file_reader = []
            for line in csv.reader(data):
                file_reader.append(line)
            create_companies(file_reader, CompanyType.SHOP)

        # create companies in makeupshop sheet
        data_dealer = pd.read_excel(file, sheet_name="dealer")
        if data_dealer is not None:
            data = data_dealer.to_csv(index=False).split("\n")
            file_reader = []
            for line in csv.reader(data):
                file_reader.append(line)
            create_companies(file_reader, CompanyType.DEALER)

        # create relation shop and dealer
        shop_dealer = pd.read_excel(file, sheet_name="shopdealer")
        if shop_dealer is not None:
            data = shop_dealer.to_csv(index=False).split("\n")
            file_reader = []
            for line in csv.reader(data):
                file_reader.append(line)
            create_relations_shop_dealer(file_reader)
