from datetime import datetime
from typing import Dict, List, Optional, Tuple

from django.core.management.base import BaseCommand
from django.db.models import Q

from companies.models import Company
from utilities.zipcode.zipcode import zipcode_to_address, CityAddress


BATCH_SIZE = 100  # Number of companies to update in each batch


class Command(BaseCommand):
    help = "Update company city fields based on postcodes"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Run without making actual changes to the database",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        start_time = datetime.now()

        if dry_run:
            self.stdout.write("Running in dry-run mode - no changes will be made")

        # Step 1: Get companies with blank city
        companies = self.get_companies_with_blank_city()
        total_companies = len(companies)
        self.stdout.write(f"Found {total_companies} companies with blank city field")

        if total_companies == 0:
            self.stdout.write("No companies to update. Exiting.")
            return

        # Step 2: Process companies in batches
        zipcode_cache = {}  # Cache for zipcode lookups
        updated_count = 0

        for i in range(0, total_companies, BATCH_SIZE):
            batch = companies[i:i + BATCH_SIZE]
            self.stdout.write(f"Processing batch {i//BATCH_SIZE + 1} ({len(batch)} companies)")
            
            updated_companies, batch_updated_count = self.process_company_batch(
                batch, zipcode_cache
            )

            updated_count += batch_updated_count

            if not dry_run and updated_companies:
                self.bulk_update_companies(updated_companies)
                
            self.stdout.write(f"Batch completed. Updated {batch_updated_count} companies in this batch.")

        self.stdout.write(f"Process completed. Total companies updated: {updated_count}/{total_companies}")
        self.stdout.write(f"Run finished after: {datetime.now() - start_time}")

    def get_companies_with_blank_city(self) -> List[Company]:
        """
        Get all companies with blank city field
        
        Returns:
            List[Company]: List of companies with blank city
        """
        companies = Company.objects.filter(
            Q(city__isnull=True) | Q(city="")
        ).exclude(
            postcode__isnull=True
        ).exclude(
            postcode=""
        )
        
        return list(companies)

    def process_company_batch(
        self, 
        companies: List[Company], 
        zipcode_cache: Dict[str, Optional[CityAddress]],
    ) -> Tuple[List[Company], int]:
        """
        Process a batch of companies to update their city fields
        
        Args:
            companies: List of companies to process
            zipcode_cache: Cache of zipcode to CityAddress mappings
            
        Returns:
            Tuple containing list of updated companies and count of updates
        """
        updated_companies = []
        updated_count = 0
        
        for company in companies:
            postcode = company.real_postcode.strip()
            
            if not postcode:
                self.stdout.write(f"Company {company.id} ({company.name}) has empty postcode")
                continue

            city_address = self.get_city_address(postcode, zipcode_cache)
            
            if not city_address:
                self.stdout.write(f"Could not get city for company {company.id} ({company.name}) with postcode {postcode}")
                continue
                
            if not city_address.city:
                self.stdout.write(f"Empty city returned for company {company.id} ({company.name}) with postcode {postcode}")
                continue
            company.city = city_address.city
            updated_companies.append(company)
                
            updated_count += 1

        return updated_companies, updated_count

    def get_city_address(
        self, 
        postcode: str, 
        zipcode_cache: Dict[str, Optional[CityAddress]]
    ) -> Optional[CityAddress]:
        """
        Get city address from postcode, using cache if available
        
        Args:
            postcode: Postal code to look up
            zipcode_cache: Cache of previous lookups
            
        Returns:
            CityAddress object if found, None otherwise
        """
        if postcode in zipcode_cache:
            return zipcode_cache[postcode]
            
        try:
            city_address = zipcode_to_address(postcode)
            zipcode_cache[postcode] = city_address
            return city_address
        except Exception as e:
            self.stdout.write(f"Error getting city for postcode {postcode}: {str(e)}")
            return None

    def bulk_update_companies(self, companies: List[Company]) -> None:
        """
        Bulk update companies to the database
        
        Args:
            companies: List of companies to update
        """
        try:
            Company.objects.bulk_update(companies, ['city'])
            self.stdout.write(f"Bulk updated {len(companies)} companies")
        except Exception as e:
            self.stdout.write(f"Error during bulk update: {str(e)}")
