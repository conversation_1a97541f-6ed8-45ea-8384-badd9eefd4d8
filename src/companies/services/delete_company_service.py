from dataclasses import dataclass
from logging import getLogger
from typing import Optional

from django.db.models import Q

from companies.models import Company, ShopDealer
from kanna_api.models import UserKanna

logger = getLogger(__name__)


@dataclass
class DeleteCompanyService:
    company: Company

    def validate_linked_to_approve_flow(self) -> Optional[str]:
        approve_steps = self.company.company_approve_steps.exists()
        if approve_steps > 0:
            return f"{self.company.name} が承認フローに存在します」"
        return None

    def validate_link_to_user(self) -> Optional[str]:
        users = UserKanna.objects.filter(company=self.company)
        if users.exists():
            return f"{self.company.name} はユーザーに紐づいています。"
        return None

    def delete_related_data(self):
        ShopDealer.objects.filter(
            Q(shop=self.company) | Q(dealer=self.company)
        ).delete()

    def execute(self):
        deletable_message = self.get_deletable_message()
        if deletable_message:
            raise ValueError(deletable_message)
        self.company.delete()
        self.delete_related_data()

    def get_deletable_message(self) -> Optional[str]:
        validators = [
            self.validate_linked_to_approve_flow,
            self.validate_link_to_user,
        ]
        for validator in validators:
            message = validator()
            if message:
                return message
        return None
