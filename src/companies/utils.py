from companies.models import Company, CompanyType, ShopDealer
from prefectures.models import Prefectures


def create_companies(data, company_type):
    # company_name = []
    for row in data:
        if len(row) < 1:
            continue
        # company_name.append(row[0])
        # import collections
        # print([item for item, count in collections.Counter(company_name).items() if count > 1])
        # print(len(company_name))
        if row[0] == "name":
            continue
        prefectures = Prefectures.objects.filter(name=row[5]).first()
        if prefectures is None:
            prefectures = Prefectures.objects.create(name=row[5])
        prefectures.name = row[5]
        prefectures.save()
        if prefectures is not None:
            if row[0] != "":
                company = Company.objects.filter(name=row[0], type=company_type).first()
                if company is None:
                    company = Company.objects.create(
                        name=row[0],
                        postcode=row[1],
                        address=row[2],
                        tel=row[3],
                        fax=row[4],
                        prefectures_id=prefectures.id,
                        email=row[6],
                        shop_no=row[7],
                        type=company_type,
                    )

                    print("created", company, company_type)
                else:
                    print("already exist", company, company_type)
        else:
            print("this prefectures is None", row[6])

    return True


def create_relations_shop_dealer(data):
    for row in data:
        if len(row) < 1:
            continue
        if row[1] == "shop_name":
            continue
        shop = Company.objects.filter(name=row[1], type=CompanyType.SHOP).first()
        if shop is None:
            print("shop is None", row[1])
            continue
        dealer = Company.objects.filter(name=row[0], type=CompanyType.DEALER).first()
        if dealer is None:
            print("dealer is None", row[0])
            continue
        # try:
        obj, is_created = ShopDealer.objects.get_or_create(shop=shop, dealer=dealer)

        if is_created:
            print("created", obj, shop, " - ", dealer)
        else:
            print("already exist", obj, shop, " - ", dealer)
        # except Exception as err:
        #     print(err)
    return True
