from logging import getLogger

from django.db import models

from prefectures.models import Prefectures
from utilities.address.address import ensure_prefecture_city_in_address
from utilities.model.custom_model import CustomModel
from utilities.zipcode.zipcode import (
    extract_zipcode_from_string,
    zipcode_to_address,
)

logger = getLogger(__name__)


class CompanyType(models.TextChoices):
    SHOP = "SHOP"
    DEALER = "DEALER"
    ACT = "ACT"


class Company(CustomModel):
    id = models.AutoField(primary_key=True)
    shop_no = models.CharField(blank=True, null=True, max_length=30)
    kanna_uuid = models.UUIDField(unique=True, blank=True, null=True)
    prefectures_id = models.IntegerField(blank=True)
    name = models.CharField(blank=True, max_length=75)
    address = models.CharField(blank=True, max_length=255)
    postcode = models.CharField(max_length=50, blank=True)
    city = models.CharField(max_length=50, blank=True, default="")
    tel = models.Char<PERSON>ield(max_length=30, null=True, blank=True, default="")
    fax = models.CharField(max_length=30, null=True, blank=True, default="")
    email = models.CharField(max_length=255, blank=True, null=True, default="")
    type = models.CharField(
        max_length=16, choices=CompanyType.choices, default=None, blank=True
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-id"]

    def __str__(self) -> str:
        return f"{self.type} - {self.name}"

    def update_city(self):
        city = zipcode_to_address(self.real_postcode)
        if city:
            self.city = city.city
            self.save()

    @property
    def prefecture_name(self) -> str:
        prefecture = Prefectures.objects.filter(pk=self.prefectures_id).first()
        if prefecture:
            return prefecture.name
        return ""

    @property
    def full_address(self) -> str:
        return ensure_prefecture_city_in_address(
            prefecture=self.prefecture_name,
            city=self.city,
            address=self.address,
        )

    @property
    def real_postcode(self) -> str:
        postcode_str = str(self.postcode)
        return extract_zipcode_from_string(postcode_str)

    def should_update_city(self):
        """
        Determines whether the city field should be updated.
        Returns True if city is empty on creation or if postcode has changed on update.
        """
        if not self.pk:
            # New instance: update city if it is empty
            return not self.city

        # Existing instance: update city if postcode has changed
        old = self.__class__.objects.get(pk=self.pk)
        return old.postcode != self.postcode

    def save(self, *args, **kwargs):
        """
        Save the model instance.

        If `should_update_city()` returns True, update the `city` field using
        the result of `zipcode_to_address(self.real_postcode)` before saving.
        """
        if self.should_update_city():
            address = zipcode_to_address(self.real_postcode)
            if address:
                logger.info(
                    f"Updating city for company '{self.name}' from '{self.city}' to '{address.city}'"
                )
                self.city = address.city

        super().save(*args, **kwargs)

    @property
    def is_shop(self) -> bool:
        return self.type == CompanyType.SHOP

    @property
    def is_dealer(self) -> bool:
        return self.type == CompanyType.DEALER


class ShopDealer(models.Model):
    shop = models.ForeignKey(
        Company, on_delete=models.DO_NOTHING, null=True, related_name="shop"
    )
    dealer = models.ForeignKey(
        Company, on_delete=models.DO_NOTHING, null=True, related_name="dealer"
    )
