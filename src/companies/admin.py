from django.contrib import admin

from prefectures.models import Prefectures

from .models import Company, ShopDealer


class CompanyAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "shop_no",
        "name",
        "type",
        "get_prefectures_name",
        "is_active",
        "created_at",
    ]
    search_fields = ["name"]

    def get_prefectures_name(self, obj):
        prefectures = Prefectures.objects.filter(id=obj.prefectures_id).first()
        prefectures_name = ""
        if prefectures is not None:
            prefectures_name = prefectures.name
        return prefectures_name


class ShopDealerAdmin(admin.ModelAdmin):
    list_display = ["id", "shop", "dealer"]


admin.site.register(Company, CompanyAdmin)
admin.site.register(ShopDealer, ShopDealerAdmin)
