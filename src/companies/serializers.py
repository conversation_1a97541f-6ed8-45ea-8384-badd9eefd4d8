from rest_framework import serializers

from django.db import transaction
from django.db.models import Q
from django.shortcuts import get_object_or_404

from approve_flow_settings.models import ApproveFlowSetting
from companies.models import Company, CompanyType, ShopDealer
from companies.constants import SHOP_TYPE_CHOICES
from companies.services.delete_company_service import DeleteCompanyService
from prefectures.models import Prefectures
from prefectures.serializers import PrefecturesSerializer


class CompanySerializer(serializers.ModelSerializer):
    prefectures = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Company
        fields = (
            "id",
            "shop_no",
            "prefectures",
            "postcode",
            "name",
            "type",
            "address",
            "email",
            "tel",
            "fax",
            "is_active",
        )

    def get_prefectures(self, obj):
        prefectures = Prefectures.objects.filter(pk=obj.prefectures_id).first()
        if prefectures:
            return PrefecturesSerializer(prefectures).data
        return None


class ShopperDealerCreateSerializer(serializers.ModelSerializer):
    dealer_id = serializers.IntegerField(required=False)

    class Meta:
        model = Company
        fields = (
            "shop_no",
            "prefectures_id",
            "dealer_id",
            "name",
            "address",
            "postcode",
            "tel",
            "fax",
            "email",
            "type",
        )

    def validate_prefectures_id(self, prefectures_id):
        if not prefectures_id:
            raise serializers.ValidationError("Prefecture is invalid.")
        get_object_or_404(Prefectures, id=prefectures_id)

        return prefectures_id

    def validate_name(self, name):
        if not name:
            raise serializers.ValidationError("Please enter company name.")
        return name

    def validate_dealer_id(self, dealer_id):
        if dealer_id:
            get_object_or_404(Company, pk=dealer_id, type=CompanyType.DEALER)

        return dealer_id

    @transaction.atomic
    def create(self, validated_data):
        dealer_id = validated_data.pop("dealer_id", None)

        instance = Company.objects.create(**validated_data)

        if dealer_id:
            dealer = Company.objects.get(pk=dealer_id)
            ShopDealer.objects.create(shop=instance, dealer=dealer)

        return instance


class DealerUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = (
            "name",
            "postcode",
            "address",
            "tel",
            "fax",
            "prefectures_id",
            "email",
        )

    def validate_prefectures_id(self, prefectures_id):
        if not prefectures_id:
            raise serializers.ValidationError("Prefecture is invalid.")
        get_object_or_404(Prefectures, id=prefectures_id)

        return prefectures_id

    def update(self, instance: Company, validated_data):
        for key, value in validated_data.items():
            setattr(instance, key, value)

        instance.save()

        return instance


class ShopperUpdateSerializer(serializers.ModelSerializer):
    dealer_id = serializers.IntegerField(required=False)
    prefectures_id = serializers.IntegerField()

    class Meta:
        model = Company
        fields = (
            "name",
            "postcode",
            "address",
            "tel",
            "fax",
            "prefectures_id",
            "email",
            "shop_no",
            "dealer_id",
        )

    def validate_prefectures_id(self, prefectures_id):
        if not prefectures_id:
            raise serializers.ValidationError("Prefecture is missing.")
        get_object_or_404(Prefectures, id=prefectures_id)

        return prefectures_id

    def validate_dealer_id(self, dealer_id):
        if dealer_id is None:
            return dealer_id

        instance_dealer_id = None
        if self.instance.shop.first():
            instance_dealer_id = self.instance.shop.first().dealer_id

        if instance_dealer_id == dealer_id:
            return dealer_id

        if not Company.objects.filter(type=CompanyType.DEALER, pk=dealer_id).exists():
            raise serializers.ValidationError("Dealer is not exists.")

        if ApproveFlowSetting.objects.filter(
            shop__id=self.instance.id, dealer__id=dealer_id
        ).exists():
            raise serializers.ValidationError("Approve flow is exists.")

        return dealer_id

    @transaction.atomic
    def update(self, instance: Company, validated_data):
        dealer_id = validated_data.pop("dealer_id", None)
        for key, value in validated_data.items():
            setattr(instance, key, value)

        instance.save()

        if dealer_id and instance.shop.first():
            shopdealer_instance = instance.shop.first()
            shopdealer_instance.dealer_id = dealer_id
            shopdealer_instance.save()

        if dealer_id and not instance.shop.first():
            dealer = Company.objects.get(pk=dealer_id)
            ShopDealer.objects.create(shop=instance, dealer=dealer)

        if not dealer_id:
            shop_dealer = ShopDealer.objects.filter(shop=instance)

            shop_dealer_instance = shop_dealer.first()

            if shop_dealer_instance:
                if ApproveFlowSetting.objects.filter(
                    shop=shop_dealer_instance.shop, dealer=shop_dealer_instance.dealer
                ).exists():
                    raise serializers.ValidationError("Approve flow is exists.")

                shop_dealer.delete()

        return instance


class CompanyDropdownSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = (
            "id",
            "name",
            "type",
        )


class DealerOfShopSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = (
            "id",
            "name",
            "is_active",
        )


class ShopDealerInformationSerializer(CompanySerializer):
    dealer = serializers.SerializerMethodField(read_only=True)
    is_deletable = serializers.SerializerMethodField(read_only=True)
    deletable_message = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Company
        fields = (
            "id",
            "shop_no",
            "prefectures",
            "postcode",
            "name",
            "type",
            "address",
            "email",
            "tel",
            "fax",
            "dealer",
            "is_active",
            "is_deletable",
            "deletable_message",
        )

    def get_prefectures(self, obj):
        prefectures = Prefectures.objects.filter(pk=obj.prefectures_id).first()
        if prefectures:
            return PrefecturesSerializer(prefectures).data

        return None

    def get_dealer(self, obj):
        if obj.type == CompanyType.DEALER:
            return None

        shop_dealer = ShopDealer.objects.filter(shop=obj).first()

        if not shop_dealer:
            return None

        dealer = Company.objects.filter(
            pk=shop_dealer.dealer_id, type=CompanyType.DEALER
        ).first()

        if dealer:
            return DealerOfShopSerializer(dealer).data

        return None

    def get_is_deletable(self, obj: Company) -> bool:
        if ShopDealer.objects.filter(Q(shop=obj) | Q(dealer=obj)).exists():
            return False

        if obj.company_approve_steps.exists():
            return False

        return True

    def get_deletable_message(self, obj: Company) -> str:
        delete_company_service = DeleteCompanyService(company=obj)
        return delete_company_service.get_deletable_message()


class ShopDealerCsvSerializer(ShopDealerInformationSerializer):

    company_type = serializers.SerializerMethodField(read_only=True)
    company_address = serializers.SerializerMethodField(read_only=True)
    dealer_name = serializers.SerializerMethodField(read_only=True)
    tel_number = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Company
        fields = (
            "name",
            "dealer_name",
            "type",
            "tel_number",
            "company_address",
            "company_type",
        )

    def get_company_type(self, obj: Company) -> str:
        return SHOP_TYPE_CHOICES.get(obj.type, "")

    def get_company_address(self, obj: Company) -> str:
        return (
            f"{obj.real_postcode} {obj.address}" if obj.address and obj.postcode else ""
        )

    def get_dealer_name(self, obj: Company) -> str:
        dealer = self.get_dealer(obj)
        if dealer:
            return dealer.get("name", "")
        return ""

    def get_tel_number(self, obj: Company) -> str:
        return obj.tel if obj.tel else ""
