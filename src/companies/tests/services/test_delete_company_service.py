from unittest.mock import patch

from django.test import TestCase

from approve_flow.tests.factories import ApproveFlowStepFactory
from companies.factories import CompanyFactory, ShopDealerFactory
from companies.models import Company, CompanyType
from companies.services.delete_company_service import DeleteCompanyService
from prefectures.factories import PrefecturesFactory
from users.factories import UserKannaFactory


class DeleteCompanyServiceTest(TestCase):
    """Test cases for DeleteCompanyService"""

    def setUp(self):
        """Set up test data"""
        self.prefecture = PrefecturesFactory()
        self.company = CompanyFactory(
            name="Test Company", prefectures_id=self.prefecture.id
        )
        self.user_kanna = UserKannaFactory(name="Test User", email="<EMAIL>")
        self.service = DeleteCompanyService(company=self.company)

    def test_init_service(self):
        """Test service initialization"""
        self.assertEqual(self.service.company, self.company)
        self.assertEqual(self.service.user, self.user_kanna)

    def test_check_linked_to_approve_flow_no_steps(self):
        """Test check_linked_to_approve_flow when no approve steps exist"""
        try:
            self.service.validate_linked_to_approve_flow()
        except ValueError:
            self.fail("check_linked_to_approve_flow raised ValueError unexpectedly")

    def test_check_linked_to_approve_flow_with_steps(self):
        """Test check_linked_to_approve_flow when approve steps exist"""
        ApproveFlowStepFactory(company=self.company)

        with self.assertRaises(ValueError) as context:
            self.service.validate_linked_to_approve_flow()

        expected_message = f"{self.company.name} が承認フローに存在します」"
        self.assertEqual(str(context.exception), expected_message)

    def test_check_linked_to_shop_dealer_no_relations(self):
        """Test check_linked_to_shop_dealer when no ShopDealer relations exist"""
        try:
            self.service.check_linked_to_shop_dealer()
        except ValueError:
            self.fail("check_linked_to_shop_dealer raised ValueError unexpectedly")

    def test_check_linked_to_shop_dealer_as_shop(self):
        """Test check_linked_to_shop_dealer when company is linked as shop"""
        ShopDealerFactory(shop=self.company)

        with self.assertRaises(ValueError) as context:
            self.service.check_linked_to_shop_dealer()

        expected_message = f"{self.company.name} はメイクアップショップに紐づいています。"
        self.assertEqual(str(context.exception), expected_message)

    def test_check_linked_to_shop_dealer_as_dealer(self):
        """Test check_linked_to_shop_dealer when company is linked as dealer"""
        self.company.type = CompanyType.DEALER
        ShopDealerFactory(dealer=self.company)

        with self.assertRaises(ValueError) as context:
            self.service.check_linked_to_shop_dealer()

        expected_message = f"{self.company.name} はディーラーに紐づいています。"
        self.assertEqual(str(context.exception), expected_message)

    def test_execute_successful_deletion(self):
        """Test successful execution of company deletion"""
        company_id = self.company.id

        self.assertTrue(Company.objects.filter(id=company_id).exists())

        self.service.execute()
        self.assertFalse(Company.objects.filter(id=company_id).exists())

    def test_execute_fails_with_shop_dealer_relation(self):
        """Test execute fails when company has ShopDealer relations"""
        ShopDealerFactory(shop=self.company)

        company_id = self.company.id
        with self.assertRaises(ValueError):
            self.service.execute()

        self.assertTrue(Company.objects.filter(id=company_id).exists())

    def test_execute_fails_with_approve_flow_steps(self):
        """Test execute fails when company has approve flow steps"""
        ApproveFlowStepFactory(company=self.company)
        company_id = self.company.id

        with self.assertRaises(ValueError):
            self.service.execute()

        self.assertTrue(Company.objects.filter(id=company_id).exists())

    def test_execute_order_of_checks(self):
        """Test that execute runs checks in correct order"""
        ShopDealerFactory(shop=self.company)
        ApproveFlowStepFactory(company=self.company)

        with self.assertRaises(ValueError) as context:
            self.service.execute()

        expected_message = f"{self.company.name} はメイクアップショップに紐づいています。"
        self.assertEqual(str(context.exception), expected_message)

    @patch("companies.services.delete_company_service.logger")
    def test_logging_on_successful_deletion(self, mock_logger):
        """Test that logging works correctly (if logging is added to the service)"""
        self.service.execute()
        self.assertFalse(Company.objects.filter(id=self.company.id).exists())

    def test_multiple_shop_dealer_relations(self):
        """Test check when company has multiple ShopDealer relations"""
        ShopDealerFactory.create_batch(2, shop=self.company)
        with self.assertRaises(ValueError) as context:
            self.service.check_linked_to_shop_dealer()

        expected_message = f"{self.company.name} はメイクアップショップに紐づいています。"
        self.assertEqual(str(context.exception), expected_message)

    def test_company_with_special_characters_in_name(self):
        """Test service with company name containing special characters"""
        special_company = CompanyFactory(
            name="テスト会社 & Co. (株)", prefectures_id=self.prefecture.id
        )
        service = DeleteCompanyService(company=special_company)

        ShopDealerFactory(shop=special_company)
        with self.assertRaises(ValueError) as context:
            service.check_linked_to_shop_dealer()

        expected_message = f"{special_company.name} はメイクアップショップに紐づいています。"
        self.assertEqual(str(context.exception), expected_message)
