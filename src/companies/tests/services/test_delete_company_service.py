from unittest.mock import patch

from django.test import TestCase

from approve_flow.tests.factories import ApproveFlowSettingInstanceFactory
from companies.factories import CompanyFactory, ShopDealerFactory
from companies.models import Company, CompanyType
from companies.services.delete_company_service import DeleteCompanyService
from prefectures.factories import PrefecturesFactory
from users.factories import UserKannaFactory


class DeleteCompanyServiceTest(TestCase):
    """Test cases for DeleteCompanyService"""

    def setUp(self):
        """Set up test data"""
        self.prefecture = PrefecturesFactory()
        self.company = CompanyFactory(
            name="Test Company", prefectures_id=self.prefecture.id
        )
        self.service = DeleteCompanyService(company=self.company)

    def test_init_service(self):
        """Test service initialization"""
        self.assertEqual(self.service.company, self.company)

    def test_validate_linked_to_approve_flow_no_steps(self):
        """Test validate_linked_to_approve_flow when no approve steps exist"""
        result = self.service.validate_linked_to_approve_flow_setting()
        self.assertIsNone(result)

    def test_validate_linked_to_approve_flow_with_approve_flows(self):
        """Test validate_linked_to_approve_flow when approve flows exist"""
        approve_flow = ApproveFlowSettingInstanceFactory(shop=self.company)

        result = self.service.validate_linked_to_approve_flow_setting()
        expected_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        self.assertEqual(result, expected_message)

    def test_validate_linked_to_approve_flow_with_multiple_approve_flows(self):
        """Test validate_linked_to_approve_flow when multiple approve flows exist"""
        approve_flow1 = ApproveFlowSettingInstanceFactory(shop=self.company)
        approve_flow2 = ApproveFlowSettingInstanceFactory(dealer=self.company)

        result = self.service.validate_linked_to_approve_flow_setting()
        expected_message = (
            f"承認フローに使用しています。承認フローID： {approve_flow1.id}, {approve_flow2.id}"
        )
        self.assertEqual(result, expected_message)

    def test_validate_linked_to_approve_flow_with_act_relationship(self):
        """Test validate_linked_to_approve_flow when company is linked as ACT"""
        approve_flow = ApproveFlowSettingInstanceFactory(act=self.company)

        result = self.service.validate_linked_to_approve_flow_setting()
        expected_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        self.assertEqual(result, expected_message)

    def test_validate_link_to_user_no_users(self):
        """Test validate_link_to_user when no users are linked to the company"""
        result = self.service.validate_link_to_user()
        self.assertIsNone(result)

    def test_validate_link_to_user_with_single_user(self):
        """Test validate_link_to_user when a single user is linked to the company"""
        user = UserKannaFactory(company=self.company)

        result = self.service.validate_link_to_user()
        expected_message = f"担当者で指定されています。担当者ID：{user.id}"
        self.assertEqual(result, expected_message)

    def test_validate_link_to_user_with_multiple_users(self):
        """Test validate_link_to_user when multiple users are linked to the company"""
        users = UserKannaFactory.create_batch(3, company=self.company)

        result = self.service.validate_link_to_user()
        user_ids = [str(user.id) for user in users]
        expected_message = f"担当者で指定されています。担当者ID：{', '.join(user_ids)}"
        self.assertEqual(result, expected_message)

    def test_validate_link_to_user_with_inactive_user(self):
        """Test validate_link_to_user when inactive user is linked to the company"""
        user = UserKannaFactory(company=self.company, is_active=False)

        result = self.service.validate_link_to_user()
        expected_message = f"担当者で指定されています。担当者ID：{user.id}"
        self.assertEqual(result, expected_message)

    def test_validate_link_to_user_with_users_from_different_company(self):
        """Test validate_link_to_user when users are linked to different companies"""
        other_company = CompanyFactory(
            name="Other Company", prefectures_id=self.prefecture.id
        )
        UserKannaFactory(company=other_company)

        result = self.service.validate_link_to_user()
        self.assertIsNone(result)

    def test_validate_link_to_user_with_special_characters_in_company_name(self):
        """Test validate_link_to_user with company name containing special characters"""
        special_company = CompanyFactory(
            name="テスト会社 & Co. (株)", prefectures_id=self.prefecture.id
        )
        service = DeleteCompanyService(company=special_company)
        user = UserKannaFactory(company=special_company)

        result = service.validate_link_to_user()
        expected_message = f"担当者で指定されています。担当者ID：{user.id}"
        self.assertEqual(result, expected_message)

    def test_get_deletable_message_no_issues(self):
        """Test get_deletable_message when company can be deleted (no validation issues)"""
        result = self.service.get_deletable_message()
        self.assertIsNone(result)

    def test_get_deletable_message_with_approve_flows(self):
        """Test get_deletable_message when company has approve flows"""
        approve_flow = ApproveFlowSettingInstanceFactory(shop=self.company)

        result = self.service.get_deletable_message()
        expected_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        self.assertEqual(result, expected_message)

    def test_get_deletable_message_with_linked_users(self):
        """Test get_deletable_message when company has linked users"""
        user = UserKannaFactory(company=self.company)

        result = self.service.get_deletable_message()
        expected_message = f"担当者で指定されています。担当者ID：{user.id}"
        self.assertEqual(result, expected_message)

    def test_get_deletable_message_collects_all_errors(self):
        """Test get_deletable_message collects ALL validation errors instead of stopping at first"""
        approve_flow = ApproveFlowSettingInstanceFactory(shop=self.company)
        user = UserKannaFactory(company=self.company)

        result = self.service.get_deletable_message()
        # Should return both error messages joined with newline
        approve_flow_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        user_message = f"担当者で指定されています。担当者ID：{user.id}"
        expected_message = f"{approve_flow_message}\n{user_message}"
        self.assertEqual(result, expected_message)

    def test_get_deletable_message_multiple_users_single_message(self):
        """Test get_deletable_message returns single message even with multiple users"""
        users = UserKannaFactory.create_batch(3, company=self.company)

        result = self.service.get_deletable_message()
        user_ids = [str(user.id) for user in users]
        expected_message = f"担当者で指定されています。担当者ID：{', '.join(user_ids)}"
        self.assertEqual(result, expected_message)

    def test_get_deletable_message_multiple_approve_flows_and_users(self):
        """Test get_deletable_message with multiple approve flows and multiple users"""
        approve_flow1 = ApproveFlowSettingInstanceFactory(shop=self.company)
        approve_flow2 = ApproveFlowSettingInstanceFactory(dealer=self.company)
        users = UserKannaFactory.create_batch(2, company=self.company)

        result = self.service.get_deletable_message()
        approve_flow_message = (
            f"承認フローに使用しています。承認フローID： {approve_flow1.id}, {approve_flow2.id}"
        )
        user_ids = [str(user.id) for user in users]
        user_message = f"担当者で指定されています。担当者ID：{', '.join(user_ids)}"
        expected_message = f"{approve_flow_message}\n{user_message}"
        self.assertEqual(result, expected_message)

    def test_execute_successful_deletion(self):
        """Test successful execution of company deletion"""
        company_id = self.company.id

        self.assertTrue(Company.objects.filter(id=company_id).exists())

        self.service.execute()
        self.assertFalse(Company.objects.filter(id=company_id).exists())

    def test_execute_fails_with_approve_flows(self):
        """Test execute fails when company has approve flows"""
        approve_flow = ApproveFlowSettingInstanceFactory(shop=self.company)
        company_id = self.company.id

        with self.assertRaises(ValueError) as context:
            self.service.execute()

        expected_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        self.assertEqual(str(context.exception), expected_message)
        self.assertTrue(Company.objects.filter(id=company_id).exists())

    def test_execute_fails_with_linked_users(self):
        """Test execute fails when company has linked users"""
        user = UserKannaFactory(company=self.company)
        company_id = self.company.id

        with self.assertRaises(ValueError) as context:
            self.service.execute()

        expected_message = f"担当者で指定されています。担当者ID：{user.id}"
        self.assertEqual(str(context.exception), expected_message)
        self.assertTrue(Company.objects.filter(id=company_id).exists())

    def test_execute_fails_with_multiple_validation_errors(self):
        """Test that execute fails with all validation errors combined"""
        approve_flow = ApproveFlowSettingInstanceFactory(shop=self.company)
        user = UserKannaFactory(company=self.company)

        with self.assertRaises(ValueError) as context:
            self.service.execute()

        # Should fail with both error messages joined with newline
        approve_flow_message = f"承認フローに使用しています。承認フローID： {approve_flow.id}"
        user_message = f"担当者で指定されています。担当者ID：{user.id}"
        expected_message = f"{approve_flow_message}\n{user_message}"
        self.assertEqual(str(context.exception), expected_message)

    @patch("companies.services.delete_company_service.logger")
    def test_logging_on_successful_deletion(self, mock_logger):
        """Test that logging works correctly (if logging is added to the service)"""
        self.service.execute()
        self.assertFalse(Company.objects.filter(id=self.company.id).exists())
