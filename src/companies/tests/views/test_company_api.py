from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.urls import reverse
from faker import Faker
from rest_framework import status
from rest_framework.test import APITestCase

from api.constants import KannaUserRoleEnum
from authentication.factories import UserFactory
from companies.factories import CompanyFactory
from companies.models import CompanyType, Company
from kanna_api.models import UserKanna
from prefectures.factories import PrefecturesFactory
from users.factories import UserKannaFactory

User = get_user_model()


class CompanyAPITests(APITestCase):
    @classmethod
    def setUpTestData(cls):
        call_command("loaddata", "fixtures/01-master/00-role.json")

    def setUp(self):
        self.faker = Faker()
        self.prefecture = PrefecturesFactory()
        self.user = UserFactory()
        admin_email = self.faker.email()
        self.admin_user = UserFactory(email=admin_email)

    def test_get_company_list(self):
        CompanyFactory.create_batch(10, type=CompanyType.ACT)
        url = reverse("companies:companies-list", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 10)

    def test_get_company_list_with_filters(self):
        company1 = CompanyFactory.create(
            name="Test Company 1",
            type=CompanyType.SHOP,
            prefectures_id=self.prefecture.id,
            is_active=True,
        )
        company2 = CompanyFactory.create(
            name="Another Company",
            type=CompanyType.DEALER,
            prefectures_id=self.prefecture.id,
            is_active=False,
        )
        company3 = CompanyFactory.create(
            name="Test Company 2", type=CompanyType.ACT, is_active=True
        )

        url = reverse("companies:companies-list", kwargs={"version": "v1"})

        response = self.client.get(url + "?keyword=Test")
        data = response.json()
        self.assertEqual(len(data["results"]), 2)

        response = self.client.get(url + "?is_active=False")
        data = response.json()
        self.assertEqual(len(data["results"]), 1)
        self.assertEqual(data["results"][0]["name"], "Another Company")

        response = self.client.get(url + "?type=SHOP")
        data = response.json()
        self.assertEqual(len(data["results"]), 1)
        self.assertEqual(data["results"][0]["type"], CompanyType.SHOP)

    def test_get_company_detail(self):
        company = CompanyFactory.create(
            name="Test Company",
            type=CompanyType.SHOP,
            prefectures_id=self.prefecture.id,
        )
        url = reverse(
            "companies:companies-detail", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(data["name"], "Test Company")
        self.assertEqual(data["type"], CompanyType.SHOP)

    def test_create_company_unauthorized(self):
        url = reverse("companies:companies-list", kwargs={"version": "v1"})
        data = {
            "name": "New Company",
            "type": CompanyType.SHOP,
            "prefectures_id": self.prefecture.id,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_company_authorized(self):
        self.client.force_authenticate(user=self.user)
        url = reverse("companies:companies-list", kwargs={"version": "v1"})
        data = {
            "name": "New Company",
            "type": CompanyType.SHOP,
            "prefectures_id": self.prefecture.id,
            "address": "Test Address",
            "email": "<EMAIL>",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Company.objects.filter(name="New Company").exists())

    def test_update_company_unauthorized(self):
        company = CompanyFactory.create()
        url = reverse(
            "companies:companies-detail", kwargs={"version": "v1", "pk": company.pk}
        )
        data = {"name": "Updated Company"}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_company_unauthorized(self):
        company = CompanyFactory.create()
        url = reverse(
            "companies:companies-detail", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_company_non_admin(self):
        UserKannaFactory.create(user=self.user, role_id=KannaUserRoleEnum.SHOP.value)
        self.client.force_authenticate(user=self.user)
        company = CompanyFactory.create()
        url = reverse(
            "companies:companies-detail", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_company_admin(self):
        UserKanna.objects.create(
            user_uuid=self.faker.uuid4(),
            email=self.admin_user.email,
            name=self.faker.name(),
            role_id=KannaUserRoleEnum.ADMIN.value,
            user=self.admin_user,
        )
        self.client.force_authenticate(user=self.admin_user)
        company = CompanyFactory.create()
        url = reverse(
            "companies:companies-detail", kwargs={"version": "v1", "pk": company.pk}
        )

        with patch(
            "companies.services.delete_company_service.DeleteCompanyService.execute"
        ) as mock_delete:
            mock_delete.return_value = True
            response = self.client.delete(url)
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_member_of_company(self):
        companies = CompanyFactory.create_batch(10)
        url = reverse(
            "companies:companies-members",
            kwargs={"version": "v1", "pk": companies[0].pk},
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 0)

        UserKannaFactory.create_batch(10, company_id=companies[0].pk)
        url = reverse(
            "companies:companies-members",
            kwargs={"version": "v1", "pk": companies[0].pk},
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 10)

    def test_member_of_company_invalid_company(self):
        url = reverse(
            "companies:companies-members",
            kwargs={"version": "v1", "pk": 99999},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_users_for_company(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory.create(prefectures_id=prefectures.id)
        company2 = CompanyFactory.create(prefectures_id=prefectures.id)
        UserFactory.create_batch(19, company_id=company.pk)
        UserFactory.create_batch(2, company_id=company2.pk)
        url = reverse(
            "companies:companies-owners", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(data["count"], 19)
        self.assertEqual(len(data["results"]), 19)

        response = self.client.get(url + "?is_active=false")
        data = response.json()
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["results"]), 0)

        response = self.client.get(url + "?is_active=true")
        data = response.json()
        self.assertEqual(data["count"], 19)
        self.assertEqual(len(data["results"]), 19)

    def test_get_users_for_company_with_keyword_search(self):
        company = CompanyFactory.create(prefectures_id=self.prefecture.id)
        user1 = UserFactory.create(
            company_id=company.pk, first_name="John", last_name="Doe"
        )
        user2 = UserFactory.create(
            company_id=company.pk, first_name="Jane", last_name="Smith"
        )
        user3 = UserFactory.create(
            company_id=company.pk, first_name="Bob", last_name="Johnson"
        )

        url = reverse(
            "companies:companies-owners", kwargs={"version": "v1", "pk": company.pk}
        )

        response = self.client.get(url + "?keyword=john")
        data = response.json()
        self.assertEqual(data["count"], 2)

        response = self.client.get(url + "?keyword=jane")
        data = response.json()
        self.assertEqual(data["count"], 1)

    def test_get_users_for_nonexistent_company(self):
        url = reverse(
            "companies:companies-owners", kwargs={"version": "v1", "pk": 99999}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_approve_owner_unauthorized(self):
        company = CompanyFactory.create()
        url = reverse(
            "companies:companies-approve", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.post(url, {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_companies_dropdown(self):
        CompanyFactory.create(type=CompanyType.SHOP, name="Shop 1")
        CompanyFactory.create(type=CompanyType.DEALER, name="Dealer 1")
        CompanyFactory.create(type=CompanyType.ACT, name="ACT 1")

        url = reverse(
            "companies:companies-companies_drop_down", kwargs={"version": "v1"}
        )

        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data), 3)

        response = self.client.get(url + "?type=SHOP")
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["type"], CompanyType.SHOP)

        response = self.client.get(url + "?type=DEALER")
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["type"], CompanyType.DEALER)
