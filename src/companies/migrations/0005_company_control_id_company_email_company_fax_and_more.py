# Generated by Django 4.0.6 on 2022-09-27 08:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0004_alter_company_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='control_id',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='email',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='fax',
            field=models.CharField(blank=True, default='', max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='postcode',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='company',
            name='tel',
            field=models.CharField(blank=True, default='', max_length=30, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='company',
            name='kanna_uuid',
            field=models.UUIDField(blank=True, null=True, unique=True),
        ),
        migrations.CreateModel(
            name='ShopDealer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dealer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='dealer', to='companies.company')),
                ('shop', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='shop', to='companies.company')),
            ],
        ),
    ]
