# Generated by Django 4.0.6 on 2022-08-29 08:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('kanna_uuid', models.UUIDField(blank=True, unique=True)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=75)),
                ('type', models.CharField(blank=True, choices=[('SHOP', 'Shop'), ('DEALER', 'Dealer'), ('ACT', 'Act')], default=None, max_length=16)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
