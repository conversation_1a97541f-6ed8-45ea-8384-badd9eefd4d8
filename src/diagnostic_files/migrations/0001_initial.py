# Generated by Django 4.1.3 on 2023-09-20 08:57

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("files", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="DiagnosticFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("file_name", models.CharField(blank=True, max_length=255, null=True)),
                ("file_url", models.URLField(blank=True, max_length=255, null=True)),
                ("note", models.TextField(blank=True, default="", null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="files.file",
                    ),
                ),
            ],
            options={
                "db_table": "diagnostic_file",
                "ordering": ["-id"],
            },
        ),
    ]
