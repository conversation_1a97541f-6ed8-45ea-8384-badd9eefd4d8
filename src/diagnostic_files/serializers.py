from rest_framework import serializers

from .models import DiagnosticFile


class DiagnosticFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = DiagnosticFile
        fields = (
            "id",
            "file_name",
            "file_url",
            "note",
            "created_at",
            "updated_at",
        )

        read_only_fields = (
            "id",
            "created_at",
            "updated_at",
        )
