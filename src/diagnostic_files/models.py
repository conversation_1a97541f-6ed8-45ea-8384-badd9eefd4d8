from django.db import models
from django.utils import timezone

from files.models import File
from utilities.model.custom_model import CustomModel


class DiagnosticFile(CustomModel):
    file_name = models.CharField(max_length=255, blank=True, null=True)
    file_url = models.URLField(blank=True, null=True, max_length=255)
    file = models.ForeignKey(File, on_delete=models.DO_NOTHING, null=True, blank=True)
    note = models.TextField(blank=True, null=True, default="")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "diagnostic_file"
        ordering = ["-id"]
