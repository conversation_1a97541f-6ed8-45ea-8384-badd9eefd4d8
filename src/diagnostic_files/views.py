from rest_framework import viewsets

from api.constants import ASCENDING
from utilities.permissions.custom_permission import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .models import DiagnosticFile
from .serializers import DiagnosticFileSerializer

# Create your views here.


class DiagnosticFileViewSet(viewsets.ModelViewSet):
    permission_classes = [HasKannaPermission]
    http_method_names = ["get", "post", "put", "delete"]

    def get_serializer_class(self):
        return DiagnosticFileSerializer

    def sort_sds_price(self, queryset, params_sort, params_direction):
        SORT_FIELDS = {
            "created_at": "created_at",
            "updated_at": "updated_at",
            "file_name": "file_name",
        }
        for key, value in SORT_FIELDS.items():
            if key == params_sort:
                if params_direction == ASCENDING:
                    queryset = queryset.order_by(value)
                else:
                    queryset = queryset.order_by(f"-{value}")

        return queryset

    def get_queryset(self):
        queryset = DiagnosticFile.objects.all()
        param_sort = self.request.GET.get("sort_by")
        order_by_direction = self.request.GET.get("order_by_direction")

        file_name = self.request.GET.get("file_name")
        queryset = (
            queryset.filter(file_name__icontains=file_name) if file_name else queryset
        )

        if param_sort:
            queryset = self.sort_sds_price(queryset, param_sort, order_by_direction)

        return queryset
