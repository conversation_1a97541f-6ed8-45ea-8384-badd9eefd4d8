from rest_framework import permissions, viewsets

from django.db.models import Q

from .models import Prefectures
from .serializers import PrefecturesSerializer


class PrefecturesViewSet(viewsets.ModelViewSet):
    serializer_class = PrefecturesSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        queryset = Prefectures.objects.all()
        name = self.request.query_params.get("name", None)
        if name is not None:
            queryset = queryset.filter(Q(name__icontains=name))
        area_id = self.request.query_params.get("area_id", None)
        if area_id is not None:
            queryset = queryset.filter(area_id=area_id)
        return queryset
