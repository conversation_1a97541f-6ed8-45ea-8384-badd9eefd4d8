from django.db import models
from django.utils import timezone

from areas.models import Area


class Prefectures(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    area = models.ForeignKey(
        Area,
        to_field="id",
        related_name="area_prefectures",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "prefectures"
        ordering = ["id"]
