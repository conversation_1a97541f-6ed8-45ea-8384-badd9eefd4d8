# Generated by Django 4.1.3 on 2022-11-28 08:42

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("areas", "0004_alter_area_options"),
    ]

    operations = [
        migrations.CreateModel(
            name="Prefectures",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "area",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="area_prefectures",
                        to="areas.area",
                    ),
                ),
            ],
            options={
                "db_table": "prefectures",
                "ordering": ["-id"],
            },
        ),
    ]
