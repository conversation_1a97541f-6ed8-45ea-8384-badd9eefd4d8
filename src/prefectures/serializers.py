from rest_framework import serializers

from areas.models import Area
from areas.serializers import AreaSerializer
from prefectures.models import Prefectures


class PrefecturesSerializer(serializers.ModelSerializer):
    area = AreaSerializer(read_only=True)
    area_pk = serializers.PrimaryKeyRelatedField(
        write_only=True,
        queryset=Area.objects.all(),
        source="areas",
        required=True,
        allow_null=True,
    )

    class Meta:
        model = Prefectures
        fields = "__all__"
