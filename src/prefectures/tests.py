from rest_framework.test import APITestCase

from django.urls import reverse

from .factories import AreaFactory, PrefecturesFactory


class PrefecturesAPITests(APITestCase):
    def test_get_prefectures_list(self):
        area = AreaFactory()
        area2 = AreaFactory()
        PrefecturesFactory(area_id=area.pk)
        PrefecturesFactory(area_id=area.pk)
        PrefecturesFactory(area_id=area2.pk)
        url = reverse("prefectures", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(len(data["results"]), 3)

        url = reverse("prefectures", kwargs={"version": "v1"})
        response = self.client.get(url + f"?area_id={area.pk}")
        data = response.json()
        self.assertEqual(len(data["results"]), 2)
