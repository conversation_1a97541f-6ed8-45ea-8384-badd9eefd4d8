from abc import ABC
from logging import getLogger

from django.db.models import Q
from rest_framework.exceptions import PermissionDenied, ValidationError

from api.constants import (
    KANNA_USER_ROLE,
    NOTIFICATION_SEND_TO,
    NOTIFICATION_VERBS, LEVEL_MEMBERS,
)
from approve_flow.models import (
    ApproveFlowStep,
    ApproveFlowStepStatusChoices,
    UserElectronicMark,
)
from approve_flow.services import (
    create_warranty_instances,
    get_current_approve_flow_step_without_level,
    get_current_approve_flow_step_without_level_ms,
    get_current_apprve_flow_step,
    get_current_apprve_flow_step_ms,
    get_min_max_level_in_company,
    get_next_level,
    handle_return_reject_ms,
    handle_return_reject_sds,
    render_to_pdf_and_send_to_kanna_diagnostic,
    render_to_pdf_and_send_to_kanna_management_sheet,
    query_current_approve_flow_step_from_admin,
    query_current_approve_flow_step_from_admin_ms, is_exist_lv3_in_list_lv_in_company
)
from approve_history.models import ApproveList
from cms.services import get_cms_kanna
from companies.models import Company, CompanyType, ShopDealer
from diagnostics.choices import DiagnosticStatus
from kanna_api.models import UserKanna
from management_sheets.choices import ManagementSheetStatus
from notification_settings.models import NotificationEmailSetting
from notification_settings.services import get_list_user_turn_on_email
from templates.emails.mailer import (
    request_approved_approve_flow_ms,
    request_approved_approve_flow_sds,
    send_approved_approve_flow_ms,
    send_approved_approve_flow_sds,
    send_rejected_approve_flow_ms_for_dealer,
    send_rejected_approve_flow_ms_for_shop,
    send_rejected_approve_flow_sds_for_dealer,
    send_rejected_approve_flow_sds_for_shop,
    send_returned_approve_flow_ms_for_dealer,
    send_returned_approve_flow_ms_for_shop,
    send_returned_approve_flow_sds_for_dealer,
    send_returned_approve_flow_sds_for_shop,
)
from utilities.helpers.notifications import (
    send_approve_from_act_notifications,
    send_approve_from_dealer_notifications,
    send_reject_from_act_notifications,
    send_reject_from_dealer_notifications,
    send_return_from_act_notifications,
    send_return_from_dealer_notifications,
    send_submit_from_shop_notifications,
    send_update_after_approve_from_act_notifications,
    send_from_admin_notifications
)
from approve_flow.approve_flow_email_service import ApproveFlowEmailService


logger = getLogger(__name__)


class State(ABC):
    def __init__(
        self, kanna_user, diagnostic_instance, approve_flow_setting_instance, comment=""
    ):
        self.kanna_user = kanna_user
        self.diagnostic_instance = diagnostic_instance
        self.approve_flow_setting_instance = approve_flow_setting_instance
        self.comment = comment

    def _get_last_step(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )
        return last_step

    def handle_approve(self):
        pass

    def handle_reject(self):
        pass

    def handle_submit(self):
        pass

    def handle_return(self):
        pass

    def handle_update_after_approve(self, construction_method_id):
        pass


class StateMS(ABC):
    def __init__(
        self, kanna_user, management_sheet, approve_flow_setting_instance, comment=""
    ):
        self.kanna_user = kanna_user
        self.management_sheet = management_sheet
        self.approve_flow_setting_instance = approve_flow_setting_instance
        self.comment = comment

    def handle_approve(self):
        pass

    def handle_reject(self):
        pass

    def handle_submit(self):
        pass

    def handle_return(self):
        pass


############################
# Diagnostic
############################
# SHOP
class SubmitFromShop(State):
    status = ApproveFlowStepStatusChoices.OPEN
    allowed_status = ()

    def handle_approve(self):
        if self.diagnostic_instance.status == DiagnosticStatus.WAITING_APPROVAL:
            raise ValidationError({"detail": "Diagnosits still in WAITING_APPROVAL"})

        if self.diagnostic_instance.status == DiagnosticStatus.REJECT:
            raise ValidationError({"detail": "Diagnosits was rejected"})

        # Create Approve Flow Step for Shop and assign comment
        shop = self.approve_flow_setting_instance.shop
        kanna_user = self.kanna_user
        shop_step = ApproveFlowStep(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            diagnostic=self.diagnostic_instance,
            user=kanna_user.user,
            level=kanna_user.level,
            company=shop,
            comment=self.comment,
            status=ApproveFlowStepStatusChoices.APPROVED,
        )
        shop_step.save()

        # Get min level and next company
        notification_send_to = ""
        min_level = None
        if dealer := self.approve_flow_setting_instance.dealer:
            if self.approve_flow_setting_instance.is_approve_level_dealer:
                min_level, _ = get_min_max_level_in_company(dealer)
            else:
                min_level = None
            company = dealer
            notification_send_to = NOTIFICATION_SEND_TO["DEALER"]
        else:
            act = self.approve_flow_setting_instance.act
            company = act
            if self.approve_flow_setting_instance.is_approve_level_act:
                min_level, _ = get_min_max_level_in_company(act)
            else:
                min_level = None
            notification_send_to = NOTIFICATION_SEND_TO["ACT"]

        # Create Approve Flow Step for next company
        new_approve_flow_step = ApproveFlowStep(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            diagnostic=self.diagnostic_instance,
            user=None,
            level=min_level,
            company=company,
        )
        new_approve_flow_step.save()

        # Change status of diagnostic
        self.diagnostic_instance.status = DiagnosticStatus.WAITING_APPROVAL
        self.diagnostic_instance.save()

        # Store in Approve List
        ApproveList.objects.get_or_create(
            diagnostic=self.diagnostic_instance,
            document_type=self.diagnostic_instance.type,
        )

        # send mail request approve
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()
        user_email_to = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company=company, is_active=True
        )
        if min_level:
            user_email_to = user_email_to.filter(level=min_level)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid, user_kanna_uuids=user_email_to
        )
        cms = get_cms_kanna(self.diagnostic_instance.cms_uuid, kanna_user=kanna_user)

        request_approved_approve_flow_sds(
            company_name=company.name,
            user_dealer=user_email_to_after_filter,
            user_request=user_create_sds,
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # Submit request approve SDS from Shop
        # send notification to dealer
        send_submit_from_shop_notifications(
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            send_to=notification_send_to,
            next_level=min_level,
        )

        return super().handle_submit()


# DEALER
class ApproveFromDealer(State):
    status = ApproveFlowStepStatusChoices.APPROVED
    allowed_status = ApproveFlowStepStatusChoices.OPEN
    # company_act = Company.objects.filter(type=CompanyType.ACT).first()

    def handle_approve(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        # Check last_step and change stt last_step to APPROVED
        if last_step:
            if last_step.status in (
                ApproveFlowStepStatusChoices.APPROVED,
                ApproveFlowStepStatusChoices.REJECT,
            ):
                raise PermissionDenied({"detail": "Invalid action"})

            last_step.status = ApproveFlowStepStatusChoices.APPROVED
            last_step.user = self.kanna_user.user
            last_step.comment = self.comment
            last_step.save()

        else:
            raise PermissionDenied({"detail": "Invalid action"})

        # @TODO: Refactor this
        # Create new step

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)
        approve_flow_setting_instance = (
            self.diagnostic_instance.diagnostic_approve_flow_setting_instance
        )
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            next_level = get_next_level(self.kanna_user)
            if next_level:
                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    diagnostic=self.diagnostic_instance,
                    user=None,
                    level=next_level,
                    company=approve_flow_setting_instance.dealer,
                )
                new_approve_flow_step.save()
                # if next level is exist then send noti to ACT next level
                # if dealer next level exist then send noti to dealer
                if approve_flow_setting_instance.dealer is not None:
                    send_approve_from_dealer_notifications(
                        target_object=self.diagnostic_instance,
                        kanna_user=self.kanna_user,
                        send_to=NOTIFICATION_SEND_TO["DEALER"],
                        company_id=new_approve_flow_step.company.id,
                        next_level=next_level,
                    )
            else:
                act = Company.objects.get(type=CompanyType.ACT)

                if self.approve_flow_setting_instance.is_approve_level_act:
                    min_level, _ = get_min_max_level_in_company(act)
                else:
                    min_level = None
                user_kanna_act = UserKanna.objects.values_list(
                    "user_uuid", flat=True
                ).filter(role=KANNA_USER_ROLE["ACT"], is_active=True)

                if min_level:
                    user_kanna_act = user_kanna_act.filter(level=min_level)

                user_email_to_after_filter = get_list_user_turn_on_email(
                    cms_uuid=self.diagnostic_instance.cms_uuid,
                    user_kanna_uuids=user_kanna_act,
                )

                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    diagnostic=self.diagnostic_instance,
                    level=min_level,
                    company=approve_flow_setting_instance.act,
                )
                new_approve_flow_step.save()

                # send notification to ACT
                send_approve_from_dealer_notifications(
                    target_object=self.diagnostic_instance,
                    kanna_user=self.kanna_user,
                    send_to=NOTIFICATION_SEND_TO["ACT"],
                    company_id=new_approve_flow_step.company.id,
                    next_level=new_approve_flow_step.level,
                )

                # send mail to ACT
                send_approved_approve_flow_sds(
                    company_to=act,
                    user_approved=self.kanna_user,
                    user_to=list(user_email_to_after_filter),
                    comment=self.comment,
                    sds_type=self.diagnostic_instance.type,
                    cms_title=cms.get("title", "")
                )
        else:
            act = Company.objects.get(type=CompanyType.ACT)

            if self.approve_flow_setting_instance.is_approve_level_act:
                min_level, _ = get_min_max_level_in_company(act)
            else:
                min_level = None

            user_kanna_act = UserKanna.objects.values_list(
                "user_uuid", flat=True
            ).filter(role=KANNA_USER_ROLE["ACT"], is_active=True)

            if min_level:
                user_kanna_act = user_kanna_act.filter(level=min_level)

            user_email_to_after_filter = get_list_user_turn_on_email(
                cms_uuid=self.diagnostic_instance.cms_uuid,
                user_kanna_uuids=user_kanna_act,
            )

            new_approve_flow_step = ApproveFlowStep(
                approve_flow_setting_instance=approve_flow_setting_instance,
                diagnostic=self.diagnostic_instance,
                level=min_level,
                company=approve_flow_setting_instance.act,
            )
            new_approve_flow_step.save()

            # send mail to ACT
            send_approved_approve_flow_sds(
                company_to=act,
                user_approved=self.kanna_user,
                user_to=list(user_email_to_after_filter),
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )
            # send notification to ACT
            send_approve_from_dealer_notifications(
                target_object=self.diagnostic_instance,
                kanna_user=self.kanna_user,
                send_to=NOTIFICATION_SEND_TO["ACT"],
                company_id=new_approve_flow_step.company.id,
                next_level=new_approve_flow_step.level,
            )

        return super().handle_approve()


class RejectFromDealer(State):
    status = ApproveFlowStepStatusChoices.REJECT

    def handle_reject(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        handle_return_reject_sds(
            last_step=last_step,
            kanna_user=self.kanna_user,
            diagnostic_instance=self.diagnostic_instance,
            diagnostic_status=ApproveFlowStepStatusChoices.REJECT,
            step_status=DiagnosticStatus.REJECT,
            comment=self.comment,
        )
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        user_kanna_reject = UserKanna.objects.filter(
            email=self.kanna_user.email
        ).first()

        company_dealer = Company.objects.filter(id=user_kanna_reject.company_id).first()
        user_kanna_without_levle_reject = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(
            Q(role=KANNA_USER_ROLE["DEALER"]),
            Q(company=user_kanna_reject.company),
            ~Q(level=user_kanna_reject.level),
        )

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_without_levle_reject,
        )

        # Send mail Shop:
        send_rejected_approve_flow_sds_for_shop(
            user=user_create_sds,
            user_reject=self.kanna_user,
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # Send mail Dealer:
        send_rejected_approve_flow_sds_for_dealer(
            company_dealer=company_dealer,
            user_reject=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # reject request approve MS from Shop
        # send notification to dealer and shop
        send_reject_from_dealer_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )

        return super().handle_reject()


class ReturnfromDealer(State):
    status = ApproveFlowStepStatusChoices.RETURN

    def handle_return(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        handle_return_reject_sds(
            last_step=last_step,
            kanna_user=self.kanna_user,
            diagnostic_instance=self.diagnostic_instance,
            diagnostic_status=ApproveFlowStepStatusChoices.RETURN,
            step_status=DiagnosticStatus.RETURN,
            comment=self.comment,
        )
        UserElectronicMark.objects.filter(diagnostic=self.diagnostic_instance).delete()
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        is_can_sent_shop = True

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        user_kanna_return = UserKanna.objects.filter(
            email=self.kanna_user.email
        ).first()

        company_dealer = Company.objects.filter(id=user_kanna_return.company_id).first()
        user_kanna_without_level_reject = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(
            Q(role=KANNA_USER_ROLE["DEALER"]),
            Q(company=user_kanna_return.company),
            ~Q(level=user_kanna_return.level),
        )
        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_without_level_reject,
        )

        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_sds_for_shop(
                user=user_create_sds,
                user_return=self.kanna_user,
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_returned_approve_flow_sds_for_dealer(
            company_dealer=company_dealer,
            user_return=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        send_return_from_dealer_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()


# ACT
class ApproveFromACT(State):
    def handle_approve(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        # Check last_step and change stt last_step to APPROVED
        if last_step:
            if last_step.status in (
                ApproveFlowStepStatusChoices.APPROVED,
                ApproveFlowStepStatusChoices.REJECT,
            ):
                raise PermissionDenied({"detail": "Invalid action"})

            user_create_sds = UserKanna.objects.filter(
                email=self.diagnostic_instance.person
            ).first()

            company_dealer = ShopDealer.objects.filter(
                shop=user_create_sds.company
            ).first()

            is_can_sent_shop = True
            if (
                user_create_sds
                and not NotificationEmailSetting.objects.filter(
                    user_uuid=user_create_sds.user_uuid,
                    cms_uuid=self.diagnostic_instance.cms_uuid,
                    is_receive_email=True,
                ).exists()
            ):
                is_can_sent_shop = False

            if company_dealer is None:
                raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

            last_step.status = ApproveFlowStepStatusChoices.APPROVED
            last_step.user = self.kanna_user.user
            last_step.comment = self.comment
            last_step.save()
        else:
            raise PermissionDenied({"detail": "Invalid action"})

        next_level = get_next_level(self.kanna_user)
        approve_flow_setting_instance = (
            self.diagnostic_instance.diagnostic_approve_flow_setting_instance
        )
        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        if self.approve_flow_setting_instance.is_approve_level_act:
            logger.info(f"ACT {self.kanna_user} approved, next level: {next_level}")
            # 2025-02-03 customer order Level2 Skip
            if next_level and (
                    next_level == LEVEL_MEMBERS["LEVEL_3"] or (
                    next_level != LEVEL_MEMBERS["LEVEL_3"] and is_exist_lv3_in_list_lv_in_company(
                self.kanna_user.company))):
                next_level = LEVEL_MEMBERS["LEVEL_3"]
                # if approve_flow_setting_instance.has
                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    diagnostic=self.diagnostic_instance,
                    user=None,
                    level=next_level,
                    company=approve_flow_setting_instance.act,
                )
                new_approve_flow_step.save()

                # Send email to ACT next level using service
                logger.info(f"Request send email to new level: {next_level}")
                ApproveFlowEmailService.send_email_to_act_next_level_sds(
                    kanna_user=self.kanna_user,
                    diagnostic_instance=self.diagnostic_instance,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    next_level=next_level,
                    comment=self.comment,
                    cms_title=cms.get("title", "")
                )

                # if next level is exist then send noti to ACT next level
                # if dealer next level exist then send noti to dealer
                if approve_flow_setting_instance.dealer is not None:
                    send_approve_from_act_notifications(
                        send_to=NOTIFICATION_SEND_TO["DEALER"],
                        company_id=new_approve_flow_step.company.id,
                        next_level=next_level,
                        target_object=self.diagnostic_instance,
                        kanna_user=self.kanna_user,
                    )
            else:
                self.diagnostic_instance.status = DiagnosticStatus.APPROVED
                self.diagnostic_instance.save()

                user_kanna_dealer = UserKanna.objects.values_list(
                    "user_uuid", flat=True
                ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

                user_email_to_after_filter = get_list_user_turn_on_email(
                    cms_uuid=self.diagnostic_instance.cms_uuid,
                    user_kanna_uuids=user_kanna_dealer,
                )

                company_dealer_send_mail = Company.objects.filter(
                    id=company_dealer.dealer_id
                ).first()

                # Send mail Shop
                if is_can_sent_shop:
                    send_approved_approve_flow_sds(
                        company_to=user_create_sds,
                        user_approved=self.kanna_user,
                        user_to=[user_create_sds.email],
                        comment=self.comment,
                        sds_type=self.diagnostic_instance.type,
                        cms_title=cms.get("title", "")
                    )

                # Send mail Dealer
                logger.info(f"Send email to dealer: {list(user_email_to_after_filter)}")
                send_approved_approve_flow_sds(
                    company_to=company_dealer_send_mail,
                    user_approved=self.kanna_user,
                    user_to=list(user_email_to_after_filter),
                    comment=self.comment,
                    sds_type=self.diagnostic_instance.type,
                    cms_title=cms.get("title", "")
                )

                # gen diagnostic PDF
                render_to_pdf_and_send_to_kanna_diagnostic(
                    diagnostic=self.diagnostic_instance,
                    kanna_user=self.kanna_user,
                    cms_uuid=self.diagnostic_instance.cms_uuid,
                )
                # if next level is none (lasted level) then send noti to dealer and shop
                send_approve_from_act_notifications(
                    send_to=NOTIFICATION_SEND_TO["DEALER_SHOP"],
                    approve_flow_setting_instance=self.approve_flow_setting_instance,
                    target_object=self.diagnostic_instance,
                    kanna_user=self.kanna_user,
                )
        else:
            self.diagnostic_instance.status = DiagnosticStatus.APPROVED
            self.diagnostic_instance.save()

            user_kanna_dealer = UserKanna.objects.values_list(
                "user_uuid", flat=True
            ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

            user_email_to_after_filter = get_list_user_turn_on_email(
                cms_uuid=self.diagnostic_instance.cms_uuid,
                user_kanna_uuids=user_kanna_dealer,
            )

            company_dealer_send_mail = Company.objects.filter(
                id=company_dealer.dealer_id
            ).first()

            # Send mail Shop
            if is_can_sent_shop:
                send_approved_approve_flow_sds(
                    company_to=user_create_sds,
                    user_approved=self.kanna_user,
                    user_to=[user_create_sds.email],
                    comment=self.comment,
                    sds_type=self.diagnostic_instance.type,
                    cms_title=cms.get("title", "")
                )
            # Send mail Dealer
            logger.info(f"Send email to dealer: {list(user_email_to_after_filter)}")
            send_approved_approve_flow_sds(
                company_to=company_dealer_send_mail,
                user_approved=self.kanna_user,
                user_to=list(user_email_to_after_filter),
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

            # if not approve level then send noti to dealer and shop
            send_approve_from_act_notifications(
                send_to=NOTIFICATION_SEND_TO["DEALER_SHOP"],
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                target_object=self.diagnostic_instance,
                kanna_user=self.kanna_user,
            )
            # gen diagnostic PDF
            render_to_pdf_and_send_to_kanna_diagnostic(
                diagnostic=self.diagnostic_instance,
                kanna_user=self.kanna_user,
                cms_uuid=self.diagnostic_instance.cms_uuid,
            )

        return super().handle_approve()


class RejectFromACT(State):
    def handle_reject(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        company_dealer_shop = ShopDealer.objects.filter(
            shop=user_create_sds.company
        ).first()
        is_can_sent_shop = True
        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        if company_dealer_shop is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

        handle_return_reject_sds(
            last_step=last_step,
            kanna_user=self.kanna_user,
            diagnostic_instance=self.diagnostic_instance,
            diagnostic_status=ApproveFlowStepStatusChoices.REJECT,
            step_status=DiagnosticStatus.REJECT,
            comment=self.comment,
        )

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer_shop.dealer)

        company_dealer = Company.objects.filter(
            id=company_dealer_shop.dealer_id
        ).first()

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_rejected_approve_flow_sds_for_shop(
                user=user_create_sds,
                user_reject=self.kanna_user,
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )
        if self.approve_flow_setting_instance.dealer:
            # Send mail Dealer
            send_rejected_approve_flow_sds_for_dealer(
                company_dealer=company_dealer,
                user_reject=self.kanna_user,
                user_dealer=list(user_email_to_after_filter),
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Reject request approve SDS from ACT
        # send notification to dealer and shop
        send_reject_from_act_notifications(
            kanna_user=self.kanna_user,
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
        )

        return super().handle_reject()


class ReturnFromACT(State):
    def handle_return(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step(
                self.kanna_user, self.diagnostic_instance
            )
        else:
            last_step = get_current_approve_flow_step_without_level(
                self.kanna_user, self.diagnostic_instance
            )

        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()
        company_dealer_shop = ShopDealer.objects.filter(
            shop=user_create_sds.company
        ).first()

        is_can_sent_shop = True
        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        if company_dealer_shop is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

        handle_return_reject_sds(
            last_step=last_step,
            kanna_user=self.kanna_user,
            diagnostic_instance=self.diagnostic_instance,
            diagnostic_status=ApproveFlowStepStatusChoices.RETURN,
            step_status=DiagnosticStatus.RETURN,
            comment=self.comment,
        )
        UserElectronicMark.objects.filter(diagnostic=self.diagnostic_instance).delete()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer_shop.dealer)
        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        company_dealer = Company.objects.filter(
            id=company_dealer_shop.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_sds_for_shop(
                user=user_create_sds,
                user_return=self.kanna_user,
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )
        if self.approve_flow_setting_instance.dealer:
            # Send mail Dealer
            send_returned_approve_flow_sds_for_dealer(
                company_dealer=company_dealer,
                user_return=self.kanna_user,
                user_dealer=list(user_email_to_after_filter),
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Return request approve SDS from ACT
        # send notification to dealer and shop
        send_return_from_act_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()


class UpdateAfterApproveFromACT(State):
    def handle_update_after_approve(self, construction_method_id):
        last_step = self._get_last_step()
        management_sheet = self.diagnostic_instance.management_sheet
        approve_flow_setting_instance = (
            self.diagnostic_instance.diagnostic_approve_flow_setting_instance
        )
        next_level = get_next_level(self.kanna_user)
        condition_allow_update_after_approve = (
            last_step
            and last_step.status == ApproveFlowStepStatusChoices.APPROVED
            and approve_flow_setting_instance
            and management_sheet.status == ManagementSheetStatus.TEMP
            and next_level is None
        )

        if condition_allow_update_after_approve:
            self.diagnostic_instance.status = DiagnosticStatus.UPDATE_AFTER_APPROVE
            self.diagnostic_instance.construction_method_id = construction_method_id
            self.diagnostic_instance.save()
            render_to_pdf_and_send_to_kanna_diagnostic()

        # send notifications
        send_update_after_approve_from_act_notifications(
            target_object=self.diagnostic_instance,
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            kanna_user=self.kanna_user,
        )

        raise PermissionDenied({"detail": "Invalid action"})


# ADMIN
class ApproveFromAdmin(State):
    def handle_approve(self):
        last_step = query_current_approve_flow_step_from_admin(
            self.diagnostic_instance
        )

        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        company_dealer = ShopDealer.objects.filter(
            shop=user_create_sds.company
        ).first()

        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})
        
        is_can_sent_shop = True
        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.APPROVED
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()

        # update diagnostic
        self.diagnostic_instance.status = DiagnosticStatus.APPROVED
        self.diagnostic_instance.save()
        
        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()
        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_approved_approve_flow_sds(
                company_to=user_create_sds,
                user_approved=self.kanna_user,
                user_to=[user_create_sds.email],
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_approved_approve_flow_sds(
            company_to=company_dealer_send_mail,
            user_approved=self.kanna_user,
            user_to=list(user_email_to_after_filter),
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # gen diagnostic PDF
        render_to_pdf_and_send_to_kanna_diagnostic(
            diagnostic=self.diagnostic_instance,
            kanna_user=self.kanna_user,
            cms_uuid=self.diagnostic_instance.cms_uuid,
        )
        # send noti to dealer and shop
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["APPROVE_REQUEST"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )
        return super().handle_approve()


class RejectFromAdmin(State):
    def handle_reject(self):
        last_step = query_current_approve_flow_step_from_admin(
            self.diagnostic_instance
        )
        
        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        company_dealer = ShopDealer.objects.filter(
            shop=user_create_sds.company
        ).first()
        
        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})    
          
        is_can_sent_shop = True
        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.REJECT
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()
        
        # update diagnostic
        self.diagnostic_instance.status = DiagnosticStatus.REJECT
        self.diagnostic_instance.save()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_rejected_approve_flow_sds_for_shop(
                user=user_create_sds,
                user_reject=self.kanna_user,
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_rejected_approve_flow_sds_for_dealer(
            company_dealer=company_dealer_send_mail,
            user_reject=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # send noti reject to dealer and shop
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["REJECT_REQUEST"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )
        return super().handle_reject()
    

class ReturnFromAdmin(State):
    def handle_return(self):
        last_step = query_current_approve_flow_step_from_admin(
            self.diagnostic_instance
        )
        
        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,    
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_create_sds = UserKanna.objects.filter(
            email=self.diagnostic_instance.person
        ).first()

        company_dealer = ShopDealer.objects.filter(
            shop=user_create_sds.company
        ).first()
        
        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})    
        
        is_can_sent_shop = True
        if (
            user_create_sds
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_create_sds.user_uuid,
                cms_uuid=self.diagnostic_instance.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.RETURN
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()
        
        # update diagnostic
        self.diagnostic_instance.status = DiagnosticStatus.RETURN
        self.diagnostic_instance.save()
        
        UserElectronicMark.objects.filter(diagnostic=self.diagnostic_instance).delete()
        
        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.diagnostic_instance.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.diagnostic_instance.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_sds_for_shop(
                user=user_create_sds,
                user_return=self.kanna_user,
                comment=self.comment,
                sds_type=self.diagnostic_instance.type,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_returned_approve_flow_sds_for_dealer(
            company_dealer=company_dealer_send_mail,
            user_return=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            sds_type=self.diagnostic_instance.type,
            cms_title=cms.get("title", "")
        )

        # send noti reject to dealer and shop
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["RETURN_REQUEST"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.diagnostic_instance,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()
############################
# Management Sheet
############################
# SHOP
class SubmitFromShopMS(StateMS):
    status = ApproveFlowStepStatusChoices.OPEN
    allowed_status = ()

    def handle_approve(self):
        if self.management_sheet.status == ManagementSheetStatus.WAITING_APPROVAL:
            raise ValidationError(
                {"detail": "management_sheet still in WAITING_APPROVAL"}
            )

        if self.management_sheet.status == ManagementSheetStatus.REJECT:
            raise ValidationError({"detail": "management_sheet was rejected"})

        # Create Approve Flow Step for Shop and assign comment
        shop = self.approve_flow_setting_instance.shop
        kanna_user = self.kanna_user
        shop_step = ApproveFlowStep(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            management_sheet=self.management_sheet,
            user=kanna_user.user,
            level=kanna_user.level,
            company=shop,
            comment=self.comment,
            status=ApproveFlowStepStatusChoices.APPROVED,
        )
        shop_step.save()

        # Get min level and next company
        notification_send_to = ""
        min_level = None
        if dealer := self.approve_flow_setting_instance.dealer:
            if self.approve_flow_setting_instance.is_approve_level_dealer:
                min_level, _ = get_min_max_level_in_company(dealer)
            else:
                min_level = None
            company = dealer
            notification_send_to = NOTIFICATION_SEND_TO["DEALER"]
        else:
            act = self.approve_flow_setting_instance.act
            company = act
            if self.approve_flow_setting_instance.is_approve_level_act:
                min_level, _ = get_min_max_level_in_company(act)
            else:
                min_level = None
            notification_send_to = NOTIFICATION_SEND_TO["ACT"]

        # Create Approve Flow Step for next company
        new_approve_flow_step = ApproveFlowStep(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            management_sheet=self.management_sheet,
            user=None,
            level=min_level,
            company=company,
        )
        new_approve_flow_step.save()

        # Change status of diagnostic
        self.management_sheet.status = ManagementSheetStatus.WAITING_APPROVAL
        self.management_sheet.save()

        # Store in Approve List
        ApproveList.objects.get_or_create(
            management_sheet=self.management_sheet, document_type="MS"
        )

        # send mail request approve
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()
        user_email_to = UserKanna.objects.values_list("user_uuid", flat=True).filter(
            company=company, is_active=True
        )
        if min_level:
            user_email_to = user_email_to.filter(level=min_level)

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        user_email_to = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_email_to,
        )
        request_approved_approve_flow_ms(
            company_name=company.name,
            user_dealer=user_email_to,
            user_request=user_pic_ms,
            comment=self.comment,
            cms_title=cms.get("title", "")
        )

        # send notification to all users of shop is dealer
        send_submit_from_shop_notifications(
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            send_to=notification_send_to,
            next_level=min_level,
        )

        return super().handle_submit()


# DEALER
class ApproveFromDealerMS(StateMS):
    status = ApproveFlowStepStatusChoices.APPROVED
    allowed_status = ApproveFlowStepStatusChoices.OPEN

    def handle_approve(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        # Check last_step and change stt last_step to APPROVED
        if last_step:
            if last_step.status in (
                ApproveFlowStepStatusChoices.APPROVED,
                ApproveFlowStepStatusChoices.REJECT,
            ):
                raise PermissionDenied({"detail": "Invalid action"})

            last_step.status = ApproveFlowStepStatusChoices.APPROVED
            last_step.user = self.kanna_user.user
            last_step.comment = self.comment
            last_step.save()
        else:
            raise PermissionDenied({"detail": "Invalid action"})

        # @TODO: Refactor this
        # Create new step

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        approve_flow_setting_instance = (
            self.management_sheet.management_sheet_approve_flow_setting_instance
        )
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            next_level = get_next_level(self.kanna_user)
            if next_level:
                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    management_sheet=self.management_sheet,
                    user=None,
                    level=next_level,
                    company=approve_flow_setting_instance.dealer,
                )
                new_approve_flow_step.save()

                # if dealer next level exist then send noti to dealer
                if approve_flow_setting_instance.dealer is not None:
                    send_approve_from_dealer_notifications(
                        target_object=self.management_sheet,
                        kanna_user=self.kanna_user,
                        send_to=NOTIFICATION_SEND_TO["DEALER"],
                        company_id=new_approve_flow_step.company.id,
                        next_level=next_level,
                    )
            else:
                act = Company.objects.get(type=CompanyType.ACT)

                if self.approve_flow_setting_instance.is_approve_level_act:
                    min_level, _ = get_min_max_level_in_company(act)
                else:
                    min_level = None

                user_kanna_act = UserKanna.objects.values_list(
                    "user_uuid", flat=True
                ).filter(role=KANNA_USER_ROLE["ACT"], is_active=True)

                if min_level:
                    user_kanna_act = user_kanna_act.filter(level=min_level)

                user_kanna_act = get_list_user_turn_on_email(
                    cms_uuid=self.management_sheet.cms_uuid,
                    user_kanna_uuids=user_kanna_act,
                )

                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    management_sheet=self.management_sheet,
                    level=min_level,
                    company=approve_flow_setting_instance.act,
                )
                new_approve_flow_step.save()
                # if next level is none (lasted level) then send noti to ACT
                send_approve_from_dealer_notifications(
                    target_object=self.management_sheet,
                    kanna_user=self.kanna_user,
                    send_to=NOTIFICATION_SEND_TO["ACT"],
                    company_id=new_approve_flow_step.company.id,
                    next_level=new_approve_flow_step.level,
                )

                # Send mail ACT
                send_approved_approve_flow_ms(
                    company_to=act,
                    user_approved=self.kanna_user,
                    user_to=list(user_kanna_act),
                    comment=self.comment,
                    cms_title=cms.get("title", "")
                )
        else:
            act = Company.objects.get(type=CompanyType.ACT)

            if self.approve_flow_setting_instance.is_approve_level_act:
                min_level, _ = get_min_max_level_in_company(act)
            else:
                min_level = None

            user_kanna_act = UserKanna.objects.values_list(
                "user_uuid", flat=True
            ).filter(role=KANNA_USER_ROLE["ACT"], is_active=True)

            if min_level:
                user_kanna_act = user_kanna_act.filter(level=min_level)

            user_kanna_act = get_list_user_turn_on_email(
                cms_uuid=self.management_sheet.cms_uuid,
                user_kanna_uuids=user_kanna_act,
            )

            new_approve_flow_step = ApproveFlowStep(
                approve_flow_setting_instance=approve_flow_setting_instance,
                management_sheet=self.management_sheet,
                level=min_level,
                company=approve_flow_setting_instance.act,
            )
            new_approve_flow_step.save()

            # send notification to ACT
            send_approve_from_dealer_notifications(
                target_object=self.management_sheet,
                kanna_user=self.kanna_user,
                send_to=NOTIFICATION_SEND_TO["ACT"],
                company_id=new_approve_flow_step.company.id,
                next_level=new_approve_flow_step.level,
            )

            # Send mail ACT
            send_approved_approve_flow_ms(
                company_to=act,
                user_approved=self.kanna_user,
                user_to=list(user_kanna_act),
                comment=self.comment,
                cms_title=cms.get("title", "")
            )
        return super().handle_approve()


class RejectFromDealerMS(StateMS):
    status = ApproveFlowStepStatusChoices.REJECT

    def handle_reject(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        handle_return_reject_ms(
            last_step=last_step,
            kanna_user=self.kanna_user,
            management_sheet=self.management_sheet,
            management_sheet_status=ApproveFlowStepStatusChoices.REJECT,
            step_status=ManagementSheetStatus.REJECT,
            comment=self.comment,
        )
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()

        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = True

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Dealer
        user_kanna_reject = UserKanna.objects.filter(
            email=self.kanna_user.email
        ).first()

        company_dealer = Company.objects.filter(id=user_kanna_reject.company_id).first()
        user_kanna_without_levle_reject = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(
            Q(role=KANNA_USER_ROLE["DEALER"]),
            Q(company=user_kanna_reject.company),
            ~Q(level=user_kanna_reject.level),
        )

        user_kanna_without_levle_reject = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_kanna_without_levle_reject,
        )

        # Send mail Shop
        if is_can_sent_shop:
            send_rejected_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_reject=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        send_rejected_approve_flow_ms_for_dealer(
            company_dealer=company_dealer,
            user_reject=self.kanna_user,
            user_dealer=list(user_kanna_without_levle_reject),
            comment=self.comment,
            cms_title=cms.get("title", "")
        )

        # Reject request approve MS from Shop
        # send notification to dealer and shop
        send_reject_from_dealer_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )
        return super().handle_reject()


class ReturnfromDealerMS(StateMS):
    status = ApproveFlowStepStatusChoices.RETURN

    def handle_return(self):
        if self.approve_flow_setting_instance.is_approve_level_dealer:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        handle_return_reject_ms(
            last_step=last_step,
            kanna_user=self.kanna_user,
            management_sheet=self.management_sheet,
            management_sheet_status=ApproveFlowStepStatusChoices.RETURN,
            step_status=ManagementSheetStatus.RETURN,
            comment=self.comment,
        )
        UserElectronicMark.objects.filter(
            management_sheet=self.management_sheet
        ).delete()
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()

        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = True

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)
        # Send mail Dealer
        user_kanna_return = UserKanna.objects.filter(
            email=self.kanna_user.email
        ).first()

        company_dealer = Company.objects.filter(id=user_kanna_return.company_id).first()
        user_kanna_without_levle_reject = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(
            Q(role=KANNA_USER_ROLE["DEALER"]),
            Q(company=user_kanna_return.company),
            ~Q(level=user_kanna_return.level),
        )

        user_kanna_without_levle_reject = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_kanna_without_levle_reject,
        )

        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_return=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )
        send_returned_approve_flow_ms_for_dealer(
            company_dealer=company_dealer,
            user_return=self.kanna_user,
            user_dealer=list(user_kanna_without_levle_reject),
            comment=self.comment,
            cms_title=cms.get("title", "")
        )

        # Return request approve MS from Shop
        # send notification to dealer and shop
        send_return_from_dealer_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()


# ACT
class ApproveFromACTMS(StateMS):
    def handle_approve(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        # Check last_step and change stt last_step to APPROVED
        if last_step:
            if last_step.status in (
                ApproveFlowStepStatusChoices.APPROVED,
                ApproveFlowStepStatusChoices.REJECT,
            ):
                raise ValidationError({"detail": "Invalid action"})

            last_step.status = ApproveFlowStepStatusChoices.APPROVED
            last_step.user = self.kanna_user.user
            last_step.comment = self.comment
            last_step.save()

        else:
            raise PermissionDenied({"detail": "Invalid action"})

        next_level = get_next_level(self.kanna_user)
        approve_flow_setting_instance = (
            self.management_sheet.management_sheet_approve_flow_setting_instance
        )

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        if self.approve_flow_setting_instance.is_approve_level_act:
            logger.info(f"ACT {self.kanna_user} approved, next level: {next_level}")
            # 2025-02-03 customer order Level2 Skip
            if next_level and (
                    next_level == LEVEL_MEMBERS["LEVEL_3"] or (
                    next_level != LEVEL_MEMBERS["LEVEL_3"] and is_exist_lv3_in_list_lv_in_company(
                self.kanna_user.company))):
                next_level = LEVEL_MEMBERS["LEVEL_3"]
                # if approve_flow_setting_instance.has
                new_approve_flow_step = ApproveFlowStep(
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    management_sheet=self.management_sheet,
                    user=None,
                    level=next_level,
                    company=approve_flow_setting_instance.act,
                )
                new_approve_flow_step.save()

                # Send email to ACT next level using service
                logger.info(f"Request send email to new level: {next_level}")
                ApproveFlowEmailService.send_email_to_act_next_level_ms(
                    kanna_user=self.kanna_user,
                    management_sheet=self.management_sheet,
                    approve_flow_setting_instance=approve_flow_setting_instance,
                    next_level=next_level,
                    comment=self.comment,
                    cms_title=cms.get("title", "")
                )

                # if next level is exist then send noti to ACT next level
                if approve_flow_setting_instance.act is not None:
                    send_approve_from_act_notifications(
                        send_to=NOTIFICATION_SEND_TO["ACT"],
                        company_id=new_approve_flow_step.company.id,
                        next_level=next_level,
                        target_object=self.management_sheet,
                        kanna_user=self.kanna_user,
                    )
            else:
                self.management_sheet.status = ManagementSheetStatus.APPROVED
                self.management_sheet.save()
                user_pic_ms = UserKanna.objects.filter(
                    email=self.management_sheet.pic.email
                ).first()

                if (
                    user_pic_ms
                    and not NotificationEmailSetting.objects.filter(
                        user_uuid=user_pic_ms.user_uuid,
                        cms_uuid=self.management_sheet.cms_uuid,
                        is_receive_email=True,
                    ).exists()
                ):
                    user_pic_ms = UserKanna.objects.none()

                company_dealer = ShopDealer.objects.filter(
                    shop=user_pic_ms.company
                ).first()

                if company_dealer is None:
                    raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})
                user_kanna_dealer = UserKanna.objects.values_list(
                    "user_uuid", flat=True
                ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

                company_dealer_send_mail = Company.objects.filter(
                    id=company_dealer.dealer_id
                ).first()

                # Send mail Shop
                send_approved_approve_flow_ms(
                    company_to=user_pic_ms,
                    user_approved=self.kanna_user,
                    user_to=[user_pic_ms.email],
                    comment=self.comment,
                    cms_title=cms.get("title", "")
                )
                user_kanna_dealer = get_list_user_turn_on_email(
                    cms_uuid=self.management_sheet.cms_uuid,
                    user_kanna_uuids=user_kanna_dealer,
                )
                # Send mail Dealer
                logger.info(f"Send email to dealer: {list(user_kanna_dealer)}")
                send_approved_approve_flow_ms(
                    company_to=company_dealer_send_mail,
                    user_approved=self.kanna_user,
                    user_to=list(user_kanna_dealer),
                    comment=self.comment,
                    cms_title=cms.get("title", "")
                )
                # Generate Warranty, PDF, send notification
                try:
                    create_warranty_instances(
                        management_sheet=self.management_sheet,
                        kanna_user=self.kanna_user,
                    )
                    # gen MS PDF
                    render_to_pdf_and_send_to_kanna_management_sheet(
                        management_sheet=self.management_sheet,
                        kanna_user=self.kanna_user,
                        cms_uuid=self.management_sheet.cms_uuid,
                    )

                    # send noti when release warranty
                    send_approve_from_act_notifications(
                        target_object=self.management_sheet,
                        approve_flow_setting_instance=self.approve_flow_setting_instance,
                        kanna_user=self.kanna_user,
                        send_to=NOTIFICATION_SEND_TO["DEALER_SHOP"],
                        verb=NOTIFICATION_VERBS["RELEASE_WARRANTY"],
                    )
                except Exception as e:
                    raise Exception(str(e))
        else:
            self.management_sheet.status = ManagementSheetStatus.APPROVED
            self.management_sheet.save()
            user_pic_ms = UserKanna.objects.filter(
                email=self.management_sheet.pic.email
            ).first()

            company_dealer = ShopDealer.objects.filter(shop=user_pic_ms.company).first()

            if company_dealer is None:
                raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})
            user_kanna_dealer = UserKanna.objects.values_list(
                "user_uuid", flat=True
            ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

            company_dealer_send_mail = Company.objects.filter(
                id=company_dealer.dealer_id
            ).first()

            if (
                user_pic_ms
                and not NotificationEmailSetting.objects.filter(
                    user_uuid=user_pic_ms.user_uuid,
                    cms_uuid=self.management_sheet.cms_uuid,
                    is_receive_email=True,
                ).exists()
            ):
                user_pic_ms = UserKanna.objects.none()

            # Send mail Shop
            send_approved_approve_flow_ms(
                company_to=user_pic_ms,
                user_approved=self.kanna_user,
                user_to=[user_pic_ms.email],
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

            user_kanna_dealer = get_list_user_turn_on_email(
                cms_uuid=self.management_sheet.cms_uuid,
                user_kanna_uuids=user_kanna_dealer,
            )

            # Send mail Dealer
            logger.info(f"Send email to dealer: {list(user_kanna_dealer)}")
            send_approved_approve_flow_ms(
                company_to=company_dealer_send_mail,
                user_approved=self.kanna_user,
                user_to=list(user_kanna_dealer),
                comment=self.comment,
                cms_title=cms.get("title", "")
            )
            # Generate Warranty
            create_warranty_instances(
                management_sheet=self.management_sheet, kanna_user=self.kanna_user
            )
            # gen MS PDF
            render_to_pdf_and_send_to_kanna_management_sheet(
                management_sheet=self.management_sheet,
                kanna_user=self.kanna_user,
                cms_uuid=self.management_sheet.cms_uuid,
            )
            # send noti when release warranty
            send_approve_from_act_notifications(
                target_object=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                kanna_user=self.kanna_user,
                send_to=NOTIFICATION_SEND_TO["DEALER_SHOP"],
                verb=NOTIFICATION_VERBS["RELEASE_WARRANTY"],
            )

        # send noti when approve
        # ACT approve
        send_approve_from_act_notifications(
            send_to=NOTIFICATION_SEND_TO["DEALER_SHOP"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )

        return super().handle_approve()


class RejectFromACTMS(StateMS):
    def handle_reject(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()

        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = True

        company_dealer_shop = ShopDealer.objects.filter(
            shop=user_pic_ms.company
        ).first()

        if company_dealer_shop is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

        handle_return_reject_ms(
            last_step=last_step,
            kanna_user=self.kanna_user,
            management_sheet=self.management_sheet,
            management_sheet_status=ApproveFlowStepStatusChoices.REJECT,
            step_status=ManagementSheetStatus.REJECT,
            comment=self.comment,
        )

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer_shop.dealer)

        company_dealer = Company.objects.filter(
            id=company_dealer_shop.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_rejected_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_reject=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )
        if self.approve_flow_setting_instance.dealer:
            # Send mail Dealer
            user_kanna_dealer = get_list_user_turn_on_email(
                cms_uuid=self.management_sheet.cms_uuid,
                user_kanna_uuids=user_kanna_dealer,
            )

            send_rejected_approve_flow_ms_for_dealer(
                company_dealer=company_dealer,
                user_reject=self.kanna_user,
                user_dealer=list(user_kanna_dealer),
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        # Reject request approve MS from Shop
        # send notification to dealer and shop
        send_reject_from_act_notifications(
            kanna_user=self.kanna_user,
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
        )

        return super().handle_reject()


class ReturnFromACTMS(StateMS):
    def handle_return(self):
        if self.approve_flow_setting_instance.is_approve_level_act:
            last_step = get_current_apprve_flow_step_ms(
                self.kanna_user, self.management_sheet
            )
        else:
            last_step = get_current_approve_flow_step_without_level_ms(
                self.kanna_user, self.management_sheet
            )

        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()

        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = True

        company_dealer_shop = ShopDealer.objects.filter(
            shop=user_pic_ms.company
        ).first()

        if company_dealer_shop is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

        handle_return_reject_ms(
            last_step=last_step,
            kanna_user=self.kanna_user,
            management_sheet=self.management_sheet,
            management_sheet_status=ApproveFlowStepStatusChoices.RETURN,
            step_status=ManagementSheetStatus.RETURN,
            comment=self.comment,
        )

        UserElectronicMark.objects.filter(
            management_sheet=self.management_sheet
        ).delete()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer_shop.dealer)

        company_dealer = Company.objects.filter(
            id=company_dealer_shop.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_return=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )
        if self.approve_flow_setting_instance.dealer:
            # Send mail Dealer
            user_kanna_dealer = get_list_user_turn_on_email(
                cms_uuid=self.management_sheet.cms_uuid,
                user_kanna_uuids=user_kanna_dealer,
            )
            send_returned_approve_flow_ms_for_dealer(
                company_dealer=company_dealer,
                user_return=self.kanna_user,
                user_dealer=list(user_kanna_dealer),
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        # Return request approve SDS from ACT
        # send notification to dealer and shop
        send_return_from_act_notifications(
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()

# ADMIN
class ApproveFromAdminMS(StateMS):
    def handle_approve(self):
        last_step = query_current_approve_flow_step_from_admin_ms(
            self.management_sheet
        )
        
        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()
        company_dealer = ShopDealer.objects.filter(
            shop=user_pic_ms.company
        ).first()
            
        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})
        
        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False
        
        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.APPROVED
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()

        # update management sheet
        self.management_sheet.status = ManagementSheetStatus.APPROVED
        self.management_sheet.save()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)
        
        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()
        # Send mail Shop
        if is_can_sent_shop:
            send_approved_approve_flow_ms(
                company_to=user_pic_ms,
                user_approved=self.kanna_user,
                user_to=[user_pic_ms.email],
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_approved_approve_flow_ms(
            company_to=company_dealer_send_mail,
            user_approved=self.kanna_user,
            user_to=list(user_email_to_after_filter),
            comment=self.comment,
            cms_title=cms.get("title", "")
        )
        # gen warranty
        create_warranty_instances(
            management_sheet=self.management_sheet,
            kanna_user=self.kanna_user,
        )
        # gen MS PDF
        render_to_pdf_and_send_to_kanna_management_sheet(
            management_sheet=self.management_sheet,
            kanna_user=self.kanna_user,
            cms_uuid=self.management_sheet.cms_uuid,
        )

        # send noti when release warranty
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["RELEASE_WARRANTY"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )
        return super().handle_approve()


class RejectFromAdminMS(StateMS):
    def handle_reject(self):
        last_step = query_current_approve_flow_step_from_admin_ms(
            self.management_sheet
        )
        
        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()
        company_dealer = ShopDealer.objects.filter(
            shop=user_pic_ms.company
        ).first()
            
        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})

        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.REJECT
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()

        # update management sheet
        self.management_sheet.status = ManagementSheetStatus.REJECT
        self.management_sheet.save()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )
        
        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)

         # Send mail Shop
        if is_can_sent_shop:
            send_rejected_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_reject=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_rejected_approve_flow_ms_for_dealer(
            company_dealer=company_dealer_send_mail,
            user_reject=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            cms_title=cms.get("title", "")
        )

        # send noti to dealer and shop
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["REJECT_REQUEST"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )

        return super().handle_reject()
    
    
class ReturnFromAdminMS(StateMS):
    def handle_return(self):
        last_step = query_current_approve_flow_step_from_admin_ms(
            self.management_sheet
        )
        
        # check last_step 
        if not last_step or last_step.status in (
            ApproveFlowStepStatusChoices.APPROVED,
            ApproveFlowStepStatusChoices.REJECT,
        ):
            raise PermissionDenied({"detail": "無効なアクション"})
        
        user_pic_ms = UserKanna.objects.filter(
            email=self.management_sheet.pic.email
        ).first()
        company_dealer = ShopDealer.objects.filter(
            shop=user_pic_ms.company
        ).first()
            
        if company_dealer is None:
            raise ValidationError({"message": "このメイクUpショップの配属ディーラーはありません。"})
        
        is_can_sent_shop = True
        if (
            user_pic_ms
            and not NotificationEmailSetting.objects.filter(
                user_uuid=user_pic_ms.user_uuid,
                cms_uuid=self.management_sheet.cms_uuid,
                is_receive_email=True,
            ).exists()
        ):
            is_can_sent_shop = False

        # update last approve flow step
        last_step.status = ApproveFlowStepStatusChoices.RETURN
        last_step.user = self.kanna_user.user
        last_step.comment = self.comment
        last_step.level = None
        last_step.is_admin_approved = True
        last_step.save()

        # update management sheet
        self.management_sheet.status = ManagementSheetStatus.RETURN
        self.management_sheet.save()

        # remove electronic mark        
        UserElectronicMark.objects.filter(
            management_sheet=self.management_sheet
        ).delete()

        user_kanna_dealer = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=KANNA_USER_ROLE["DEALER"], company=company_dealer.dealer)

        user_email_to_after_filter = get_list_user_turn_on_email(
            cms_uuid=self.management_sheet.cms_uuid,
            user_kanna_uuids=user_kanna_dealer,
        )

        cms = get_cms_kanna(cms_uuid=self.management_sheet.cms_uuid, kanna_user=self.kanna_user)
        
        company_dealer_send_mail = Company.objects.filter(
            id=company_dealer.dealer_id
        ).first()
        # Send mail Shop
        if is_can_sent_shop:
            send_returned_approve_flow_ms_for_shop(
                user=user_pic_ms,
                user_return=self.kanna_user,
                comment=self.comment,
                cms_title=cms.get("title", "")
            )

        # Send mail Dealer
        send_returned_approve_flow_ms_for_dealer(
            company_dealer=company_dealer_send_mail,
            user_return=self.kanna_user,
            user_dealer=list(user_email_to_after_filter),
            comment=self.comment,
            cms_title=cms.get("title", "")
        )

        # send noti to dealer and shop
        send_from_admin_notifications(
            verb=NOTIFICATION_VERBS["RETURN_REQUEST"],
            approve_flow_setting_instance=self.approve_flow_setting_instance,
            target_object=self.management_sheet,
            kanna_user=self.kanna_user,
        )

        return super().handle_return()
