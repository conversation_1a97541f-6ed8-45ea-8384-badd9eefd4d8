from django.contrib import admin

from .models import (
    ApproveFlowSettingInstance,
    ApproveFlowStep,
    OwnerElectronicMark,
    UserElectronicMark,
)


class ApproveFlowStepAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "diagnostic",
        "management_sheet",
        "approve_flow_setting_instance",
        "user",
        "company",
        "level",
        "status",
        "comment",
    )
    autocomplete_fields = ["company"]


class ApproveFlowSettingInstanceAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "diagnostic",
        "management_sheet",
        "type",
        "shop",
        "dealer",
        "act",
        "is_approve_level_dealer",
        "is_approve_level_act",
        "is_signed_by_shop",
        "is_signed_by_dealer",
        "is_signed_by_act",
        "ref_setting",
    )
    autocomplete_fields = ["shop", "dealer", "act"]


class UserElectronicMarkAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "user",
        "diagnostic",
        "management_sheet",
        "image_base64",
    )


class OwnerElectronicMarkAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "diagnostic",
        "management_sheet",
        "image_base64",
    )


admin.site.register(ApproveFlowStep, ApproveFlowStepAdmin)
admin.site.register(ApproveFlowSettingInstance, ApproveFlowSettingInstanceAdmin)
admin.site.register(UserElectronicMark, UserElectronicMarkAdmin)
admin.site.register(OwnerElectronicMark, OwnerElectronicMarkAdmin)
