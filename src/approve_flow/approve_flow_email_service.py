from logging import get<PERSON>ogger
from typing import List

from api.constants import KANNA_USER_ROLE
from kanna_api.models import Role, UserKanna
from notification_settings.services import get_list_user_turn_on_email
from templates.emails.mailer import (
    send_approved_approve_flow_ms,
    send_approved_approve_flow_sds,
)

logger = getLogger(__name__)


class ApproveFlowEmailService:
    """
    Service class for handling email notifications in approve flow
    """

    @staticmethod
    def get_act_users_at_level_with_email_enabled(
        next_level: str,
        cms_uuid: str,
    ) -> List[str]:
        """
        Get list of ACT users at specific level who have email notifications enabled

        Args:
            next_level: Level to filter users
            cms_uuid: CMS UUID for filtering email notifications

        Returns:
            List[str]: List of user UUIDs who have email notifications enabled
        """
        # Get ACT users at next level
        role_act = Role.objects.get(id=KANNA_USER_ROLE["ACT"])
        user_kanna_act_next_level = UserKanna.objects.values_list(
            "user_uuid", flat=True
        ).filter(role=role_act, is_active=True, level=next_level)
        logger.info(
            f"User kanna act next level: {next_level},  user_uuid: {user_kanna_act_next_level}"
        )

        # Filter users who have email notifications enabled
        user_email_to_act_next_level = get_list_user_turn_on_email(
            cms_uuid=cms_uuid,
            user_kanna_uuids=user_kanna_act_next_level,
        )

        return list(user_email_to_act_next_level)

    @staticmethod
    def send_email_to_act_next_level_sds(
        kanna_user: UserKanna,
        diagnostic_instance,
        approve_flow_setting_instance,
        next_level: str,
        comment: str,
        cms_title: str,
    ) -> bool:
        """
        Send email to ACT next level for SDS approval

        Args:
            kanna_user: Current user who approved
            diagnostic_instance: Diagnostic instance
            approve_flow_setting_instance: Approve flow setting instance
            next_level: Next level to send email to
            comment: Approval comment
            cms_title: CMS title

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        logger.info(
            f"Sending email to ACT next level {next_level}, "
            f"From {kanna_user.email}, diagnostic instance {diagnostic_instance.id}, "
            f"approve flow setting: {approve_flow_setting_instance.id} "
            f"comment: {comment}, cms_title: {cms_title}"
        )
        try:
            user_email_to_act_next_level = (
                ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                    next_level=next_level,
                    cms_uuid=diagnostic_instance.cms_uuid,
                )
            )
            logger.info(
                f"SDS APPROVED | Send email to users: {user_email_to_act_next_level}"
            )

            if user_email_to_act_next_level:
                send_approved_approve_flow_sds(
                    company_to=approve_flow_setting_instance.act,
                    user_approved=kanna_user,
                    user_to=user_email_to_act_next_level,
                    comment=comment,
                    sds_type=diagnostic_instance.type,
                    cms_title=cms_title,
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error sending email to ACT next level for SDS: {str(e)}")
            return False

    @staticmethod
    def send_email_to_act_next_level_ms(
        kanna_user: UserKanna,
        management_sheet,
        approve_flow_setting_instance,
        next_level: str,
        comment: str,
        cms_title: str,
    ) -> bool:
        """
        Send email to ACT next level for MS approval

        Args:
            kanna_user: Current user who approved
            management_sheet: Management sheet instance
            approve_flow_setting_instance: Approve flow setting instance
            next_level: Next level to send email to
            comment: Approval comment
            cms_title: CMS title

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        logger.info(
            f"Sending email to ACT next level {next_level}, "
            f"From {kanna_user.email}, management_sheet instance {management_sheet.id}, "
            f"approve flow setting: {approve_flow_setting_instance.id} "
            f"comment: {comment}, cms_title: {cms_title}"
        )
        try:
            user_email_to_act_next_level = (
                ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                    next_level=next_level,
                    cms_uuid=management_sheet.cms_uuid,
                )
            )
            logger.info(
                f"MS APPROVED | Send email to users: {user_email_to_act_next_level}"
            )

            if user_email_to_act_next_level:
                send_approved_approve_flow_ms(
                    company_to=approve_flow_setting_instance.act,
                    user_approved=kanna_user,
                    user_to=user_email_to_act_next_level,
                    comment=comment,
                    cms_title=cms_title,
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error sending email to ACT next level for MS: {str(e)}")
            return False
