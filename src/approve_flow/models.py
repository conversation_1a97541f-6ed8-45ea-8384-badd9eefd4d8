from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from approve_flow_settings.models import (
    ApproveFlowSetting,
    ApproveFlowSettingAbstract,
)
from authentication.models import User
from companies.models import Company
from kanna_api.models.user_kanna import LevelChoices


# Create your models here.


class ApproveFlowStepStatusChoices(models.TextChoices):
    INIT = "INIT", _("Init")
    OPEN = "OPEN", _("Open")
    APPROVED = "APPROVED", _("Approved")
    REJECT = "REJECT", _("Reject")
    RETURN = "RETURN", _("Return")
    UPDATE_AFTER_APPROVE = "UPDATE_AFTER_APPROVE", _("Update after approve")


class UserRoleType(models.TextChoices):
    SHOP = "SHOP"
    DEALER = "DEALER"
    ACT = "ACT"
    ADMIN = "ADMIN"


class ApproveFlowSettingInstance(ApproveFlowSettingAbstract):
    shop = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_setting_instance_shop",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    dealer = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_setting_instance_dealer",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    act = models.ForeignKey(
        Company,
        to_field="id",
        related_name="approve_flow_setting_instance_act",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    ref_setting = models.ForeignKey(
        ApproveFlowSetting,
        related_name="approve_flow_setting_instance",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    diagnostic = models.OneToOneField(
        "diagnostics.Diagnostic",
        related_name="diagnostic_approve_flow_setting_instance",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    management_sheet = models.OneToOneField(
        "management_sheets.ManagementSheet",
        related_name="management_sheet_approve_flow_setting_instance",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "approve_flow_setting_instance"
        ordering = ["-id"]

    def __str__(self) -> str:
        return self.name


class ApproveFlowStep(models.Model):
    approve_flow_setting_instance = models.ForeignKey(
        ApproveFlowSettingInstance,
        related_name="approve_flow_setting_instance_approve_steps",
        on_delete=models.CASCADE,
    )
    diagnostic = models.ForeignKey(
        "diagnostics.Diagnostic",
        related_name="diagnostic_approve_steps",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    management_sheet = models.ForeignKey(
        "management_sheets.ManagementSheet",
        related_name="management_sheet_approve_steps",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    user = models.ForeignKey(
        User,
        related_name="user_approve_steps",
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )
    level = models.CharField(
        max_length=16,
        choices=LevelChoices.choices,
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=32,
        choices=ApproveFlowStepStatusChoices.choices,
        default=ApproveFlowStepStatusChoices.OPEN,
    )
    comment = models.TextField(null=True, blank=True)
    company = models.ForeignKey(
        Company, related_name="company_approve_steps", on_delete=models.DO_NOTHING
    )
    is_admin_approved = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        db_table = "approve_flow_step"
        ordering = ["-id"]

    def save(self, *args, **kwargs):
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    @property
    def document(self):
        if self.diagnostic:
            return self.diagnostic
        elif self.management_sheet:
            return self.management_sheet
        else:
            return None


class UserElectronicMark(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(
        User,
        related_name="user_electronic",
        on_delete=models.DO_NOTHING,
    )
    diagnostic = models.ForeignKey(
        "diagnostics.Diagnostic",
        related_name="diagnostic_user_electronic_mark",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    management_sheet = models.ForeignKey(
        "management_sheets.ManagementSheet",
        related_name="management_sheet_user_electronic_mark",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    user_role_type = models.CharField(
        max_length=16, choices=UserRoleType.choices, default=UserRoleType.SHOP
    )
    image_base64 = models.TextField()

    class Meta:
        db_table = "user_electronic_mark"
        ordering = ["-id"]


class OwnerElectronicMark(models.Model):
    id = models.BigAutoField(primary_key=True)
    diagnostic = models.OneToOneField(
        "diagnostics.Diagnostic",
        related_name="diagnostic_owner_electronic_mark",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    management_sheet = models.OneToOneField(
        "management_sheets.ManagementSheet",
        related_name="management_sheet_owner_electronic_mark",
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )

    image_base64 = models.TextField()

    class Meta:
        db_table = "owner_electronic_mark"
        ordering = ["-id"]
