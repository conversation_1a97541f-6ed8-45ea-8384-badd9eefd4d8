# Generated by Django 4.1 on 2022-09-20 09:11

from django.db import migrations


def delete_approve_flow_setting_instance(apps, schema_editor):
    approve_flow_setting_instance_model = apps.get_model("approve_flow", "ApproveFlowSettingInstance")
    approve_flow_setting_instance_model.objects.all().delete()


def create_approve_flow_setting_instance(apps, schema_editor):
    approve_flow_setting_instance_model = apps.get_model("approve_flow", "ApproveFlowSettingInstance")
    diagnostic_model = apps.get_model("diagnostics", "Diagnostic")
    diagnostics = diagnostic_model.objects.all()
    for diagnostic in diagnostics:
        try:
            if approve_flow_setting := diagnostic.approve_flow_setting:
                approve_flow_setting_instance_model.objects.create(
                    name=approve_flow_setting.name,
                    type=approve_flow_setting.type,
                    shop=approve_flow_setting.shop,
                    dealer=approve_flow_setting.dealer,
                    act=approve_flow_setting.act,
                    is_approve_level_dealer=approve_flow_setting.is_approve_level_dealer,
                    is_approve_level_act=approve_flow_setting.is_approve_level_act,
                    is_signed_by_shop=approve_flow_setting.is_signed_by_shop,
                    is_signed_by_dealer=approve_flow_setting.is_signed_by_dealer,
                    is_signed_by_act=approve_flow_setting.is_signed_by_act,
                    ref_setting=approve_flow_setting,
                    diagnostic=diagnostic
                )
        except Exception as e:
            print(e)
            pass


class Migration(migrations.Migration):
    dependencies = [
        ("approve_flow", "0003_approveflowsettinginstance_and_more"),
    ]

    operations = [
        migrations.RunPython(create_approve_flow_setting_instance, reverse_code=delete_approve_flow_setting_instance),
    ]
