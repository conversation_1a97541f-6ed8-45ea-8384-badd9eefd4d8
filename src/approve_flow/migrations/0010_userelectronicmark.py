# Generated by Django 4.1.2 on 2022-11-03 16:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        (
            "management_sheets",
            "0029_alter_actualconstructionmethod_amount_of_use_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("diagnostics", "0021_alter_diagnostic_diagnosis_date"),
        ("approve_flow", "0009_approveflowstep_created_at_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserElectronicMark",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("image_base64", models.TextField()),
                (
                    "diagnostic",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="diagnostic_user_electronic_mark",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "management_sheet",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="management_sheet_user_electronic_mark",
                        to="management_sheets.managementsheet",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="user_electronic",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "user_electronic_mark",
                "ordering": ["-id"],
            },
        ),
    ]
