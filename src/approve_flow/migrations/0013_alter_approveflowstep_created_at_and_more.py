# Generated by Django 4.1.2 on 2022-12-02 07:38

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("approve_flow", "0012_alter_approveflowstep_status"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="approveflowstep",
            name="created_at",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
