# Generated by Django 4.1 on 2022-10-02 14:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("companies", "0005_company_control_id_company_email_company_fax_and_more"),
        ("approve_flow_settings", "0002_alter_approveflowsetting_dealer"),
        ("diagnostics", "0008_remove_diagnostic_building_completion_month_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ApproveFlowSettingInstance",
            fields=[
                (
                    "approveflowsetting_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="approve_flow_settings.approveflowsetting",
                    ),
                ),
                (
                    "ref_setting_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_setting",
                        to="approve_flow_settings.approveflowsetting",
                    ),
                ),
            ],
            options={
                "db_table": "approve_flow_setting_instance",
                "ordering": ["-id"],
            },
            bases=("approve_flow_settings.approveflowsetting",),
        ),
        migrations.CreateModel(
            name="ApproveFlowStep",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("LEVEL_1", "Level 1"),
                            ("LEVEL_2", "Level 2"),
                            ("LEVEL_3", "Level 3"),
                        ],
                        max_length=16,
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("OPEN", "Open"),
                            ("APPROVED", "Approved"),
                            ("REJECT", "Reject"),
                            ("RETURN", "Return"),
                        ],
                        default="OPEN",
                        max_length=32,
                    ),
                ),
                ("comment", models.TextField(blank=True, null=True)),
                (
                    "approve_flow_setting_instance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_setting_instance",
                        to="approve_flow.approveflowsettinginstance",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="assigned_company",
                        to="companies.company",
                    ),
                ),
                (
                    "diagnostic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="diagnostic",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="assigned_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "approve_flow_step",
                "ordering": ["-id"],
            },
        ),
    ]
