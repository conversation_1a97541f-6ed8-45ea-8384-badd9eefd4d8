# Generated by Django 4.1.3 on 2024-11-14 07:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('approve_flow', '0016_approveflowstep_is_admin_approved'),
    ]

    operations = [
        migrations.RenameField(
            model_name='userelectronicmark',
            old_name='company_type',
            new_name='user_role_type',
        ),
        migrations.AlterField(
            model_name='userelectronicmark',
            name='user_role_type',
            field=models.CharField(choices=[('SHOP', 'Shop'), ('DEALER', 'Dealer'), ('ACT', 'Act'), ('ADMIN', 'Admin')], default='SHOP', max_length=16),
        ),
    ]
