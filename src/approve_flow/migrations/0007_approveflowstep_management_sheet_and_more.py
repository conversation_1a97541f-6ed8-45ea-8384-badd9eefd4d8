# Generated by Django 4.1 on 2022-10-20 06:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("diagnostics", "0018_alter_diagnosticconstructionmethodestimation_options"),
        ("management_sheets", "0016_managementsheet_completed_date_and_more"),
        ("approve_flow", "0006_approveflowsettinginstance_management_sheet"),
    ]

    operations = [
        migrations.AddField(
            model_name="approveflowstep",
            name="management_sheet",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="management_sheet_approve_steps",
                to="management_sheets.managementsheet",
            ),
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="diagnostic",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="diagnostic_approve_steps",
                to="diagnostics.diagnostic",
            ),
        ),
    ]
