# Generated by Django 4.1.2 on 2022-12-27 00:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("management_sheets", "0038_alter_actualconstruction_hexcolor"),
        (
            "diagnostics",
            "0024_alter_diagnosticconstructionmethodestimation_construction_method_type",
        ),
        ("approve_flow", "0013_alter_approveflowstep_created_at_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="OwnerElectronicMark",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name_owner", models.Char<PERSON>ield(max_length=255)),
                ("image_base64", models.TextField()),
                (
                    "diagnostic",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="diagnostic_owner_electronic_mark",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "management_sheet",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="management_sheet_owner_electronic_mark",
                        to="management_sheets.managementsheet",
                    ),
                ),
            ],
            options={
                "db_table": "owner_electronic_mark",
                "ordering": ["-id"],
            },
        ),
    ]
