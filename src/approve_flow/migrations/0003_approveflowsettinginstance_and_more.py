# Generated by Django 4.1 on 2022-10-11 14:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("approve_flow_settings", "0003_approveflowsetting_deleted_at_and_more"),
        ("companies", "0006_remove_company_control_id_company_shop_no"),
        ("diagnostics", "0012_alter_diagnostic_approve_flow_setting_and_more"),
        (
            "approve_flow",
            "0002_alter_approveflowstep_approve_flow_setting_instance_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="ApproveFlowSettingInstance",
            fields=[
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("SDS_ROOF", "Sds Roof"),
                            ("SDS_OUTER_WALL", "Sds Outer Wall"),
                            ("MS", "Ms"),
                        ],
                        max_length=125,
                    ),
                ),
                ("is_approve_level_dealer", models.BooleanField(default=True)),
                ("is_approve_level_act", models.BooleanField(default=True)),
                ("is_signed_by_shop", models.BooleanField(default=True)),
                ("is_signed_by_dealer", models.BooleanField(default=True)),
                ("is_signed_by_act", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "act",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_setting_instance_act",
                        to="companies.company",
                    ),
                ),
                (
                    "dealer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_setting_instance_dealer",
                        to="companies.company",
                    ),
                ),
                (
                    "diagnostic",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="diagnostic_approve_flow_setting_instance",
                        to="diagnostics.diagnostic",
                    ),
                ),
                (
                    "ref_setting",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approve_flow_setting",
                        to="approve_flow_settings.approveflowsetting",
                    ),
                ),
                (
                    "shop",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="approve_flow_setting_instance_shop",
                        to="companies.company",
                    ),
                ),
            ],
            options={
                "db_table": "approve_flow_setting_instance",
                "ordering": ["-id"],
            },
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="approve_flow_setting_instance",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="approve_flow_setting_instance_approve_steps",
                to="approve_flow.approveflowsettinginstance",
            ),
        ),
    ]
