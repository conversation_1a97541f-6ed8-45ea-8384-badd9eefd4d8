from django.db import migrations


def delete_approve_flow_setting_instance(apps, schema_editor):
    pass


def create_approve_flow_setting_instance(apps, schema_editor):
    approve_flow_setting_instance_model = apps.get_model("approve_flow", "ApproveFlowSettingInstance")
    management_sheet_model = apps.get_model("management_sheets", "ManagementSheet")
    management_sheets = management_sheet_model.objects.all()
    for management_sheet in management_sheets:
        try:
            if approve_flow_setting := management_sheet.approve_flow_setting and True:
                approve_flow_setting_instance_model.objects.get_or_create(
                    name=approve_flow_setting.name,
                    type=approve_flow_setting.type,
                    shop=approve_flow_setting.shop,
                    dealer=approve_flow_setting.dealer,
                    act=approve_flow_setting.act,
                    is_approve_level_dealer=approve_flow_setting.is_approve_level_dealer,
                    is_approve_level_act=approve_flow_setting.is_approve_level_act,
                    is_signed_by_shop=approve_flow_setting.is_signed_by_shop,
                    is_signed_by_dealer=approve_flow_setting.is_signed_by_dealer,
                    is_signed_by_act=approve_flow_setting.is_signed_by_act,
                    ref_setting=approve_flow_setting,
                    management_sheet=management_sheet
                )
        except Exception as e:
            print(e)
            pass


class Migration(migrations.Migration):
    dependencies = [
        ("approve_flow", "0007_approveflowstep_management_sheet_and_more"),
    ]

    operations = [
        migrations.RunPython(create_approve_flow_setting_instance, reverse_code=delete_approve_flow_setting_instance),
    ]
