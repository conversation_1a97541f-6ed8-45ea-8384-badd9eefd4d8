# Generated by Django 4.1 on 2022-10-11 14:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("diagnostics", "0012_alter_diagnostic_approve_flow_setting_and_more"),
        ("companies", "0006_remove_company_control_id_company_shop_no"),
        ("approve_flow_settings", "0003_approveflowsetting_deleted_at_and_more"),
        ("approve_flow", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="approveflowstep",
            name="approve_flow_setting_instance",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="approve_flow_setting_instance_approve_steps",
                to="approve_flow_settings.approveflowsetting",
            ),
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="company_approve_steps",
                to="companies.company",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="approveflowstep",
            name="diagnostic",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="diagnostic_approve_steps",
                to="diagnostics.diagnostic",
            ),
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="status",
            field=models.CharField(
                choices=[
                    ("INIT", "Init"),
                    ("OPEN", "Open"),
                    ("APPROVED", "Approved"),
                    ("REJECT", "Reject"),
                    ("RETURN", "Return"),
                ],
                default="OPEN",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="approveflowstep",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="user_approve_steps",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.DeleteModel(
            name="ApproveFlowSettingInstance",
        ),
    ]
