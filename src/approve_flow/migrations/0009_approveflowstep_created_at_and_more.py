# Generated by Django 4.1.2 on 2022-11-01 14:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        (
            "approve_flow",
            "0008_migrate_data_for_approve_setting_instance_management_sheet",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="approveflowstep",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="approveflowstep",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
