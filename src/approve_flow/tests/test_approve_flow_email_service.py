from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model

from api.constants import KANNA_USER_ROLE
from kanna_api.models import Role, UserKanna
from approve_flow.approve_flow_email_service import ApproveFlowEmailService
from notification_settings.services import get_list_user_turn_on_email


class TestApproveFlowEmailService(TestCase):
    """Test cases for ApproveFlowEmailService"""

    def setUp(self):
        """Set up test data"""
        # Create test roles
        self.role_act = Role.objects.create(id=KANNA_USER_ROLE["ACT"])
        
        # Create test users
        self.user1 = UserKanna.objects.create(
            user_uuid="user-uuid-1",
            role=self.role_act,
            is_active=True,
            level="level1"
        )
        self.user2 = UserKanna.objects.create(
            user_uuid="user-uuid-2", 
            role=self.role_act,
            is_active=True,
            level="level1"
        )
        self.user3 = UserKanna.objects.create(
            user_uuid="user-uuid-3",
            role=self.role_act,
            is_active=True,
            level="level2"
        )
        
        # Create test instances
        self.kanna_user = UserKanna.objects.create(
            user_uuid="approver-uuid",
            role=self.role_act,
            is_active=True,
            level="level0"
        )
        
        # Mock diagnostic instance
        self.diagnostic_instance = MagicMock()
        self.diagnostic_instance.cms_uuid = "test-cms-uuid"
        self.diagnostic_instance.type = "test-type"
        
        # Mock management sheet instance
        self.management_sheet = MagicMock()
        self.management_sheet.cms_uuid = "test-cms-uuid"
        
        # Mock approve flow setting instance
        self.approve_flow_setting_instance = MagicMock()
        self.approve_flow_setting_instance.act = "test-act"

    def test_get_act_users_at_level_with_email_enabled_success(self):
        """Test successful retrieval of ACT users with email enabled"""
        # Mock the get_list_user_turn_on_email function
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.return_value = ["user-uuid-1", "user-uuid-2"]
            
            result = ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                next_level="level1",
                cms_uuid="test-cms-uuid"
            )
            
            # Verify the result
            self.assertEqual(result, ["user-uuid-1", "user-uuid-2"])
            
            # Verify the function was called with correct parameters
            mock_get_users.assert_called_once_with(
                cms_uuid="test-cms-uuid",
                user_kanna_uuids=["user-uuid-1", "user-uuid-2"]
            )

    def test_get_act_users_at_level_with_email_enabled_no_users(self):
        """Test when no users are found at the specified level"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.return_value = []
            
            result = ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                next_level="level3",  # Non-existent level
                cms_uuid="test-cms-uuid"
            )
            
            self.assertEqual(result, [])

    def test_send_email_to_act_next_level_sds_success(self):
        """Test successful email sending for SDS approval"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users, \
             patch('approve_flow.approve_flow_email_service.send_approved_approve_flow_sds') as mock_send_email:
            
            mock_get_users.return_value = ["user-uuid-1", "user-uuid-2"]
            mock_send_email.return_value = True
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Verify the result
            self.assertTrue(result)
            
            # Verify email was sent with correct parameters
            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=["user-uuid-1", "user-uuid-2"],
                comment="Test comment",
                sds_type="test-type",
                cms_title="Test CMS"
            )

    def test_send_email_to_act_next_level_sds_no_users(self):
        """Test SDS email sending when no users have email enabled"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.return_value = []
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Should return False when no users to send email to
            self.assertFalse(result)

    def test_send_email_to_act_next_level_sds_exception(self):
        """Test SDS email sending when exception occurs"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_sds(
                kanna_user=self.kanna_user,
                diagnostic_instance=self.diagnostic_instance,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Should return False when exception occurs
            self.assertFalse(result)

    def test_send_email_to_act_next_level_ms_success(self):
        """Test successful email sending for MS approval"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users, \
             patch('approve_flow.approve_flow_email_service.send_approved_approve_flow_ms') as mock_send_email:
            
            mock_get_users.return_value = ["user-uuid-1", "user-uuid-2"]
            mock_send_email.return_value = True
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Verify the result
            self.assertTrue(result)
            
            # Verify email was sent with correct parameters
            mock_send_email.assert_called_once_with(
                company_to="test-act",
                user_approved=self.kanna_user,
                user_to=["user-uuid-1", "user-uuid-2"],
                comment="Test comment",
                cms_title="Test CMS"
            )

    def test_send_email_to_act_next_level_ms_no_users(self):
        """Test MS email sending when no users have email enabled"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.return_value = []
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Should return False when no users to send email to
            self.assertFalse(result)

    def test_send_email_to_act_next_level_ms_exception(self):
        """Test MS email sending when exception occurs"""
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.side_effect = Exception("Database error")
            
            result = ApproveFlowEmailService.send_email_to_act_next_level_ms(
                kanna_user=self.kanna_user,
                management_sheet=self.management_sheet,
                approve_flow_setting_instance=self.approve_flow_setting_instance,
                next_level="level1",
                comment="Test comment",
                cms_title="Test CMS"
            )
            
            # Should return False when exception occurs
            self.assertFalse(result)

    def test_get_act_users_at_level_with_email_enabled_database_query(self):
        """Test that the correct database query is made for ACT users"""
        # Create users with different roles and levels
        role_other = Role.objects.create(id="OTHER_ROLE")
        UserKanna.objects.create(
            user_uuid="other-role-user",
            role=role_other,
            is_active=True,
            level="level1"
        )
        
        UserKanna.objects.create(
            user_uuid="inactive-user",
            role=self.role_act,
            is_active=False,
            level="level1"
        )
        
        with patch('approve_flow.approve_flow_email_service.get_list_user_turn_on_email') as mock_get_users:
            mock_get_users.return_value = ["user-uuid-1", "user-uuid-2"]
            
            ApproveFlowEmailService.get_act_users_at_level_with_email_enabled(
                next_level="level1",
                cms_uuid="test-cms-uuid"
            )
            
            # Verify that get_list_user_turn_on_email was called with the correct user UUIDs
            # Only active ACT users at level1 should be included
            expected_user_uuids = ["user-uuid-1", "user-uuid-2"]
            mock_get_users.assert_called_once_with(
                cms_uuid="test-cms-uuid",
                user_kanna_uuids=expected_user_uuids
            ) 