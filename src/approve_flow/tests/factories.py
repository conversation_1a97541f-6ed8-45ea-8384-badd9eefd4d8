import factory

from approve_flow.models import ApproveFlowSettingInstance, ApproveFlowStep
from authentication.factories import UserFactory
from companies.factories import CompanyFactory


class ApproveFlowSettingInstanceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ApproveFlowSettingInstance

    name = factory.Sequence(lambda n: f"Approve Flow Setting {n}")
    type = "DIAGNOSTIC"
    shop = factory.SubFactory(CompanyFactory, type="SHOP")
    dealer = factory.SubFactory(CompanyFactory, type="DEALER")
    act = factory.SubFactory(CompanyFactory, type="ACT")
    is_approve_level_dealer = True
    is_approve_level_act = True


class ApproveFlowStepFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ApproveFlowStep

    approve_flow_setting_instance = factory.SubFactory(
        ApproveFlowSettingInstanceFactory
    )
    user = factory.SubFactory(UserFactory)
    level = "LEVEL_1"
    status = "OPEN"
    comment = factory.Faker("text", max_nb_chars=100)
    company = factory.SubFactory(CompanyFactory)
    is_admin_approved = False
