# Python Coding Style Guide

## Table of Contents
1. [Code Formatting](#code-formatting)
2. [Naming Conventions](#naming-conventions)
3. [Documentation](#documentation)
4. [Imports](#imports)
5. [Code Organization](#code-organization)
6. [Error Handling](#error-handling)
7. [Testing](#testing)
8. [Performance](#performance)
9. [Security](#security)
10. [Object-Oriented Programming Principles](#object-oriented-programming-principles)

## Code Formatting

### General Rules
- Follow PEP 8 style guide
- Use 4 spaces for indentation (no tabs)
- Maximum line length: 120 characters (JetBrains IDE default)
- Use Black for code formatting
- Use isort for import sorting
- Use flake8 for linting
- All Python files should end with a single empty line (\n)

### Example
```python
# Good
def calculate_flight_price(base_price: float, distance: int, passenger_count: int, discount_rate: float = 0.0) -> float:
    """Calculate total flight price with optional discount."""
    return base_price * distance * passenger_count * (1 - discount_rate)

# Bad
def calculate_flight_price(base_price,distance,passenger_count,discount_rate=0.0):
    return base_price*distance*passenger_count*(1-discount_rate)

```

## Naming Conventions

### Variables and Functions
- Use snake_case for variables and functions
- Use descriptive names
- Avoid single-letter names (except for loop counters)
- Use type hints
- Use underscore prefix (_) for private methods and variables
- Use double underscore prefix (__) for name mangling when needed
- For async functions, use async_ prefix to distinguish them

```python
# Good
def async_fetch_flight_data(flight_id: str) -> Flight:
    pass

class FlightService:
    def __init__(self):
        self._cache = {}  # private instance variable
        self.__secret_key = "key"  # name mangled

    def _validate_flight(self, flight: Flight) -> bool:  # private method
        pass

# Bad
def fetchData(f_id: str) -> Flight:  # no async prefix, camelCase
    pass

class FlightService:
    def __init__(self):
        self.cache = {}  # should be private
        self.secret_key = "key"  # should be name mangled

    def validateFlight(self, f: Flight) -> bool:  # no underscore, camelCase
        pass
```

### Classes
- Use PascalCase for class names
- Use descriptive nouns
- Avoid abbreviations

```python
# Good
class FlightBooking:
    pass

class PaymentProcessor:
    pass

# Bad
class booking:
    pass

class PP:
    pass
```

### Constants
- Use UPPER_SNAKE_CASE for constants
- Define at module level

```python
# Good
MAX_PASSENGERS = 4
DEFAULT_CURRENCY = "USD"

# Bad
maxPassengers = 4
default_currency = "USD"
```

## Documentation

### Docstrings
- Use Google style docstrings
- Document all public functions, classes, and methods
- Include type hints
- Document exceptions
- Write class-level docstrings
- Document complex private methods
- Avoid redundant docstrings that just repeat the function name
- Focus on the "why" not just the "what"

```python
class FlightBooking:
    """Handles the flight booking process and related operations.
    
    This class manages the entire booking workflow including validation,
    payment processing, and confirmation. It ensures atomicity of the
    booking process and handles all related edge cases.
    
    Attributes:
        max_passengers: Maximum number of passengers allowed per booking
        min_passengers: Minimum number of passengers required for booking
    """

    def __init__(self, flight_id: str):
        self.flight_id = flight_id

    def book(self, passenger_count: int) -> Booking:
        """Book a flight for the specified number of passengers.
        
        Args:
            passenger_count: Number of passengers to book
            
        Returns:
            Booking object containing booking details
            
        Raises:
            ValidationError: If passenger count is invalid
            FlightNotFoundError: If flight doesn't exist
            PaymentError: If payment processing fails
        """
        pass

    def _validate_passenger_count(self, count: int) -> bool:
        """Validate passenger count against business rules.
        
        This method implements complex validation logic including:
        - Checking against airline policies
        - Validating group booking rules
        - Verifying seat availability
        
        Args:
            count: Number of passengers to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        pass
```

### Comments
- Write comments for complex logic
- Keep comments up to date
- Use TODO comments for future improvements

```python
# Calculate price with seasonal adjustments
# TODO: Add holiday season multiplier
def calculate_price(base_price: float, season: str) -> float:
    pass
```

## Imports

### Import Rules
- Avoid using `import *`
- Use absolute imports
- Group imports in the following order:
  1. Standard library imports
  2. Third-party imports
  3. Local application imports
- Use clear aliases for long module names
- Avoid ambiguous aliases

```python
# Good
import os
from typing import List, Optional, Dict

import numpy as np
import pandas as pd
from django.db import models

from common.models import BaseModel
from flights.models import Flight

# Bad
from django.db import models as m  # ambiguous alias
from .models import *  # avoid wildcard imports
import os, sys  # multiple imports on one line
```

## Code Organization

### File Structure
```text
app_name/
├── __init__.py
├── admin.py
├── apps.py
├── consts.py
├── urls.py
├── models/
│   ├── __init__.py
│   ├── base.py
│   ├── flight.py
│   ├── booking.py
│   ├── payment.py
│   └── point_event.py
├── serializers/
│   ├── __init__.py
│   ├── flight.py
│   ├── booking.py
│   └── payment.py
├── views/
│   ├── __init__.py
│   ├── flight.py
│   ├── booking.py
│   └── payment.py
├── services/
│   ├── __init__.py
│   ├── flight_service.py
│   ├── booking_service.py
│   ├── payment_service.py
│   └── notification_service.py
├── permissions/
│   ├── __init__.py
│   ├── flight.py
│   └── booking.py
├── helpers/
│   ├── __init__.py
│   ├── validators.py
│   └── utils.py
├── management/
│   └── commands/
│       ├── __init__.py
│       └── custom_command.py
├── migrations/
│   ├── __init__.py
│   └── ...
├── templates/
│   └── app_name/
│       ├── email/
│       └── notifications/
├── lambda/
│   └── functions/
└── tests/
    ├── __init__.py
    ├── models/
    │   ├── __init__.py
    │   ├── test_flight.py
    │   ├── test_booking.py
    │   └── test_payment.py
    ├── serializers/
    │   ├── __init__.py
    │   ├── test_flight.py
    │   ├── test_booking.py
    │   └── test_payment.py
    ├── services/
    │   ├── __init__.py
    │   ├── test_flight.py
    │   ├── test_booking.py
    │   └── test_payment.py
    └── views/
        ├── __init__.py
        ├── test_flight.py
        ├── test_booking.py
        └── test_payment.py
```

### Directory Structure Details

#### Core Files
- `__init__.py`: Package initialization
- `admin.py`: Django admin configurations
- `apps.py`: Django app configuration
- `consts.py`: Constants and configuration values
- `urls.py`: URL routing configurations

#### Models Directory
- Separate files for each model
- `base.py` for common model functionality
- Each model file should contain related models
- Use `__init__.py` to expose models

#### Services Directory
- Business logic implementation
- One service class per file
- Clear separation of concerns
- Use dependency injection
- Handle external API calls

#### Views Directory
- API endpoints and view logic
- Group related views in files
- Use class-based views
- Keep views thin, delegate to services

#### Serializers Directory
- Data serialization/deserialization
- Validation logic
- Nested serializers when needed
- Clear field definitions

#### Permissions Directory
- Custom permission classes
- Role-based access control
- Business rule validations

#### Helpers Directory
- Utility functions
- Common validators
- Shared helper methods
- No business logic

#### Management Commands
- Custom Django management commands
- Data migration scripts
- Maintenance tasks

#### Templates Directory
- Email templates
- Notification templates
- HTML templates if needed

#### Lambda Directory
- AWS Lambda functions
- Serverless components
- Background tasks

#### Tests Directory
- Mirror the app structure
- Separate test files for each component
- Use fixtures and factories
- Follow AAA pattern (Arrange-Act-Assert)

### Example Service Structure
```python
# services/flight_service.py
from typing import List, Optional
from django.db import transaction

from ..models.flight import Flight
from ..models.booking import Booking
from ..helpers.validators import validate_flight_availability

class FlightService:
    def __init__(self, notification_service=None):
        self.notification_service = notification_service

    @transaction.atomic
    def book_flight(self, flight_id: str, passenger_count: int) -> Booking:
        """Book a flight with transaction safety."""
        flight = Flight.objects.select_for_update().get(id=flight_id)
        validate_flight_availability(flight, passenger_count)
        
        booking = Booking.objects.create(
            flight=flight,
            passenger_count=passenger_count
        )
        
        if self.notification_service:
            self.notification_service.send_booking_confirmation(booking)
            
        return booking
```

### Example Model Structure
```python
# models/flight.py
from django.db import models
from django.utils import timezone

from .base import BaseModel

class Flight(BaseModel):
    flight_number = models.CharField(max_length=10, unique=True)
    departure_time = models.DateTimeField()
    arrival_time = models.DateTimeField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    seats_remaining = models.IntegerField(default=0)
    
    class Meta:
        indexes = [
            models.Index(fields=['departure_time']),
            models.Index(fields=['arrival_time']),
        ]
    
    @property
    def is_available(self) -> bool:
        return self.seats_remaining > 0 and self.departure_time > timezone.now()
    
    def book_seats(self, count: int) -> None:
        if count > self.seats_remaining:
            raise ValueError("Not enough seats available")
        self.seats_remaining -= count
        self.save()
```

## Error Handling

### Exception Handling Rules
- Catch specific exceptions
- Avoid catching bare `Exception` unless logging and re-raising
- Never use bare `except:`
- Log exceptions with appropriate context
- Clean up resources in finally blocks

```python
# Good
def process_payment(amount: float) -> bool:
    try:
        return payment_gateway.process(amount)
    except PaymentGatewayError as e:
        logger.error(f"Payment processing failed: {e}")
        raise
    except ConnectionError as e:
        logger.error(f"Connection error: {e}")
        raise PaymentGatewayError("Service unavailable") from e
    finally:
        cleanup_resources()

# Bad
def process_payment(amount: float) -> bool:
    try:
        return payment_gateway.process(amount)
    except:  # bare except
        return False
    except Exception as e:  # too broad
        logger.error(e)
        return False
```

## Testing

### Test Structure
```python
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

class TestFlightBooking(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.flight = FlightFactory()
        self.url = reverse("flight-booking", args=[self.flight.id])

    def test_create_booking(self):
        # Arrange
        data = {
            "passengers": [PassengerFactory.build().dict()],
            "payment_method": "credit_card"
        }

        # Act
        response = self.client.post(self.url, data, format='json')

        # Assert
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["status"], "confirmed")
```

### Test Naming
```python
# Good
def test_booking_fails_when_flight_full(self):
    pass

def test_booking_succeeds_with_valid_payment(self):
    pass

# Bad
def test1(self):
    pass

def test_booking(self):
    pass
```

## Performance

### Database Queries
```python
# Good
flights = Flight.objects.select_related(
    'airline'
).prefetch_related(
    'bookings'
).filter(
    departure_date=date
)

# Bad
flights = Flight.objects.filter(departure_date=date)
for flight in flights:
    airline = flight.airline  # N+1 query
```

### Caching
```python
# Good
@cache_page(60 * 15)  # Cache for 15 minutes
def get_flight_schedule(request):
    pass

# Bad
def get_flight_schedule(request):
    # No caching
    pass
```

## Security

### Security Guidelines
- Never log sensitive information (passwords, tokens, etc.)
- Use prepared statements/ORM to prevent SQL injection
- Validate all input data
- Escape output data when rendering HTML
- Use secure password hashing
- Implement proper authentication and authorization
- Use HTTPS for all external communications
- Implement rate limiting for APIs
- Sanitize file uploads
- Use environment variables for sensitive configuration

```python
# Good
def process_user_data(user_data: Dict[str, Any]) -> None:
    # Validate input
    if not is_valid_user_data(user_data):
        raise ValidationError("Invalid user data")
        
    # Use ORM to prevent SQL injection
    user = User.objects.create(
        username=user_data['username'],
        email=user_data['email']
    )
    
    # Hash password securely
    user.set_password(user_data['password'])
    user.save()
    
    # Log safely
    logger.info(f"Created user {user.id}")  # Don't log sensitive data

# Bad
def process_user_data(user_data: Dict[str, Any]) -> None:
    # Raw SQL - vulnerable to injection
    cursor.execute(f"INSERT INTO users VALUES ('{user_data['username']}', '{user_data['password']}')")
    
    # Logging sensitive data
    logger.info(f"Created user with password: {user_data['password']}")
```

## Object-Oriented Programming Principles

### OOP Guidelines
- Use inheritance when you need to reuse logic
- Prefer composition over inheritance when appropriate
- Mark internal API methods with `_` prefix
- Use abstract base classes for common interfaces
- Keep classes focused and under 200 lines
- Avoid overengineering
- Use dependency injection
- Follow SOLID principles

```python
from abc import ABC, abstractmethod

class PaymentMethod(ABC):
    """Abstract base class for payment methods."""
    
    @abstractmethod
    def process_payment(self, amount: float) -> bool:
        pass

class CreditCardPayment(PaymentMethod):
    """Credit card payment implementation."""
    
    def __init__(self, card_number: str, expiry: str):
        self._card_number = card_number
        self._expiry = expiry
    
    def process_payment(self, amount: float) -> bool:
        return self._process_credit_card(amount)
    
    def _process_credit_card(self, amount: float) -> bool:
        # Implementation
        pass

# Using composition
class PaymentProcessor:
    def __init__(self, payment_method: PaymentMethod):
        self._payment_method = payment_method
    
    def process(self, amount: float) -> bool:
        return self._payment_method.process_payment(amount)
```

## Best Practices

### Code Review Checklist
- [ ] Follows PEP 8 style guide
- [ ] Includes type hints
- [ ] Has proper documentation
- [ ] Includes tests
- [ ] Handles errors appropriately
- [ ] Uses proper security measures
- [ ] Follows performance best practices
- [ ] No hardcoded values
- [ ] No commented-out code
- [ ] No debug print statements

### Common Pitfalls to Avoid
1. Using mutable default arguments
```python
# Bad
def add_passenger(passengers=[]):
    passengers.append("new")
    return passengers

# Good
def add_passenger(passengers=None):
    if passengers is None:
        passengers = []
    passengers.append("new")
    return passengers
```

2. Not using context managers
```python
# Bad
file = open('data.txt')
try:
    data = file.read()
finally:
    file.close()

# Good
with open('data.txt') as file:
    data = file.read()
```

3. Not using list comprehensions appropriately
```python
# Bad
squares = []
for x in range(10):
    squares.append(x**2)

# Good
squares = [x**2 for x in range(10)]
```

## Tools and Configuration

### Required Tools
- Black (code formatter)
- isort (import sorter)
- flake8 (linter)
- mypy (type checker)
- Django Test Framework (testing)

### Configuration Files
```ini
# setup.cfg
[flake8]
max-line-length = 120
extend-ignore = E203

[isort]
profile = black
multi_line_output = 3
```

```toml
# pyproject.toml
[tool.black]
line-length = 120
target-version = ['py37']
include = '\.pyi?$'
```
