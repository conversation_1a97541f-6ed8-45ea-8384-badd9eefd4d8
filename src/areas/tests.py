import re

from rest_framework.test import APITestCase

from django.urls import reverse

from .factories import AreaFactory


class AreaAPITests(APITestCase):
    def test_get_area_list(self):
        AreaFactory.create_batch(3)
        url = reverse("areas", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(len(data["results"]), 3)
