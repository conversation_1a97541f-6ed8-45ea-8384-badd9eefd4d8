# Generated by Django 4.1 on 2022-09-20 08:13

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentication", "0017_remove_city_area_remove_shop_city_remove_shop_dealer_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Area",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                "db_table": "area",
            },
        ),
    ]
